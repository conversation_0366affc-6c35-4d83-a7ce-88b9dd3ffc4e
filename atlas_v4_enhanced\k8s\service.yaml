apiVersion: v1
kind: Service
metadata:
  name: atlas-service
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: service
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8001
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 8000
    targetPort: metrics
    protocol: TCP
  - name: nginx
    port: 80
    targetPort: nginx
    protocol: TCP
  selector:
    app: atlas-multi-agent-system
    component: api
---
apiVersion: v1
kind: Service
metadata:
  name: atlas-headless-service
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8001
    targetPort: http
    protocol: TCP
  selector:
    app: atlas-multi-agent-system
    component: api
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: atlas-ingress
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - atlas-api.yourdomain.com
    secretName: atlas-tls-cert
  rules:
  - host: atlas-api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: atlas-service
            port:
              number: 80
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: atlas-network-policy
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: security
spec:
  podSelector:
    matchLabels:
      app: atlas-multi-agent-system
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: atlas-multi-agent-system
    ports:
    - protocol: TCP
      port: 8001
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 80
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to:
    - podSelector:
        matchLabels:
          app: atlas-multi-agent-system
    ports:
    - protocol: TCP
      port: 8001
    - protocol: TCP
      port: 8000
