"""
A.T.L.A.S. Comprehensive Benchmarking Suite
Advanced benchmarking and performance analysis for production readiness
"""

import asyncio
import logging
import time
import json
import statistics
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# Core imports
from atlas_multi_agent_orchestrator import (
    AtlasMultiAgentOrchestrator, OrchestrationRequest, 
    IntentType, OrchestrationMode, TaskPriority
)
from performance_validation import PerformanceValidator

logger = logging.getLogger(__name__)

@dataclass
class BenchmarkResult:
    """Comprehensive benchmark result"""
    test_name: str
    timestamp: datetime
    duration: float
    requests_completed: int
    requests_failed: int
    success_rate: float
    avg_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    cpu_usage_avg: float
    memory_usage_avg: float
    memory_peak: float
    errors: List[str]

class AtlasBenchmarkSuite:
    """Comprehensive benchmarking suite for A.T.L.A.S."""
    
    def __init__(self):
        self.orchestrator = None
        self.results: List[BenchmarkResult] = []
        self.system_metrics = []
        
        # Benchmark configurations
        self.benchmark_configs = {
            "smoke_test": {
                "concurrent_users": 1,
                "requests_per_user": 5,
                "duration": 30,
                "description": "Basic functionality test"
            },
            "light_load": {
                "concurrent_users": 5,
                "requests_per_user": 10,
                "duration": 60,
                "description": "Light load test"
            },
            "moderate_load": {
                "concurrent_users": 20,
                "requests_per_user": 15,
                "duration": 120,
                "description": "Moderate load test"
            },
            "heavy_load": {
                "concurrent_users": 50,
                "requests_per_user": 10,
                "duration": 180,
                "description": "Heavy load test"
            },
            "stress_test": {
                "concurrent_users": 100,
                "requests_per_user": 5,
                "duration": 300,
                "description": "Stress test - maximum capacity"
            },
            "endurance_test": {
                "concurrent_users": 30,
                "requests_per_user": 50,
                "duration": 600,
                "description": "Endurance test - sustained load"
            }
        }
        
        logger.info("A.T.L.A.S. Benchmark Suite initialized")
    
    async def initialize(self):
        """Initialize the benchmark suite"""
        logger.info("Initializing benchmark suite...")
        
        self.orchestrator = AtlasMultiAgentOrchestrator()
        success = await self.orchestrator.initialize()
        
        if not success:
            raise Exception("Failed to initialize orchestrator")
        
        logger.info("Benchmark suite ready")
        return True
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive benchmark suite"""
        logger.info("🚀 Starting Comprehensive Benchmark Suite")
        
        benchmark_results = {
            "timestamp": datetime.now().isoformat(),
            "system_info": self._get_system_info(),
            "benchmark_results": {},
            "performance_analysis": {},
            "recommendations": [],
            "summary": {}
        }
        
        try:
            # Run all benchmark configurations
            for config_name, config in self.benchmark_configs.items():
                logger.info(f"🔥 Running {config_name}: {config['description']}")
                
                result = await self._run_benchmark(config_name, config)
                benchmark_results["benchmark_results"][config_name] = asdict(result)
                self.results.append(result)
                
                # Brief pause between tests
                await asyncio.sleep(10)
                
                # Force garbage collection
                gc.collect()
            
            # Analyze results
            benchmark_results["performance_analysis"] = self._analyze_performance()
            benchmark_results["recommendations"] = self._generate_benchmark_recommendations()
            benchmark_results["summary"] = self._generate_summary()
            
            # Save detailed results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"benchmark_results_{timestamp}.json"
            
            with open(results_file, 'w') as f:
                json.dump(benchmark_results, f, indent=2, default=str)
            
            logger.info(f"📄 Benchmark results saved to: {results_file}")
            
            # Generate performance charts
            await self._generate_performance_charts(timestamp)
            
            return benchmark_results
            
        except Exception as e:
            logger.error(f"Benchmark suite failed: {e}")
            benchmark_results["error"] = str(e)
            return benchmark_results
    
    async def _run_benchmark(self, test_name: str, config: Dict[str, Any]) -> BenchmarkResult:
        """Run a single benchmark test"""
        logger.info(f"Starting {test_name} benchmark...")
        
        concurrent_users = config["concurrent_users"]
        requests_per_user = config["requests_per_user"]
        duration = config["duration"]
        
        # Initialize metrics tracking
        response_times = []
        successful_requests = 0
        failed_requests = 0
        errors = []
        cpu_readings = []
        memory_readings = []
        
        start_time = time.time()
        
        # Start system monitoring
        monitor_task = asyncio.create_task(
            self._monitor_system_resources(cpu_readings, memory_readings, duration)
        )
        
        try:
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(concurrent_users)
            
            async def user_session(user_id: int):
                async with semaphore:
                    session_start = time.time()
                    user_requests = 0
                    user_successes = 0
                    user_failures = 0
                    
                    while (time.time() - session_start) < duration and user_requests < requests_per_user:
                        try:
                            # Create test request
                            symbol = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"][user_requests % 5]
                            intent = [IntentType.PATTERN_DETECTION, IntentType.COMPREHENSIVE_ANALYSIS][user_requests % 2]
                            mode = [OrchestrationMode.PARALLEL, OrchestrationMode.HYBRID][user_requests % 2]
                            
                            request = OrchestrationRequest(
                                request_id=f"benchmark_{test_name}_{user_id}_{user_requests}",
                                intent=intent,
                                symbol=symbol,
                                input_data={"benchmark_test": True, "user_id": user_id},
                                orchestration_mode=mode,
                                priority=TaskPriority.MEDIUM,
                                timeout_seconds=30
                            )
                            
                            # Execute request
                            req_start = time.time()
                            result = await self.orchestrator.process_request(request)
                            req_end = time.time()
                            
                            response_time = req_end - req_start
                            response_times.append(response_time)
                            
                            if result and result.success:
                                user_successes += 1
                            else:
                                user_failures += 1
                                if result and hasattr(result, 'error'):
                                    errors.append(f"User {user_id}: {result.error}")
                            
                            user_requests += 1
                            
                            # Small delay to prevent overwhelming
                            await asyncio.sleep(0.05)
                            
                        except Exception as e:
                            user_failures += 1
                            errors.append(f"User {user_id}: {str(e)}")
                            logger.error(f"Request failed for user {user_id}: {e}")
                    
                    return user_successes, user_failures
            
            # Run all user sessions
            tasks = [user_session(user_id) for user_id in range(concurrent_users)]
            session_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process session results
            for result in session_results:
                if isinstance(result, Exception):
                    failed_requests += requests_per_user
                    errors.append(f"Session failed: {result}")
                else:
                    successes, failures = result
                    successful_requests += successes
                    failed_requests += failures
            
        finally:
            # Stop monitoring
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Calculate metrics
        total_requests = successful_requests + failed_requests
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        requests_per_second = total_requests / total_duration if total_duration > 0 else 0
        
        # Response time statistics
        if response_times:
            avg_response_time = statistics.mean(response_times)
            p50_response_time = np.percentile(response_times, 50)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = p50_response_time = p95_response_time = p99_response_time = 0
            min_response_time = max_response_time = 0
        
        # System resource statistics
        cpu_avg = statistics.mean(cpu_readings) if cpu_readings else 0
        memory_avg = statistics.mean(memory_readings) if memory_readings else 0
        memory_peak = max(memory_readings) if memory_readings else 0
        
        return BenchmarkResult(
            test_name=test_name,
            timestamp=datetime.now(),
            duration=total_duration,
            requests_completed=successful_requests,
            requests_failed=failed_requests,
            success_rate=success_rate,
            avg_response_time=avg_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            cpu_usage_avg=cpu_avg,
            memory_usage_avg=memory_avg,
            memory_peak=memory_peak,
            errors=errors[:10]  # Keep only first 10 errors
        )
    
    async def _monitor_system_resources(self, cpu_readings: List[float], 
                                      memory_readings: List[float], duration: float):
        """Monitor system resources during benchmark"""
        start_time = time.time()
        
        while (time.time() - start_time) < duration:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_info = psutil.virtual_memory()
                memory_mb = memory_info.used / (1024 * 1024)
                
                cpu_readings.append(cpu_percent)
                memory_readings.append(memory_mb)
                
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"System monitoring error: {e}")
                break
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "cpu_count_logical": psutil.cpu_count(logical=True),
                "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                "disk_total_gb": psutil.disk_usage('/').total / (1024**3),
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "platform": sys.platform
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {"error": str(e)}
    
    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze benchmark performance"""
        if not self.results:
            return {"error": "No results to analyze"}
        
        analysis = {
            "response_time_analysis": {},
            "throughput_analysis": {},
            "scalability_analysis": {},
            "resource_usage_analysis": {},
            "reliability_analysis": {}
        }
        
        # Response time analysis
        response_times = [r.avg_response_time for r in self.results]
        p95_times = [r.p95_response_time for r in self.results]
        
        analysis["response_time_analysis"] = {
            "avg_response_time_overall": statistics.mean(response_times),
            "avg_p95_response_time": statistics.mean(p95_times),
            "response_time_stability": statistics.stdev(response_times) if len(response_times) > 1 else 0,
            "meets_10s_target": all(rt <= 10.0 for rt in p95_times)
        }
        
        # Throughput analysis
        throughputs = [r.requests_per_second for r in self.results]
        analysis["throughput_analysis"] = {
            "max_throughput": max(throughputs),
            "avg_throughput": statistics.mean(throughputs),
            "throughput_degradation": (max(throughputs) - min(throughputs)) / max(throughputs) if throughputs else 0
        }
        
        # Scalability analysis
        user_counts = [self.benchmark_configs[r.test_name]["concurrent_users"] for r in self.results]
        analysis["scalability_analysis"] = {
            "max_concurrent_users_tested": max(user_counts),
            "scalability_coefficient": self._calculate_scalability_coefficient(),
            "linear_scalability": self._check_linear_scalability()
        }
        
        # Resource usage analysis
        cpu_usages = [r.cpu_usage_avg for r in self.results]
        memory_usages = [r.memory_usage_avg for r in self.results]
        
        analysis["resource_usage_analysis"] = {
            "max_cpu_usage": max(cpu_usages),
            "avg_cpu_usage": statistics.mean(cpu_usages),
            "max_memory_usage_mb": max(memory_usages),
            "avg_memory_usage_mb": statistics.mean(memory_usages),
            "resource_efficiency": self._calculate_resource_efficiency()
        }
        
        # Reliability analysis
        success_rates = [r.success_rate for r in self.results]
        analysis["reliability_analysis"] = {
            "overall_success_rate": statistics.mean(success_rates),
            "min_success_rate": min(success_rates),
            "reliability_under_load": min(success_rates) >= 0.95,
            "total_errors": sum(len(r.errors) for r in self.results)
        }
        
        return analysis

    def _calculate_scalability_coefficient(self) -> float:
        """Calculate scalability coefficient"""
        try:
            if len(self.results) < 2:
                return 1.0

            # Sort results by concurrent users
            sorted_results = sorted(
                self.results,
                key=lambda r: self.benchmark_configs[r.test_name]["concurrent_users"]
            )

            # Calculate throughput per user for each test
            throughput_per_user = []
            for result in sorted_results:
                users = self.benchmark_configs[result.test_name]["concurrent_users"]
                throughput_per_user.append(result.requests_per_second / users)

            # Ideal scalability would maintain constant throughput per user
            if len(throughput_per_user) > 1:
                return min(throughput_per_user) / max(throughput_per_user)

            return 1.0

        except Exception as e:
            logger.error(f"Failed to calculate scalability coefficient: {e}")
            return 0.0

    def _check_linear_scalability(self) -> bool:
        """Check if system scales linearly"""
        try:
            if len(self.results) < 3:
                return True  # Not enough data

            # Sort by user count
            sorted_results = sorted(
                self.results,
                key=lambda r: self.benchmark_configs[r.test_name]["concurrent_users"]
            )

            # Check if throughput increases reasonably with user count
            user_counts = [self.benchmark_configs[r.test_name]["concurrent_users"] for r in sorted_results]
            throughputs = [r.requests_per_second for r in sorted_results]

            # Calculate correlation coefficient
            if len(user_counts) > 2:
                correlation = np.corrcoef(user_counts, throughputs)[0, 1]
                return correlation > 0.7  # Strong positive correlation

            return True

        except Exception as e:
            logger.error(f"Failed to check linear scalability: {e}")
            return False

    def _calculate_resource_efficiency(self) -> float:
        """Calculate resource efficiency score"""
        try:
            if not self.results:
                return 0.0

            # Calculate efficiency as throughput per CPU percentage
            efficiencies = []
            for result in self.results:
                if result.cpu_usage_avg > 0:
                    efficiency = result.requests_per_second / result.cpu_usage_avg
                    efficiencies.append(efficiency)

            return statistics.mean(efficiencies) if efficiencies else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate resource efficiency: {e}")
            return 0.0

    def _generate_benchmark_recommendations(self) -> List[str]:
        """Generate recommendations based on benchmark results"""
        recommendations = []

        try:
            if not self.results:
                return ["No benchmark results available for analysis"]

            # Analyze response times
            p95_times = [r.p95_response_time for r in self.results]
            avg_p95 = statistics.mean(p95_times)

            if avg_p95 > 10.0:
                recommendations.append(
                    f"Response time optimization needed. Average P95 is {avg_p95:.2f}s, "
                    "target is <10s. Consider optimizing agent algorithms or increasing resources."
                )
            elif avg_p95 > 5.0:
                recommendations.append(
                    f"Response times are acceptable but could be improved. "
                    f"Average P95 is {avg_p95:.2f}s."
                )

            # Analyze success rates
            success_rates = [r.success_rate for r in self.results]
            min_success_rate = min(success_rates)

            if min_success_rate < 0.95:
                recommendations.append(
                    f"Reliability issues detected. Minimum success rate is {min_success_rate:.1%}, "
                    "target is >95%. Review error handling and timeout configurations."
                )

            # Analyze scalability
            scalability_coeff = self._calculate_scalability_coefficient()
            if scalability_coeff < 0.8:
                recommendations.append(
                    f"Scalability issues detected. Scalability coefficient is {scalability_coeff:.2f}. "
                    "Consider optimizing for concurrent processing or implementing connection pooling."
                )

            # Analyze resource usage
            cpu_usages = [r.cpu_usage_avg for r in self.results]
            max_cpu = max(cpu_usages)

            if max_cpu > 90:
                recommendations.append(
                    f"High CPU usage detected ({max_cpu:.1f}%). "
                    "Consider horizontal scaling or CPU optimization."
                )
            elif max_cpu > 80:
                recommendations.append(
                    f"CPU usage is getting high ({max_cpu:.1f}%). "
                    "Monitor for potential bottlenecks."
                )

            # Memory analysis
            memory_usages = [r.memory_usage_avg for r in self.results]
            max_memory_gb = max(memory_usages) / 1024

            if max_memory_gb > 6:
                recommendations.append(
                    f"High memory usage detected ({max_memory_gb:.1f}GB). "
                    "Check for memory leaks or consider increasing available memory."
                )

            # Throughput analysis
            throughputs = [r.requests_per_second for r in self.results]
            max_throughput = max(throughputs)

            if max_throughput < 10:
                recommendations.append(
                    f"Low throughput detected ({max_throughput:.1f} req/s). "
                    "Consider optimizing request processing or increasing parallelism."
                )

            # Overall assessment
            if not recommendations:
                recommendations.append(
                    "Excellent performance! System meets all performance targets and is ready for production."
                )

        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error.")

        return recommendations

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate benchmark summary"""
        if not self.results:
            return {"error": "No results to summarize"}

        # Calculate overall metrics
        success_rates = [r.success_rate for r in self.results]
        response_times = [r.p95_response_time for r in self.results]
        throughputs = [r.requests_per_second for r in self.results]
        cpu_usages = [r.cpu_usage_avg for r in self.results]

        # Performance targets
        targets_met = {
            "response_time_target": all(rt <= 10.0 for rt in response_times),
            "success_rate_target": all(sr >= 0.95 for sr in success_rates),
            "throughput_target": max(throughputs) >= 10.0,
            "concurrent_users_target": any(
                self.benchmark_configs[r.test_name]["concurrent_users"] >= 100
                and r.success_rate >= 0.95
                for r in self.results
            )
        }

        # Overall score calculation
        score_components = []

        # Response time score (25%)
        avg_response_time = statistics.mean(response_times)
        response_score = max(0, 100 - (avg_response_time / 10.0) * 100)
        score_components.append(("response_time", response_score, 0.25))

        # Success rate score (30%)
        avg_success_rate = statistics.mean(success_rates)
        success_score = avg_success_rate * 100
        score_components.append(("success_rate", success_score, 0.30))

        # Throughput score (25%)
        max_throughput = max(throughputs)
        throughput_score = min(100, (max_throughput / 50.0) * 100)  # 50 req/s = 100%
        score_components.append(("throughput", throughput_score, 0.25))

        # Resource efficiency score (20%)
        avg_cpu = statistics.mean(cpu_usages)
        efficiency_score = max(0, 100 - avg_cpu)  # Lower CPU usage = higher score
        score_components.append(("efficiency", efficiency_score, 0.20))

        # Calculate weighted overall score
        overall_score = sum(score * weight for _, score, weight in score_components)

        return {
            "overall_score": round(overall_score, 1),
            "targets_met": targets_met,
            "targets_met_count": sum(targets_met.values()),
            "total_targets": len(targets_met),
            "performance_grade": self._get_performance_grade(overall_score),
            "key_metrics": {
                "max_throughput": max(throughputs),
                "avg_response_time": statistics.mean(response_times),
                "min_success_rate": min(success_rates),
                "max_concurrent_users_tested": max(
                    self.benchmark_configs[r.test_name]["concurrent_users"]
                    for r in self.results
                ),
                "total_requests_processed": sum(r.requests_completed for r in self.results),
                "total_test_duration": sum(r.duration for r in self.results)
            },
            "production_readiness": self._assess_production_readiness(targets_met, overall_score)
        }

    def _get_performance_grade(self, score: float) -> str:
        """Get performance grade based on score"""
        if score >= 90:
            return "A+ (Excellent)"
        elif score >= 80:
            return "A (Very Good)"
        elif score >= 70:
            return "B (Good)"
        elif score >= 60:
            return "C (Acceptable)"
        elif score >= 50:
            return "D (Needs Improvement)"
        else:
            return "F (Poor)"

    def _assess_production_readiness(self, targets_met: Dict[str, bool], overall_score: float) -> Dict[str, Any]:
        """Assess production readiness"""
        targets_met_count = sum(targets_met.values())
        total_targets = len(targets_met)

        if targets_met_count == total_targets and overall_score >= 80:
            readiness = "READY"
            confidence = "HIGH"
            message = "System meets all performance targets and is ready for production deployment."
        elif targets_met_count >= total_targets * 0.75 and overall_score >= 70:
            readiness = "MOSTLY_READY"
            confidence = "MEDIUM"
            message = "System meets most performance targets. Minor optimizations recommended before production."
        elif targets_met_count >= total_targets * 0.5 and overall_score >= 60:
            readiness = "NEEDS_WORK"
            confidence = "LOW"
            message = "System needs significant optimization before production deployment."
        else:
            readiness = "NOT_READY"
            confidence = "VERY_LOW"
            message = "System is not ready for production. Major performance issues need to be addressed."

        return {
            "status": readiness,
            "confidence": confidence,
            "message": message,
            "targets_met_percentage": (targets_met_count / total_targets) * 100,
            "overall_score": overall_score
        }

    async def _generate_performance_charts(self, timestamp: str):
        """Generate performance visualization charts"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns

            # Set style
            plt.style.use('seaborn-v0_8')
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('A.T.L.A.S. Performance Benchmark Results', fontsize=16)

            # Chart 1: Response Time vs Concurrent Users
            user_counts = [self.benchmark_configs[r.test_name]["concurrent_users"] for r in self.results]
            response_times = [r.p95_response_time for r in self.results]

            axes[0, 0].scatter(user_counts, response_times, alpha=0.7, s=100)
            axes[0, 0].plot(user_counts, response_times, 'b--', alpha=0.5)
            axes[0, 0].axhline(y=10, color='r', linestyle='--', label='10s Target')
            axes[0, 0].set_xlabel('Concurrent Users')
            axes[0, 0].set_ylabel('P95 Response Time (s)')
            axes[0, 0].set_title('Response Time vs Load')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Chart 2: Throughput vs Concurrent Users
            throughputs = [r.requests_per_second for r in self.results]

            axes[0, 1].scatter(user_counts, throughputs, alpha=0.7, s=100, color='green')
            axes[0, 1].plot(user_counts, throughputs, 'g--', alpha=0.5)
            axes[0, 1].set_xlabel('Concurrent Users')
            axes[0, 1].set_ylabel('Requests per Second')
            axes[0, 1].set_title('Throughput vs Load')
            axes[0, 1].grid(True, alpha=0.3)

            # Chart 3: Success Rate vs Load
            success_rates = [r.success_rate * 100 for r in self.results]

            axes[1, 0].scatter(user_counts, success_rates, alpha=0.7, s=100, color='orange')
            axes[1, 0].plot(user_counts, success_rates, 'orange', linestyle='--', alpha=0.5)
            axes[1, 0].axhline(y=95, color='r', linestyle='--', label='95% Target')
            axes[1, 0].set_xlabel('Concurrent Users')
            axes[1, 0].set_ylabel('Success Rate (%)')
            axes[1, 0].set_title('Success Rate vs Load')
            axes[1, 0].set_ylim(80, 105)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # Chart 4: Resource Usage
            cpu_usages = [r.cpu_usage_avg for r in self.results]
            memory_usages = [r.memory_usage_avg / 1024 for r in self.results]  # Convert to GB

            ax4_twin = axes[1, 1].twinx()

            line1 = axes[1, 1].plot(user_counts, cpu_usages, 'b-o', label='CPU Usage (%)', alpha=0.7)
            line2 = ax4_twin.plot(user_counts, memory_usages, 'r-s', label='Memory Usage (GB)', alpha=0.7)

            axes[1, 1].set_xlabel('Concurrent Users')
            axes[1, 1].set_ylabel('CPU Usage (%)', color='b')
            ax4_twin.set_ylabel('Memory Usage (GB)', color='r')
            axes[1, 1].set_title('Resource Usage vs Load')
            axes[1, 1].grid(True, alpha=0.3)

            # Combine legends
            lines1, labels1 = axes[1, 1].get_legend_handles_labels()
            lines2, labels2 = ax4_twin.get_legend_handles_labels()
            axes[1, 1].legend(lines1 + lines2, labels1 + labels2, loc='upper left')

            plt.tight_layout()

            # Save chart
            chart_file = f"benchmark_charts_{timestamp}.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"📊 Performance charts saved to: {chart_file}")

        except Exception as e:
            logger.error(f"Failed to generate performance charts: {e}")

# ============================================================================
# MAIN BENCHMARK RUNNER
# ============================================================================

async def run_benchmark_suite():
    """Main function to run the benchmark suite"""
    logger.info("🚀 Starting A.T.L.A.S. Benchmark Suite")

    try:
        # Initialize benchmark suite
        suite = AtlasBenchmarkSuite()
        await suite.initialize()

        # Run comprehensive benchmark
        results = await suite.run_comprehensive_benchmark()

        # Print summary
        print("\n" + "="*80)
        print("🎯 BENCHMARK SUITE SUMMARY")
        print("="*80)

        summary = results.get("summary", {})
        if summary:
            print(f"Overall Score: {summary['overall_score']}/100")
            print(f"Performance Grade: {summary['performance_grade']}")
            print(f"Targets Met: {summary['targets_met_count']}/{summary['total_targets']}")

            readiness = summary.get("production_readiness", {})
            print(f"Production Readiness: {readiness.get('status', 'UNKNOWN')}")
            print(f"Confidence: {readiness.get('confidence', 'UNKNOWN')}")
            print(f"Message: {readiness.get('message', 'No message')}")

        print("\n💡 Recommendations:")
        for i, rec in enumerate(results.get("recommendations", []), 1):
            print(f"  {i}. {rec}")

        print("="*80)

        return results

    except Exception as e:
        logger.error(f"Benchmark suite failed: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    import sys
    asyncio.run(run_benchmark_suite())
