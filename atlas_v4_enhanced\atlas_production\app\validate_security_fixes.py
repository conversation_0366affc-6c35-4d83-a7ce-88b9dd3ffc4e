"""
A.T.L.A.S. Security Fixes Validation Script
Validates that all critical security vulnerabilities have been addressed
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SecurityFixValidator:
    """Validates that all critical security fixes are properly implemented"""
    
    def __init__(self):
        self.validation_results = {}
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def validate_division_by_zero_fixes(self) -> Dict[str, Any]:
        """Validate division by zero fixes"""
        logger.info("Validating division by zero fixes...")
        
        results = {
            "test_name": "Division by Zero Fixes",
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # Test 1: Trading core position sizing
            from atlas_trading_core import TradingGodEngine
            trading_engine = TradingGodEngine()
            
            # This should not crash with zero risk
            analysis = await trading_engine.generate_6_point_analysis("AAPL", "test with zero risk")
            
            if "Error:" in analysis or "inf" not in analysis.lower():
                results["tests"].append({"name": "Trading position sizing", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Trading position sizing", "status": "FAILED", "error": "Contains infinite values"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Trading position sizing", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        try:
            # Test 2: Risk core calculations
            from atlas_risk_core import AtlasRiskEngine
            risk_engine = AtlasRiskEngine()
            risk_engine.portfolio_value = 0  # Test zero portfolio value
            
            assessment = await risk_engine.assess_position_risk("AAPL", 100, 150.0, 0)

            if assessment.risk_score == 10.0 and assessment.risk_percentage == 100.0:
                results["tests"].append({"name": "Risk core zero portfolio", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Risk core zero portfolio", "status": "FAILED", "error": "Incorrect risk assessment"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Risk core zero portfolio", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        try:
            # Test 3: Mathematical safeguards
            from atlas_math_safeguards import math_safeguards
            
            # Test safe division
            result = math_safeguards.safe_divide(10, 0, default=999)
            if result == 999:
                results["tests"].append({"name": "Mathematical safeguards", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Mathematical safeguards", "status": "FAILED", "error": f"Expected 999, got {result}"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Mathematical safeguards", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        return results
    
    async def validate_paper_trading_enforcement(self) -> Dict[str, Any]:
        """Validate paper trading enforcement"""
        logger.info("Validating paper trading enforcement...")
        
        results = {
            "test_name": "Paper Trading Enforcement",
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # Test 1: Auto trading engine paper mode
            from atlas_trading_core import AutoTradingEngine, TradingSecurityError
            auto_engine = AutoTradingEngine()
            
            # Should be in paper trading mode by default
            if auto_engine.paper_trading_mode == True:
                results["tests"].append({"name": "Default paper trading mode", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Default paper trading mode", "status": "FAILED", "error": "Not in paper trading mode"})
                results["failed"] += 1
            
            # Test 2: Live trading should be blocked
            try:
                auto_engine.set_paper_trading_mode(False)  # Should require override key
                results["tests"].append({"name": "Live trading blocked", "status": "FAILED", "error": "Live trading allowed without override"})
                results["failed"] += 1
            except TradingSecurityError:
                results["tests"].append({"name": "Live trading blocked", "status": "PASSED"})
                results["passed"] += 1
            except Exception as e:
                results["tests"].append({"name": "Live trading blocked", "status": "FAILED", "error": str(e)})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Paper trading enforcement", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        return results
    
    async def validate_input_validation(self) -> Dict[str, Any]:
        """Validate input validation fixes"""
        logger.info("Validating input validation...")
        
        results = {
            "test_name": "Input Validation",
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # Test 1: Symbol validation
            from atlas_input_validator import validator
            
            # Valid symbol
            valid, result = validator.validate_symbol("AAPL")
            if valid and result == "AAPL":
                results["tests"].append({"name": "Valid symbol validation", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Valid symbol validation", "status": "FAILED", "error": f"Expected True/AAPL, got {valid}/{result}"})
                results["failed"] += 1
            
            # Invalid symbol
            valid, result = validator.validate_symbol("INVALID_SYMBOL_123")
            if not valid:
                results["tests"].append({"name": "Invalid symbol rejection", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Invalid symbol rejection", "status": "FAILED", "error": "Invalid symbol was accepted"})
                results["failed"] += 1
            
            # Test 2: Price validation
            valid, result = validator.validate_price(-10)
            if not valid:
                results["tests"].append({"name": "Negative price rejection", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Negative price rejection", "status": "FAILED", "error": "Negative price was accepted"})
                results["failed"] += 1
            
            # Test 3: Dangerous message patterns
            valid, result = validator.validate_user_message("<script>alert('xss')</script>")
            if not valid:
                results["tests"].append({"name": "Dangerous pattern rejection", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Dangerous pattern rejection", "status": "FAILED", "error": "Dangerous pattern was accepted"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Input validation", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        return results
    
    async def validate_stale_data_detection(self) -> Dict[str, Any]:
        """Validate stale data detection"""
        logger.info("Validating stale data detection...")
        
        results = {
            "test_name": "Stale Data Detection",
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # Test market hours detection
            from atlas_market_core import AtlasMarketEngine
            market_engine = AtlasMarketEngine()
            
            # Test market hours function exists
            if hasattr(market_engine, '_is_market_hours'):
                is_market_hours = market_engine._is_market_hours()
                results["tests"].append({"name": "Market hours detection", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "Market hours detection", "status": "FAILED", "error": "Market hours method not found"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "Stale data detection", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        return results
    
    async def validate_ai_fallback_chain(self) -> Dict[str, Any]:
        """Validate AI fallback chain fixes"""
        logger.info("Validating AI fallback chain...")
        
        results = {
            "test_name": "AI Fallback Chain",
            "tests": [],
            "passed": 0,
            "failed": 0
        }
        
        try:
            # Test AI response structure
            from atlas_ai_core import AtlasConversationalEngine
            ai_engine = AtlasConversationalEngine()
            
            # Test that fallback method exists
            if hasattr(ai_engine, '_get_ai_response_with_fallback'):
                results["tests"].append({"name": "AI fallback method exists", "status": "PASSED"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "AI fallback method exists", "status": "FAILED", "error": "Fallback method not found"})
                results["failed"] += 1
                
        except Exception as e:
            results["tests"].append({"name": "AI fallback chain", "status": "FAILED", "error": str(e)})
            results["failed"] += 1
        
        return results
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run all security validation tests"""
        logger.info("Starting comprehensive security fixes validation...")
        
        start_time = datetime.now()
        
        # Run all validation tests
        division_zero_results = await self.validate_division_by_zero_fixes()
        paper_trading_results = await self.validate_paper_trading_enforcement()
        input_validation_results = await self.validate_input_validation()
        stale_data_results = await self.validate_stale_data_detection()
        ai_fallback_results = await self.validate_ai_fallback_chain()
        
        # Aggregate results
        all_results = [
            division_zero_results,
            paper_trading_results,
            input_validation_results,
            stale_data_results,
            ai_fallback_results
        ]
        
        total_passed = sum(result["passed"] for result in all_results)
        total_failed = sum(result["failed"] for result in all_results)
        total_tests = total_passed + total_failed
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        validation_time = (datetime.now() - start_time).total_seconds()
        
        final_results = {
            "validation_timestamp": datetime.now().isoformat(),
            "validation_duration_seconds": validation_time,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "success_rate": success_rate,
            "status": "PASSED" if total_failed == 0 else "FAILED",
            "test_categories": all_results,
            "summary": self._generate_summary(success_rate, total_failed)
        }
        
        self.validation_results = final_results
        return final_results
    
    def _generate_summary(self, success_rate: float, failed_tests: int) -> List[str]:
        """Generate validation summary"""
        summary = []
        
        if failed_tests == 0:
            summary.append("✅ ALL SECURITY FIXES VALIDATED SUCCESSFULLY")
            summary.append("🔒 System is ready for deployment")
            summary.append("🎯 All critical vulnerabilities have been addressed")
        else:
            summary.append(f"❌ {failed_tests} SECURITY TESTS FAILED")
            summary.append("🚫 System is NOT ready for deployment")
            summary.append("⚠️ Critical vulnerabilities still exist")
        
        summary.append(f"📊 Overall success rate: {success_rate:.1f}%")
        
        return summary


async def main():
    """Main validation function"""
    validator = SecurityFixValidator()
    
    print("\n" + "="*80)
    print("A.T.L.A.S. SECURITY FIXES VALIDATION")
    print("="*80)
    
    # Run comprehensive validation
    results = await validator.run_comprehensive_validation()
    
    # Print summary
    print(f"\nValidation completed in {results['validation_duration_seconds']:.2f} seconds")
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['total_passed']}")
    print(f"Failed: {results['total_failed']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Status: {results['status']}")
    
    print(f"\nTest Results by Category:")
    for category in results['test_categories']:
        print(f"  {category['test_name']}: {category['passed']}/{category['passed'] + category['failed']} passed")
        for test in category['tests']:
            status_icon = "✅" if test['status'] == "PASSED" else "❌"
            print(f"    {status_icon} {test['name']}")
            if test['status'] == "FAILED" and 'error' in test:
                print(f"      Error: {test['error']}")
    
    print(f"\nSummary:")
    for item in results['summary']:
        print(f"  {item}")
    
    print("="*80)
    
    return results['status'] == "PASSED"


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
