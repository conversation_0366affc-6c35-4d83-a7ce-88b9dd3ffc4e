#!/usr/bin/env python3
"""
A.T.L.A.S. Scaled System Comprehensive Test Suite
Test the enhanced scanner with 250+ symbols, multi-timeframe Lee Method, and all scaling features
"""

import asyncio
import logging
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import A.T.L.A.S. components
try:
    from atlas_realtime_scanner import AtlasRealtimeScanner, ScannerConfig
    from atlas_lee_method import LeeMethodScanner
    from atlas_performance_monitor import performance_monitor
    from atlas_dynamic_worker_pool import dynamic_worker_pool
    from atlas_multi_api_manager import MultiAPIManager
    from sp500_symbols import get_sp500_symbols, get_high_volume_symbols
except ImportError as e:
    logger.error(f"Failed to import A.T.L.A.S. components: {e}")
    sys.exit(1)


class ScaledSystemTestSuite:
    """Comprehensive test suite for the scaled A.T.L.A.S. system"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # Test configuration
        self.test_symbol_counts = [50, 100, 150, 200, 250]  # Progressive scaling test
        self.test_duration_minutes = 5  # Test duration per symbol count
        
        # Components to test
        self.scanner = None
        self.lee_scanner = None
        self.api_manager = None
        
    async def initialize_components(self):
        """Initialize all system components"""
        try:
            logger.info("🚀 Initializing A.T.L.A.S. Scaled System Components")
            
            # Initialize Lee Method Scanner
            self.lee_scanner = LeeMethodScanner()
            success = await self.lee_scanner.initialize()
            if not success:
                raise Exception("Failed to initialize Lee Method Scanner")
            
            # Initialize Multi-API Manager
            self.api_manager = MultiAPIManager()
            success = await self.api_manager.initialize()
            if not success:
                raise Exception("Failed to initialize Multi-API Manager")
            
            # Register components with performance monitor
            performance_monitor.register_components(
                scanner=None,  # Will be set when scanner is created
                worker_pool=dynamic_worker_pool,
                rate_limiter=None  # Will be set when available
            )
            
            # Start performance monitoring
            performance_monitor.start_monitoring()
            
            logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Component initialization failed: {e}")
            return False
    
    async def test_symbol_scaling(self, symbol_count: int) -> Dict[str, Any]:
        """Test system performance with specific symbol count"""
        logger.info(f"\n📊 Testing with {symbol_count} symbols")
        logger.info("=" * 60)
        
        test_start = time.time()
        results = {
            'symbol_count': symbol_count,
            'start_time': datetime.now().isoformat(),
            'duration_minutes': self.test_duration_minutes,
            'metrics': {},
            'errors': [],
            'success': False
        }
        
        try:
            # Create scanner configuration for this test
            config = ScannerConfig(
                enabled=True,
                max_symbols=symbol_count,
                ultra_priority_interval=2,
                high_priority_interval=5,
                medium_priority_interval=15,
                low_priority_interval=60,
                max_concurrent_scans=min(25, symbol_count // 10),
                api_rate_limit=3000,
                batch_size=min(50, symbol_count // 5)
            )
            
            # Get test symbols
            all_symbols = get_sp500_symbols()
            high_volume = get_high_volume_symbols()
            
            # Create symbol list prioritizing high volume
            test_symbols = list(set(high_volume[:symbol_count//2] + all_symbols[:symbol_count]))[:symbol_count]
            config.symbol_filter = test_symbols
            
            logger.info(f"   📋 Test symbols: {len(test_symbols)} ({test_symbols[:5]}...)")
            
            # Initialize scanner
            self.scanner = AtlasRealtimeScanner()

            # Update scanner configuration
            self.scanner.config.max_symbols = symbol_count
            self.scanner.config.symbol_filter = test_symbols
            self.scanner.config.max_concurrent_scans = min(25, symbol_count // 10)
            self.scanner.config.api_rate_limit = 3000
            self.scanner.config.batch_size = min(50, symbol_count // 5)

            success = await self.scanner.initialize()
            
            if not success:
                raise Exception("Failed to initialize scanner")
            
            # Register scanner with performance monitor
            performance_monitor.scanner_ref = self.scanner
            
            # Start scanning
            logger.info(f"   🔄 Starting {self.test_duration_minutes}-minute scan test...")
            await self.scanner.start()
            
            # Collect metrics during test
            test_metrics = await self._collect_test_metrics(self.test_duration_minutes * 60)
            results['metrics'] = test_metrics
            
            # Stop scanner
            await self.scanner.stop()
            
            # Calculate test results
            test_duration = time.time() - test_start
            results['actual_duration_seconds'] = test_duration
            results['success'] = True
            
            # Log results
            logger.info(f"   ✅ Test completed successfully")
            logger.info(f"   📈 Symbols scanned: {test_metrics.get('total_scans', 0)}")
            logger.info(f"   🎯 Patterns found: {test_metrics.get('patterns_found', 0)}")
            logger.info(f"   ⚡ Avg scan time: {test_metrics.get('avg_scan_time', 0):.2f}s")
            logger.info(f"   📊 API success rate: {test_metrics.get('api_success_rate', 0):.1%}")
            
        except Exception as e:
            logger.error(f"   ❌ Test failed: {e}")
            results['errors'].append(str(e))
            results['success'] = False
        
        finally:
            # Cleanup
            if self.scanner:
                try:
                    await self.scanner.stop()
                except:
                    pass
        
        return results
    
    async def _collect_test_metrics(self, duration_seconds: int) -> Dict[str, Any]:
        """Collect metrics during test execution"""
        metrics = {
            'total_scans': 0,
            'patterns_found': 0,
            'api_calls': 0,
            'api_success_rate': 1.0,
            'avg_scan_time': 0.0,
            'memory_usage_mb': 0.0,
            'cpu_usage_percent': 0.0,
            'worker_utilization': 0.0,
            'errors': 0
        }
        
        start_time = time.time()
        sample_count = 0
        
        try:
            while time.time() - start_time < duration_seconds:
                # Get performance metrics
                perf_summary = performance_monitor.get_performance_summary()
                
                if 'current_metrics' in perf_summary:
                    current = perf_summary['current_metrics']
                    
                    # Accumulate metrics
                    metrics['api_calls'] += current.get('api_requests_per_second', 0) * 10
                    metrics['memory_usage_mb'] = current.get('memory_usage_mb', 0)
                    metrics['cpu_usage_percent'] = current.get('cpu_usage_percent', 0)
                    metrics['worker_utilization'] = current.get('worker_utilization', 0)
                    
                    # Update averages
                    sample_count += 1
                    if sample_count > 0:
                        metrics['avg_scan_time'] = (
                            metrics['avg_scan_time'] * (sample_count - 1) + 
                            current.get('avg_response_time', 0)
                        ) / sample_count
                
                # Get scanner metrics if available
                if self.scanner and hasattr(self.scanner, 'get_status'):
                    scanner_status = self.scanner.get_status()
                    metrics['total_scans'] = scanner_status.get('total_scans', 0)
                    metrics['patterns_found'] = scanner_status.get('patterns_found', 0)
                    metrics['errors'] = scanner_status.get('errors', 0)
                
                # Wait before next sample
                await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"Error collecting test metrics: {e}")
        
        return metrics
    
    async def test_multi_timeframe_performance(self) -> Dict[str, Any]:
        """Test multi-timeframe Lee Method performance"""
        logger.info("\n🎯 Testing Multi-Timeframe Lee Method Performance")
        logger.info("=" * 60)
        
        results = {
            'test_name': 'multi_timeframe_performance',
            'symbols_tested': [],
            'daily_signals': 0,
            'weekly_signals': 0,
            'multi_tf_signals': 0,
            'avg_confidence_daily': 0.0,
            'avg_confidence_weekly': 0.0,
            'avg_scan_time': 0.0,
            'success': False
        }
        
        try:
            # Test symbols
            test_symbols = get_high_volume_symbols()[:20]  # Test with 20 high-volume symbols
            
            daily_confidences = []
            weekly_confidences = []
            scan_times = []
            
            for symbol in test_symbols:
                try:
                    start_time = time.time()
                    
                    # Test multi-timeframe scanning
                    multi_tf_result = await self.lee_scanner.scan_symbol_multi_timeframe(symbol)
                    
                    scan_time = time.time() - start_time
                    scan_times.append(scan_time)
                    
                    if multi_tf_result and 'signals' in multi_tf_result:
                        results['symbols_tested'].append(symbol)
                        
                        # Count signals by timeframe
                        if '1day' in multi_tf_result['signals']:
                            results['daily_signals'] += 1
                            daily_confidences.append(multi_tf_result['signals']['1day']['confidence'])
                        
                        if '1week' in multi_tf_result['signals']:
                            results['weekly_signals'] += 1
                            weekly_confidences.append(multi_tf_result['signals']['1week']['confidence'])
                        
                        if multi_tf_result.get('best_signal'):
                            results['multi_tf_signals'] += 1
                    
                    logger.info(f"   📊 {symbol}: {len(multi_tf_result.get('signals', {}))} signals, {scan_time:.2f}s")
                    
                except Exception as e:
                    logger.warning(f"   ⚠️ Error testing {symbol}: {e}")
                    continue
            
            # Calculate averages
            if daily_confidences:
                results['avg_confidence_daily'] = sum(daily_confidences) / len(daily_confidences)
            
            if weekly_confidences:
                results['avg_confidence_weekly'] = sum(weekly_confidences) / len(weekly_confidences)
            
            if scan_times:
                results['avg_scan_time'] = sum(scan_times) / len(scan_times)
            
            results['success'] = True
            
            # Log results
            logger.info(f"   ✅ Multi-timeframe test completed")
            logger.info(f"   📈 Symbols tested: {len(results['symbols_tested'])}")
            logger.info(f"   📊 Daily signals: {results['daily_signals']}")
            logger.info(f"   📅 Weekly signals: {results['weekly_signals']}")
            logger.info(f"   🎯 Multi-TF signals: {results['multi_tf_signals']}")
            logger.info(f"   ⚡ Avg scan time: {results['avg_scan_time']:.2f}s")
            
        except Exception as e:
            logger.error(f"❌ Multi-timeframe test failed: {e}")
            results['success'] = False
            results['error'] = str(e)
        
        return results
    
    async def test_api_capacity_and_rotation(self) -> Dict[str, Any]:
        """Test API capacity and key rotation"""
        logger.info("\n🔄 Testing API Capacity and Key Rotation")
        logger.info("=" * 60)
        
        results = {
            'test_name': 'api_capacity_rotation',
            'providers_tested': [],
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'key_rotations': 0,
            'success': False
        }
        
        try:
            # Get API manager statistics
            api_stats = self.api_manager.get_statistics()
            
            results['providers_tested'] = list(api_stats.get('healthy_providers', []))
            results['total_requests'] = api_stats.get('request_metrics', {}).get('total_requests', 0)
            results['successful_requests'] = api_stats.get('request_metrics', {}).get('successful_requests', 0)
            results['failed_requests'] = api_stats.get('request_metrics', {}).get('failed_requests', 0)
            results['avg_response_time'] = api_stats.get('request_metrics', {}).get('average_response_time', 0)
            
            # Calculate success rate
            if results['total_requests'] > 0:
                success_rate = results['successful_requests'] / results['total_requests']
                results['success_rate'] = success_rate
            else:
                results['success_rate'] = 0.0
            
            results['success'] = True
            
            # Log results
            logger.info(f"   ✅ API capacity test completed")
            logger.info(f"   🌐 Healthy providers: {len(results['providers_tested'])}")
            logger.info(f"   📊 Total requests: {results['total_requests']}")
            logger.info(f"   ✅ Success rate: {results['success_rate']:.1%}")
            logger.info(f"   ⚡ Avg response time: {results['avg_response_time']:.2f}s")
            
        except Exception as e:
            logger.error(f"❌ API capacity test failed: {e}")
            results['success'] = False
            results['error'] = str(e)
        
        return results
    
    async def run_comprehensive_test(self):
        """Run the complete test suite"""
        logger.info("🚀 Starting A.T.L.A.S. Scaled System Comprehensive Test Suite")
        logger.info("=" * 80)
        
        self.start_time = datetime.now()
        
        # Initialize components
        if not await self.initialize_components():
            logger.error("❌ Failed to initialize components - aborting tests")
            return False
        
        try:
            # Test 1: Multi-timeframe performance
            multi_tf_results = await self.test_multi_timeframe_performance()
            self.test_results['multi_timeframe'] = multi_tf_results
            
            # Test 2: API capacity and rotation
            api_results = await self.test_api_capacity_and_rotation()
            self.test_results['api_capacity'] = api_results
            
            # Test 3: Progressive symbol scaling
            scaling_results = []
            for symbol_count in self.test_symbol_counts:
                result = await self.test_symbol_scaling(symbol_count)
                scaling_results.append(result)
                
                # Brief pause between tests
                await asyncio.sleep(30)
            
            self.test_results['scaling'] = scaling_results
            
            # Generate final report
            await self._generate_final_report()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Test suite execution failed: {e}")
            return False
        
        finally:
            # Cleanup
            try:
                performance_monitor.stop_monitoring()
                if self.api_manager:
                    await self.api_manager.cleanup()
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
    
    async def _generate_final_report(self):
        """Generate comprehensive test report"""
        self.end_time = datetime.now()
        
        logger.info("\n" + "=" * 80)
        logger.info("📊 A.T.L.A.S. SCALED SYSTEM TEST REPORT")
        logger.info("=" * 80)
        
        # Test duration
        duration = self.end_time - self.start_time
        logger.info(f"Test Duration: {duration}")
        logger.info(f"Start Time: {self.start_time}")
        logger.info(f"End Time: {self.end_time}")
        
        # Multi-timeframe results
        if 'multi_timeframe' in self.test_results:
            mt_results = self.test_results['multi_timeframe']
            logger.info(f"\n🎯 MULTI-TIMEFRAME LEE METHOD:")
            logger.info(f"   Success: {'✅' if mt_results['success'] else '❌'}")
            logger.info(f"   Symbols Tested: {len(mt_results['symbols_tested'])}")
            logger.info(f"   Daily Signals: {mt_results['daily_signals']}")
            logger.info(f"   Weekly Signals: {mt_results['weekly_signals']}")
            logger.info(f"   Avg Scan Time: {mt_results['avg_scan_time']:.2f}s")
        
        # API capacity results
        if 'api_capacity' in self.test_results:
            api_results = self.test_results['api_capacity']
            logger.info(f"\n🔄 API CAPACITY & ROTATION:")
            logger.info(f"   Success: {'✅' if api_results['success'] else '❌'}")
            logger.info(f"   Healthy Providers: {len(api_results['providers_tested'])}")
            logger.info(f"   Total Requests: {api_results['total_requests']}")
            logger.info(f"   Success Rate: {api_results.get('success_rate', 0):.1%}")
        
        # Scaling results
        if 'scaling' in self.test_results:
            logger.info(f"\n📈 SYMBOL SCALING TESTS:")
            for result in self.test_results['scaling']:
                status = "✅" if result['success'] else "❌"
                logger.info(f"   {result['symbol_count']} symbols: {status}")
                if result['success']:
                    metrics = result['metrics']
                    logger.info(f"      Scans: {metrics.get('total_scans', 0)}")
                    logger.info(f"      Patterns: {metrics.get('patterns_found', 0)}")
                    logger.info(f"      API Success: {metrics.get('api_success_rate', 0):.1%}")
        
        # Overall assessment
        all_tests_passed = all([
            self.test_results.get('multi_timeframe', {}).get('success', False),
            self.test_results.get('api_capacity', {}).get('success', False),
            all(r['success'] for r in self.test_results.get('scaling', []))
        ])
        
        logger.info(f"\n🎉 OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_tests_passed else '❌ SOME TESTS FAILED'}")
        
        if all_tests_passed:
            logger.info("\n🚀 A.T.L.A.S. Scaled System is ready for 250+ symbol production scanning!")
            logger.info("Features validated:")
            logger.info("  ✅ Multi-timeframe Lee Method patterns (daily + weekly)")
            logger.info("  ✅ TTM Squeeze integration")
            logger.info("  ✅ Multi-tier scanning architecture")
            logger.info("  ✅ Intelligent API key rotation")
            logger.info("  ✅ Dynamic worker pool scaling")
            logger.info("  ✅ Performance monitoring and auto-scaling")
            logger.info("  ✅ Alternative data source integration")
        
        # Save detailed results to file
        try:
            with open('atlas_scaled_system_test_results.json', 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            logger.info(f"\n📄 Detailed results saved to: atlas_scaled_system_test_results.json")
        except Exception as e:
            logger.error(f"Error saving results: {e}")


async def main():
    """Main test execution"""
    test_suite = ScaledSystemTestSuite()
    success = await test_suite.run_comprehensive_test()
    
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Test suite interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test suite execution failed: {e}")
        sys.exit(1)
