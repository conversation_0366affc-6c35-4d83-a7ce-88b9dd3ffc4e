"""
A.T.L.A.S. Security and Compliance Module
Comprehensive security and compliance features for the multi-agent trading system
"""

import asyncio
import logging
import json
import hashlib
import hmac
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
from pathlib import Path

# Cryptography imports
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import base64
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# Core imports
from models import EngineStatus

logger = logging.getLogger(__name__)

# ============================================================================
# SECURITY ENUMS AND MODELS
# ============================================================================

class SecurityLevel(Enum):
    """Security levels for different operations"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ComplianceStatus(Enum):
    """Compliance status for operations"""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING_REVIEW = "pending_review"
    EXEMPT = "exempt"

class AuditEventType(Enum):
    """Types of audit events"""
    TRADE_EXECUTION = "trade_execution"
    RISK_ASSESSMENT = "risk_assessment"
    DATA_ACCESS = "data_access"
    AGENT_COMMUNICATION = "agent_communication"
    SYSTEM_ACCESS = "system_access"
    CONFIGURATION_CHANGE = "configuration_change"
    COMPLIANCE_CHECK = "compliance_check"
    SECURITY_INCIDENT = "security_incident"

@dataclass
class AuditEvent:
    """Audit event structure"""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: Optional[str]
    agent_id: Optional[str]
    action: str
    details: Dict[str, Any]
    security_level: SecurityLevel
    compliance_status: ComplianceStatus
    ip_address: Optional[str] = None
    session_id: Optional[str] = None
    risk_score: float = 0.0

@dataclass
class ComplianceRule:
    """Compliance rule definition"""
    rule_id: str
    name: str
    description: str
    rule_type: str
    parameters: Dict[str, Any]
    severity: SecurityLevel
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class SecurityPolicy:
    """Security policy definition"""
    policy_id: str
    name: str
    description: str
    rules: List[str]  # Rule IDs
    enforcement_level: SecurityLevel
    applicable_agents: List[str]
    created_at: datetime = field(default_factory=datetime.now)

# ============================================================================
# API KEY ENCRYPTION MANAGER
# ============================================================================

class APIKeyEncryptionManager:
    """Secure API key encryption and management"""
    
    def __init__(self, master_password: Optional[str] = None):
        self.master_password = master_password or self._generate_master_password()
        self.encryption_key = None
        self.encrypted_keys: Dict[str, str] = {}
        self.key_metadata: Dict[str, Dict[str, Any]] = {}
        
        if CRYPTO_AVAILABLE:
            self._initialize_encryption()
        else:
            logger.warning("Cryptography library not available - API keys will not be encrypted")
    
    def _generate_master_password(self) -> str:
        """Generate a secure master password"""
        return secrets.token_urlsafe(32)
    
    def _initialize_encryption(self):
        """Initialize encryption system"""
        if not CRYPTO_AVAILABLE:
            return
        
        try:
            # Derive encryption key from master password
            password = self.master_password.encode()
            salt = b'atlas_security_salt_2024'  # In production, use random salt
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self.encryption_key = Fernet(key)
            
            logger.info("API key encryption initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            self.encryption_key = None
    
    def encrypt_api_key(self, key_name: str, api_key: str, metadata: Dict[str, Any] = None) -> bool:
        """Encrypt and store an API key"""
        try:
            if not CRYPTO_AVAILABLE or not self.encryption_key:
                logger.warning(f"Encryption not available - storing {key_name} in plain text")
                self.encrypted_keys[key_name] = api_key
                return True
            
            # Encrypt the API key
            encrypted_key = self.encryption_key.encrypt(api_key.encode())
            self.encrypted_keys[key_name] = base64.urlsafe_b64encode(encrypted_key).decode()
            
            # Store metadata
            self.key_metadata[key_name] = {
                "created_at": datetime.now().isoformat(),
                "last_accessed": None,
                "access_count": 0,
                "metadata": metadata or {}
            }
            
            logger.info(f"API key '{key_name}' encrypted and stored successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to encrypt API key '{key_name}': {e}")
            return False
    
    def decrypt_api_key(self, key_name: str) -> Optional[str]:
        """Decrypt and retrieve an API key"""
        try:
            if key_name not in self.encrypted_keys:
                logger.warning(f"API key '{key_name}' not found")
                return None
            
            encrypted_key = self.encrypted_keys[key_name]
            
            if not CRYPTO_AVAILABLE or not self.encryption_key:
                # Return plain text key if encryption not available
                return encrypted_key
            
            # Decrypt the API key
            encrypted_data = base64.urlsafe_b64decode(encrypted_key.encode())
            decrypted_key = self.encryption_key.decrypt(encrypted_data).decode()
            
            # Update access metadata
            if key_name in self.key_metadata:
                self.key_metadata[key_name]["last_accessed"] = datetime.now().isoformat()
                self.key_metadata[key_name]["access_count"] += 1
            
            return decrypted_key
            
        except Exception as e:
            logger.error(f"Failed to decrypt API key '{key_name}': {e}")
            return None
    
    def rotate_api_key(self, key_name: str, new_api_key: str) -> bool:
        """Rotate an API key"""
        try:
            if key_name not in self.encrypted_keys:
                logger.warning(f"API key '{key_name}' not found for rotation")
                return False
            
            # Store old key metadata
            old_metadata = self.key_metadata.get(key_name, {})
            
            # Encrypt new key
            success = self.encrypt_api_key(key_name, new_api_key, old_metadata.get("metadata", {}))
            
            if success:
                # Update rotation metadata
                self.key_metadata[key_name]["rotated_at"] = datetime.now().isoformat()
                self.key_metadata[key_name]["rotation_count"] = old_metadata.get("rotation_count", 0) + 1
                logger.info(f"API key '{key_name}' rotated successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to rotate API key '{key_name}': {e}")
            return False
    
    def list_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """List all stored API keys with metadata (without revealing keys)"""
        return {
            key_name: {
                **metadata,
                "encrypted": CRYPTO_AVAILABLE and self.encryption_key is not None
            }
            for key_name, metadata in self.key_metadata.items()
        }

# ============================================================================
# AUDIT TRAIL MANAGER
# ============================================================================

class AuditTrailManager:
    """Comprehensive audit trail management"""
    
    def __init__(self, storage_path: str = "audit_logs"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self.audit_events: List[AuditEvent] = []
        self.max_memory_events = 10000
        self.auto_flush_interval = 300  # 5 minutes
        self.last_flush = time.time()
        
        logger.info(f"Audit trail manager initialized with storage path: {self.storage_path}")
    
    def log_event(self, event_type: AuditEventType, action: str, details: Dict[str, Any],
                  user_id: Optional[str] = None, agent_id: Optional[str] = None,
                  security_level: SecurityLevel = SecurityLevel.MEDIUM,
                  compliance_status: ComplianceStatus = ComplianceStatus.COMPLIANT,
                  ip_address: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """Log an audit event"""
        try:
            event_id = str(uuid.uuid4())
            
            # Calculate risk score based on event type and security level
            risk_score = self._calculate_risk_score(event_type, security_level, details)
            
            event = AuditEvent(
                event_id=event_id,
                event_type=event_type,
                timestamp=datetime.now(),
                user_id=user_id,
                agent_id=agent_id,
                action=action,
                details=details,
                security_level=security_level,
                compliance_status=compliance_status,
                ip_address=ip_address,
                session_id=session_id,
                risk_score=risk_score
            )
            
            self.audit_events.append(event)
            
            # Auto-flush if needed
            if (len(self.audit_events) >= self.max_memory_events or 
                time.time() - self.last_flush > self.auto_flush_interval):
                asyncio.create_task(self.flush_events())
            
            # Log high-risk events immediately
            if risk_score > 0.8:
                logger.warning(f"High-risk audit event: {event_type.value} - {action} (Risk: {risk_score:.2f})")
            
            return event_id
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return ""
    
    def _calculate_risk_score(self, event_type: AuditEventType, security_level: SecurityLevel, 
                             details: Dict[str, Any]) -> float:
        """Calculate risk score for an event"""
        base_scores = {
            AuditEventType.TRADE_EXECUTION: 0.7,
            AuditEventType.RISK_ASSESSMENT: 0.5,
            AuditEventType.DATA_ACCESS: 0.3,
            AuditEventType.AGENT_COMMUNICATION: 0.4,
            AuditEventType.SYSTEM_ACCESS: 0.6,
            AuditEventType.CONFIGURATION_CHANGE: 0.8,
            AuditEventType.COMPLIANCE_CHECK: 0.2,
            AuditEventType.SECURITY_INCIDENT: 1.0
        }
        
        level_multipliers = {
            SecurityLevel.LOW: 0.5,
            SecurityLevel.MEDIUM: 1.0,
            SecurityLevel.HIGH: 1.5,
            SecurityLevel.CRITICAL: 2.0
        }
        
        base_score = base_scores.get(event_type, 0.5)
        multiplier = level_multipliers.get(security_level, 1.0)
        
        # Additional risk factors
        risk_factors = 0.0
        if details.get("failed_attempt", False):
            risk_factors += 0.3
        if details.get("unusual_pattern", False):
            risk_factors += 0.2
        if details.get("after_hours", False):
            risk_factors += 0.1
        
        final_score = min(1.0, (base_score * multiplier) + risk_factors)
        return round(final_score, 3)
    
    async def flush_events(self):
        """Flush audit events to persistent storage"""
        try:
            if not self.audit_events:
                return
            
            # Create daily log file
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = self.storage_path / f"audit_log_{today}.jsonl"
            
            # Write events to file
            with open(log_file, "a", encoding="utf-8") as f:
                for event in self.audit_events:
                    event_dict = {
                        "event_id": event.event_id,
                        "event_type": event.event_type.value,
                        "timestamp": event.timestamp.isoformat(),
                        "user_id": event.user_id,
                        "agent_id": event.agent_id,
                        "action": event.action,
                        "details": event.details,
                        "security_level": event.security_level.value,
                        "compliance_status": event.compliance_status.value,
                        "ip_address": event.ip_address,
                        "session_id": event.session_id,
                        "risk_score": event.risk_score
                    }
                    f.write(json.dumps(event_dict) + "\n")
            
            logger.info(f"Flushed {len(self.audit_events)} audit events to {log_file}")
            
            # Clear memory events
            self.audit_events.clear()
            self.last_flush = time.time()
            
        except Exception as e:
            logger.error(f"Failed to flush audit events: {e}")
    
    def search_events(self, event_type: Optional[AuditEventType] = None,
                     user_id: Optional[str] = None, agent_id: Optional[str] = None,
                     start_time: Optional[datetime] = None, end_time: Optional[datetime] = None,
                     min_risk_score: float = 0.0, limit: int = 100) -> List[AuditEvent]:
        """Search audit events with filters"""
        try:
            filtered_events = []
            
            for event in self.audit_events:
                # Apply filters
                if event_type and event.event_type != event_type:
                    continue
                if user_id and event.user_id != user_id:
                    continue
                if agent_id and event.agent_id != agent_id:
                    continue
                if start_time and event.timestamp < start_time:
                    continue
                if end_time and event.timestamp > end_time:
                    continue
                if event.risk_score < min_risk_score:
                    continue
                
                filtered_events.append(event)
                
                if len(filtered_events) >= limit:
                    break
            
            return filtered_events
            
        except Exception as e:
            logger.error(f"Failed to search audit events: {e}")
            return []
    
    def get_compliance_report(self, days_back: int = 30) -> Dict[str, Any]:
        """Generate compliance report"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_back)
            recent_events = [e for e in self.audit_events if e.timestamp >= cutoff_time]
            
            # Compliance statistics
            total_events = len(recent_events)
            compliant_events = len([e for e in recent_events if e.compliance_status == ComplianceStatus.COMPLIANT])
            non_compliant_events = len([e for e in recent_events if e.compliance_status == ComplianceStatus.NON_COMPLIANT])
            pending_events = len([e for e in recent_events if e.compliance_status == ComplianceStatus.PENDING_REVIEW])
            
            # Risk analysis
            high_risk_events = len([e for e in recent_events if e.risk_score > 0.7])
            avg_risk_score = sum(e.risk_score for e in recent_events) / total_events if total_events > 0 else 0.0
            
            # Event type breakdown
            event_type_counts = {}
            for event in recent_events:
                event_type = event.event_type.value
                event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
            
            return {
                "report_period_days": days_back,
                "total_events": total_events,
                "compliance_summary": {
                    "compliant": compliant_events,
                    "non_compliant": non_compliant_events,
                    "pending_review": pending_events,
                    "compliance_rate": (compliant_events / total_events * 100) if total_events > 0 else 0.0
                },
                "risk_analysis": {
                    "high_risk_events": high_risk_events,
                    "average_risk_score": round(avg_risk_score, 3),
                    "risk_distribution": {
                        "low": len([e for e in recent_events if e.risk_score < 0.3]),
                        "medium": len([e for e in recent_events if 0.3 <= e.risk_score < 0.7]),
                        "high": len([e for e in recent_events if e.risk_score >= 0.7])
                    }
                },
                "event_type_breakdown": event_type_counts,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            return {"error": str(e)}

# ============================================================================
# COMPLIANCE RULE ENGINE
# ============================================================================

class ComplianceRuleEngine:
    """Rule-based compliance checking engine"""
    
    def __init__(self):
        self.rules: Dict[str, ComplianceRule] = {}
        self.policies: Dict[str, SecurityPolicy] = {}
        self.violation_history: List[Dict[str, Any]] = []
        
        # Initialize default rules
        self._initialize_default_rules()
        
        logger.info("Compliance rule engine initialized")
    
    def _initialize_default_rules(self):
        """Initialize default compliance rules"""
        default_rules = [
            ComplianceRule(
                rule_id="max_position_size",
                name="Maximum Position Size",
                description="Limit maximum position size to 10% of portfolio",
                rule_type="position_limit",
                parameters={"max_percentage": 10.0},
                severity=SecurityLevel.HIGH
            ),
            ComplianceRule(
                rule_id="daily_var_limit",
                name="Daily VaR Limit",
                description="Limit daily Value at Risk to 2% of portfolio",
                rule_type="risk_limit",
                parameters={"max_var_percentage": 2.0},
                severity=SecurityLevel.CRITICAL
            ),
            ComplianceRule(
                rule_id="trade_frequency_limit",
                name="Trade Frequency Limit",
                description="Limit trades to maximum 100 per day",
                rule_type="frequency_limit",
                parameters={"max_trades_per_day": 100},
                severity=SecurityLevel.MEDIUM
            ),
            ComplianceRule(
                rule_id="after_hours_trading",
                name="After Hours Trading Restriction",
                description="Restrict trading outside market hours",
                rule_type="time_restriction",
                parameters={"market_open": "09:30", "market_close": "16:00"},
                severity=SecurityLevel.MEDIUM
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.rule_id] = rule
    
    def add_rule(self, rule: ComplianceRule) -> bool:
        """Add a new compliance rule"""
        try:
            self.rules[rule.rule_id] = rule
            logger.info(f"Added compliance rule: {rule.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add compliance rule: {e}")
            return False
    
    def check_compliance(self, operation_type: str, operation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check compliance for an operation"""
        try:
            violations = []
            warnings = []
            
            for rule_id, rule in self.rules.items():
                if not rule.enabled:
                    continue
                
                violation = self._check_rule(rule, operation_type, operation_data)
                if violation:
                    if rule.severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
                        violations.append(violation)
                    else:
                        warnings.append(violation)
            
            compliance_status = ComplianceStatus.COMPLIANT
            if violations:
                compliance_status = ComplianceStatus.NON_COMPLIANT
            elif warnings:
                compliance_status = ComplianceStatus.PENDING_REVIEW
            
            result = {
                "compliance_status": compliance_status.value,
                "violations": violations,
                "warnings": warnings,
                "checked_rules": len(self.rules),
                "timestamp": datetime.now().isoformat()
            }
            
            # Log violations
            if violations or warnings:
                self.violation_history.append({
                    "operation_type": operation_type,
                    "operation_data": operation_data,
                    "result": result
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Compliance check failed: {e}")
            return {
                "compliance_status": ComplianceStatus.NON_COMPLIANT.value,
                "violations": [{"rule": "system_error", "message": str(e)}],
                "warnings": [],
                "checked_rules": 0,
                "timestamp": datetime.now().isoformat()
            }
    
    def _check_rule(self, rule: ComplianceRule, operation_type: str, operation_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check a specific compliance rule"""
        try:
            if rule.rule_type == "position_limit":
                return self._check_position_limit(rule, operation_data)
            elif rule.rule_type == "risk_limit":
                return self._check_risk_limit(rule, operation_data)
            elif rule.rule_type == "frequency_limit":
                return self._check_frequency_limit(rule, operation_data)
            elif rule.rule_type == "time_restriction":
                return self._check_time_restriction(rule, operation_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Rule check failed for {rule.rule_id}: {e}")
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "violation_type": "check_error",
                "message": f"Rule check failed: {e}",
                "severity": rule.severity.value
            }
    
    def _check_position_limit(self, rule: ComplianceRule, operation_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check position size limit"""
        max_percentage = rule.parameters.get("max_percentage", 10.0)
        position_percentage = operation_data.get("position_percentage", 0.0)
        
        if position_percentage > max_percentage:
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "violation_type": "position_limit_exceeded",
                "message": f"Position size {position_percentage:.1f}% exceeds limit of {max_percentage:.1f}%",
                "severity": rule.severity.value,
                "current_value": position_percentage,
                "limit_value": max_percentage
            }
        
        return None
    
    def _check_risk_limit(self, rule: ComplianceRule, operation_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check risk limit"""
        max_var_percentage = rule.parameters.get("max_var_percentage", 2.0)
        var_percentage = operation_data.get("var_percentage", 0.0)
        
        if var_percentage > max_var_percentage:
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "violation_type": "risk_limit_exceeded",
                "message": f"VaR {var_percentage:.1f}% exceeds limit of {max_var_percentage:.1f}%",
                "severity": rule.severity.value,
                "current_value": var_percentage,
                "limit_value": max_var_percentage
            }
        
        return None
    
    def _check_frequency_limit(self, rule: ComplianceRule, operation_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check trade frequency limit"""
        max_trades = rule.parameters.get("max_trades_per_day", 100)
        current_trades = operation_data.get("daily_trade_count", 0)
        
        if current_trades >= max_trades:
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "violation_type": "frequency_limit_exceeded",
                "message": f"Daily trade count {current_trades} exceeds limit of {max_trades}",
                "severity": rule.severity.value,
                "current_value": current_trades,
                "limit_value": max_trades
            }
        
        return None
    
    def _check_time_restriction(self, rule: ComplianceRule, operation_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check time-based restrictions"""
        market_open = rule.parameters.get("market_open", "09:30")
        market_close = rule.parameters.get("market_close", "16:00")
        
        current_time = datetime.now().strftime("%H:%M")
        
        if current_time < market_open or current_time > market_close:
            return {
                "rule_id": rule.rule_id,
                "rule_name": rule.name,
                "violation_type": "time_restriction_violated",
                "message": f"Trading attempted outside market hours ({market_open}-{market_close})",
                "severity": rule.severity.value,
                "current_time": current_time,
                "allowed_hours": f"{market_open}-{market_close}"
            }
        
        return None

# ============================================================================
# MULTI-AGENT SECURITY MANAGER
# ============================================================================

class MultiAgentSecurityManager:
    """Comprehensive security manager for multi-agent communications"""

    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.api_key_manager = APIKeyEncryptionManager()
        self.audit_manager = AuditTrailManager()
        self.compliance_engine = ComplianceRuleEngine()

        # Security configurations
        self.session_timeout = 3600  # 1 hour
        self.max_failed_attempts = 5
        self.rate_limit_window = 60  # 1 minute
        self.max_requests_per_window = 100

        # Active sessions and rate limiting
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.failed_attempts: Dict[str, int] = {}
        self.rate_limit_tracking: Dict[str, List[float]] = {}

        logger.info("Multi-Agent Security Manager initialized")

    async def initialize(self) -> bool:
        """Initialize the security manager"""
        try:
            # Initialize default API keys (in production, load from secure storage)
            default_keys = {
                "grok_api_key": "your_grok_api_key_here",
                "alpaca_api_key": "your_alpaca_api_key_here",
                "fmp_api_key": "your_fmp_api_key_here"
            }

            for key_name, key_value in default_keys.items():
                self.api_key_manager.encrypt_api_key(
                    key_name, key_value,
                    {"service": key_name.split("_")[0], "environment": "production"}
                )

            self.status = EngineStatus.ACTIVE
            logger.info("Security manager initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize security manager: {e}")
            self.status = EngineStatus.FAILED
            return False

    def create_session(self, user_id: str, ip_address: str, user_agent: str = "") -> Optional[str]:
        """Create a new authenticated session"""
        try:
            session_id = str(uuid.uuid4())

            session_data = {
                "user_id": user_id,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "request_count": 0,
                "is_active": True
            }

            self.active_sessions[session_id] = session_data

            # Log session creation
            self.audit_manager.log_event(
                event_type=AuditEventType.SYSTEM_ACCESS,
                action="session_created",
                details={
                    "user_id": user_id,
                    "ip_address": ip_address,
                    "user_agent": user_agent
                },
                user_id=user_id,
                security_level=SecurityLevel.MEDIUM,
                ip_address=ip_address,
                session_id=session_id
            )

            logger.info(f"Session created for user {user_id}: {session_id}")
            return session_id

        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            return None

    def validate_session(self, session_id: str, ip_address: str) -> bool:
        """Validate an active session"""
        try:
            if session_id not in self.active_sessions:
                return False

            session = self.active_sessions[session_id]

            # Check if session is active
            if not session["is_active"]:
                return False

            # Check session timeout
            time_since_activity = datetime.now() - session["last_activity"]
            if time_since_activity.total_seconds() > self.session_timeout:
                self._invalidate_session(session_id, "timeout")
                return False

            # Check IP address consistency (optional security measure)
            if session["ip_address"] != ip_address:
                logger.warning(f"IP address mismatch for session {session_id}")
                # In production, you might want to invalidate the session
                # self._invalidate_session(session_id, "ip_mismatch")
                # return False

            # Update last activity
            session["last_activity"] = datetime.now()
            session["request_count"] += 1

            return True

        except Exception as e:
            logger.error(f"Session validation failed: {e}")
            return False

    def _invalidate_session(self, session_id: str, reason: str):
        """Invalidate a session"""
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session["is_active"] = False
                session["invalidated_at"] = datetime.now()
                session["invalidation_reason"] = reason

                # Log session invalidation
                self.audit_manager.log_event(
                    event_type=AuditEventType.SYSTEM_ACCESS,
                    action="session_invalidated",
                    details={
                        "reason": reason,
                        "session_duration": (datetime.now() - session["created_at"]).total_seconds()
                    },
                    user_id=session.get("user_id"),
                    security_level=SecurityLevel.MEDIUM,
                    session_id=session_id
                )

                logger.info(f"Session {session_id} invalidated: {reason}")

        except Exception as e:
            logger.error(f"Failed to invalidate session: {e}")

    def check_rate_limit(self, identifier: str) -> bool:
        """Check rate limiting for requests"""
        try:
            current_time = time.time()

            # Initialize tracking for new identifiers
            if identifier not in self.rate_limit_tracking:
                self.rate_limit_tracking[identifier] = []

            # Clean old requests outside the window
            window_start = current_time - self.rate_limit_window
            self.rate_limit_tracking[identifier] = [
                req_time for req_time in self.rate_limit_tracking[identifier]
                if req_time > window_start
            ]

            # Check if limit exceeded
            if len(self.rate_limit_tracking[identifier]) >= self.max_requests_per_window:
                logger.warning(f"Rate limit exceeded for {identifier}")
                return False

            # Add current request
            self.rate_limit_tracking[identifier].append(current_time)
            return True

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return False

    def secure_agent_communication(self, sender_agent_id: str, receiver_agent_id: str,
                                  message: Dict[str, Any]) -> Dict[str, Any]:
        """Secure inter-agent communication"""
        try:
            # Add security headers
            secure_message = {
                "message_id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "sender": sender_agent_id,
                "receiver": receiver_agent_id,
                "payload": message,
                "security": {
                    "integrity_hash": self._calculate_message_hash(message),
                    "encryption_level": "standard"
                }
            }

            # Log agent communication
            self.audit_manager.log_event(
                event_type=AuditEventType.AGENT_COMMUNICATION,
                action="message_sent",
                details={
                    "sender": sender_agent_id,
                    "receiver": receiver_agent_id,
                    "message_type": message.get("type", "unknown"),
                    "message_size": len(str(message))
                },
                agent_id=sender_agent_id,
                security_level=SecurityLevel.LOW
            )

            return secure_message

        except Exception as e:
            logger.error(f"Failed to secure agent communication: {e}")
            return message

    def _calculate_message_hash(self, message: Dict[str, Any]) -> str:
        """Calculate integrity hash for message"""
        try:
            message_str = json.dumps(message, sort_keys=True)
            return hashlib.sha256(message_str.encode()).hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate message hash: {e}")
            return ""

    def verify_trade_compliance(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Verify trade compliance before execution"""
        try:
            # Check compliance rules
            compliance_result = self.compliance_engine.check_compliance("trade_execution", trade_data)

            # Log compliance check
            self.audit_manager.log_event(
                event_type=AuditEventType.COMPLIANCE_CHECK,
                action="trade_compliance_check",
                details={
                    "symbol": trade_data.get("symbol", "unknown"),
                    "trade_type": trade_data.get("trade_type", "unknown"),
                    "position_size": trade_data.get("position_size", 0),
                    "compliance_status": compliance_result["compliance_status"]
                },
                security_level=SecurityLevel.HIGH,
                compliance_status=ComplianceStatus(compliance_result["compliance_status"])
            )

            return compliance_result

        except Exception as e:
            logger.error(f"Trade compliance verification failed: {e}")
            return {
                "compliance_status": ComplianceStatus.NON_COMPLIANT.value,
                "violations": [{"rule": "system_error", "message": str(e)}],
                "warnings": []
            }

    def get_security_status(self) -> Dict[str, Any]:
        """Get comprehensive security status"""
        try:
            active_sessions_count = len([s for s in self.active_sessions.values() if s["is_active"]])

            # Get recent high-risk events
            high_risk_events = self.audit_manager.search_events(
                min_risk_score=0.7,
                start_time=datetime.now() - timedelta(hours=24),
                limit=10
            )

            # Get compliance report
            compliance_report = self.audit_manager.get_compliance_report(days_back=7)

            return {
                "status": self.status.value,
                "active_sessions": active_sessions_count,
                "total_sessions": len(self.active_sessions),
                "api_keys_managed": len(self.api_key_manager.list_api_keys()),
                "compliance_rules": len(self.compliance_engine.rules),
                "recent_high_risk_events": len(high_risk_events),
                "compliance_summary": compliance_report.get("compliance_summary", {}),
                "security_metrics": {
                    "failed_attempts_tracked": len(self.failed_attempts),
                    "rate_limited_identifiers": len(self.rate_limit_tracking),
                    "audit_events_in_memory": len(self.audit_manager.audit_events)
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get security status: {e}")
            return {"error": str(e)}

    async def shutdown(self):
        """Shutdown security manager and flush audit logs"""
        try:
            logger.info("Shutting down security manager...")

            # Flush remaining audit events
            await self.audit_manager.flush_events()

            # Invalidate all active sessions
            for session_id in list(self.active_sessions.keys()):
                self._invalidate_session(session_id, "system_shutdown")

            self.status = EngineStatus.STOPPED
            logger.info("Security manager shutdown completed")

        except Exception as e:
            logger.error(f"Security manager shutdown failed: {e}")

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    # Enums
    "SecurityLevel",
    "ComplianceStatus",
    "AuditEventType",

    # Models
    "AuditEvent",
    "ComplianceRule",
    "SecurityPolicy",

    # Managers
    "APIKeyEncryptionManager",
    "AuditTrailManager",
    "ComplianceRuleEngine",
    "MultiAgentSecurityManager",

    # Constants
    "CRYPTO_AVAILABLE"
]
