import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

// Import components to test
import AtlasApp from '../../src/components/AtlasApp';
import AtlasChatInterface from '../../src/components/chat/AtlasChatInterface';
import AtlasStatusDashboard from '../../src/components/status/AtlasStatusDashboard';
import AtlasChartAnalysisPanel from '../../src/components/charts/AtlasChartAnalysisPanel';
import AtlasProgressIndicator from '../../src/components/progress/AtlasProgressIndicator';
import AtlasTerminalOutput from '../../src/components/terminal/AtlasTerminalOutput';
import AtlasConversationMonitor from '../../src/components/monitoring/AtlasConversationMonitor';

// Mock theme
const mockTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: { main: '#00ff88' },
    secondary: { main: '#0099ff' },
  },
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={mockTheme}>
    {children}
  </ThemeProvider>
);

// Mock data for tests
const mockProgressStages = [
  {
    id: 'intent',
    name: 'Intent Analysis',
    description: 'Analyzing user request with Grok AI',
    status: 'completed' as const,
    progress: 100,
    duration: 245,
  },
  {
    id: 'symbol',
    name: 'Symbol Extraction',
    description: 'Extracting trading symbols and context',
    status: 'active' as const,
    progress: 75,
  },
];

const mockTerminalMessages = [
  {
    id: '1',
    timestamp: new Date(),
    level: 'info' as const,
    source: 'GROK_AI',
    message: 'Enhanced analysis request received for AAPL',
  },
  {
    id: '2',
    timestamp: new Date(),
    level: 'success' as const,
    source: 'SCANNER',
    message: 'Lee Method signal detected: TSLA bullish pattern',
  },
];

const mockConversationMetrics = {
  totalMessages: 24,
  userMessages: 12,
  assistantMessages: 12,
  averageResponseTime: 1250,
  grokEnhancedResponses: 8,
  successfulTrades: 3,
  riskAlerts: 1,
  sessionDuration: 1847,
};

const mockConversationEvents = [
  {
    id: '1',
    timestamp: new Date(),
    type: 'message' as const,
    severity: 'low' as const,
    description: 'User requested AAPL analysis',
    userId: 'test-user',
  },
];

describe('A.T.L.A.S. Integration Tests', () => {
  // Test 1: Core Chat Interface
  describe('Chat Interface', () => {
    test('renders chat interface with welcome message', () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      expect(screen.getByText(/A.T.L.A.S./)).toBeInTheDocument();
      expect(screen.getByText(/trading assistant/i)).toBeInTheDocument();
    });

    test('displays enhanced AI capabilities in welcome message', () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      expect(screen.getByText(/Grok AI/i)).toBeInTheDocument();
      expect(screen.getByText(/News Insights/i)).toBeInTheDocument();
      expect(screen.getByText(/Web Search/i)).toBeInTheDocument();
      expect(screen.getByText(/Causal Reasoning/i)).toBeInTheDocument();
    });

    test('shows quick action buttons', () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      expect(screen.getByText(/Analyze AAPL with Grok AI/i)).toBeInTheDocument();
      expect(screen.getByText(/Lee Method signals/i)).toBeInTheDocument();
      expect(screen.getByText(/Latest market news/i)).toBeInTheDocument();
    });
  });

  // Test 2: Progress Indicators
  describe('Progress Indicators', () => {
    test('renders progress indicator with stages', () => {
      render(
        <TestWrapper>
          <AtlasProgressIndicator
            stages={mockProgressStages}
            overallProgress={75}
            title="Test Processing"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Test Processing')).toBeInTheDocument();
      expect(screen.getByText('Intent Analysis')).toBeInTheDocument();
      expect(screen.getByText('Symbol Extraction')).toBeInTheDocument();
    });

    test('shows correct progress percentage', () => {
      render(
        <TestWrapper>
          <AtlasProgressIndicator
            stages={mockProgressStages}
            overallProgress={75}
            title="Test Processing"
          />
        </TestWrapper>
      );

      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    test('displays stage details when expanded', async () => {
      render(
        <TestWrapper>
          <AtlasProgressIndicator
            stages={mockProgressStages}
            overallProgress={75}
            title="Test Processing"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Analyzing user request with Grok AI')).toBeInTheDocument();
      expect(screen.getByText('245ms')).toBeInTheDocument();
    });
  });

  // Test 3: Terminal Output
  describe('Terminal Output', () => {
    test('renders terminal with messages', () => {
      render(
        <TestWrapper>
          <AtlasTerminalOutput
            messages={mockTerminalMessages}
            title="System Terminal"
          />
        </TestWrapper>
      );

      expect(screen.getByText('System Terminal')).toBeInTheDocument();
      expect(screen.getByText(/Enhanced analysis request received/)).toBeInTheDocument();
      expect(screen.getByText(/Lee Method signal detected/)).toBeInTheDocument();
    });

    test('shows correct message count', () => {
      render(
        <TestWrapper>
          <AtlasTerminalOutput
            messages={mockTerminalMessages}
            title="System Terminal"
          />
        </TestWrapper>
      );

      expect(screen.getByText('2 lines')).toBeInTheDocument();
    });

    test('displays live indicator when active', () => {
      render(
        <TestWrapper>
          <AtlasTerminalOutput
            messages={mockTerminalMessages}
            isLive={true}
            title="System Terminal"
          />
        </TestWrapper>
      );

      expect(screen.getByText('LIVE')).toBeInTheDocument();
    });
  });

  // Test 4: Conversation Monitor
  describe('Conversation Monitor', () => {
    test('renders conversation metrics', () => {
      render(
        <TestWrapper>
          <AtlasConversationMonitor
            metrics={mockConversationMetrics}
            events={mockConversationEvents}
            sessionId="test-session"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Conversation Monitor')).toBeInTheDocument();
      expect(screen.getByText('24')).toBeInTheDocument(); // Total messages
      expect(screen.getByText('1250ms')).toBeInTheDocument(); // Avg response time
    });

    test('shows session information', () => {
      render(
        <TestWrapper>
          <AtlasConversationMonitor
            metrics={mockConversationMetrics}
            events={mockConversationEvents}
            sessionId="test-session-12345678"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Session: 12345678/)).toBeInTheDocument();
      expect(screen.getByText(/User: test-use/)).toBeInTheDocument();
    });

    test('displays performance indicators', () => {
      render(
        <TestWrapper>
          <AtlasConversationMonitor
            metrics={mockConversationMetrics}
            events={mockConversationEvents}
            sessionId="test-session"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Response Time:')).toBeInTheDocument();
      expect(screen.getByText('AI Enhancement:')).toBeInTheDocument();
      expect(screen.getByText('Risk Level:')).toBeInTheDocument();
    });
  });

  // Test 5: Chart Analysis Panel
  describe('Chart Analysis Panel', () => {
    test('renders chart panel with symbol', () => {
      render(
        <TestWrapper>
          <AtlasChartAnalysisPanel symbol="AAPL" />
        </TestWrapper>
      );

      expect(screen.getByText('Chart & Analysis')).toBeInTheDocument();
      expect(screen.getByDisplayValue('AAPL')).toBeInTheDocument();
    });

    test('shows chart tabs', () => {
      render(
        <TestWrapper>
          <AtlasChartAnalysisPanel symbol="AAPL" />
        </TestWrapper>
      );

      expect(screen.getByText('Chart')).toBeInTheDocument();
      expect(screen.getByText('Technical Analysis')).toBeInTheDocument();
      expect(screen.getByText('Combined View')).toBeInTheDocument();
    });

    test('allows symbol selection', async () => {
      render(
        <TestWrapper>
          <AtlasChartAnalysisPanel symbol="AAPL" />
        </TestWrapper>
      );

      const symbolSelect = screen.getByDisplayValue('AAPL');
      expect(symbolSelect).toBeInTheDocument();
    });
  });

  // Test 6: Status Dashboard
  describe('Status Dashboard', () => {
    test('renders dashboard when visible', () => {
      render(
        <TestWrapper>
          <AtlasStatusDashboard
            isVisible={true}
            sessionId="test-session"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.getByText('A.T.L.A.S. Status')).toBeInTheDocument();
    });

    test('does not render when not visible', () => {
      render(
        <TestWrapper>
          <AtlasStatusDashboard
            isVisible={false}
            sessionId="test-session"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.queryByText('A.T.L.A.S. Status')).not.toBeInTheDocument();
    });

    test('shows system status indicators', () => {
      render(
        <TestWrapper>
          <AtlasStatusDashboard
            isVisible={true}
            sessionId="test-session"
            userId="test-user"
          />
        </TestWrapper>
      );

      expect(screen.getByText('System')).toBeInTheDocument();
      expect(screen.getByText('Grok')).toBeInTheDocument();
      expect(screen.getByText('WS')).toBeInTheDocument();
    });
  });

  // Test 7: Integration Test - Full App
  describe('Full Application Integration', () => {
    test('renders main application without crashing', () => {
      render(
        <TestWrapper>
          <AtlasApp />
        </TestWrapper>
      );

      // Should show loading screen initially
      expect(screen.getByText(/Initializing A.T.L.A.S./)).toBeInTheDocument();
    });

    test('shows all main sections after initialization', async () => {
      render(
        <TestWrapper>
          <AtlasApp />
        </TestWrapper>
      );

      // Wait for initialization to complete
      await waitFor(() => {
        expect(screen.queryByText(/Initializing A.T.L.A.S./)).not.toBeInTheDocument();
      }, { timeout: 10000 });

      // Check for main sections
      expect(screen.getByText(/A.T.L.A.S./)).toBeInTheDocument();
    });
  });

  // Test 8: Feature Preservation Validation
  describe('Feature Preservation', () => {
    test('preserves all documented README features', () => {
      // This test validates that all features mentioned in README are accessible
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      // Core trading features
      expect(screen.getByText(/Trading Analysis/i)).toBeInTheDocument();
      expect(screen.getByText(/Lee Method/i)).toBeInTheDocument();
      expect(screen.getByText(/Market Research/i)).toBeInTheDocument();
      expect(screen.getByText(/Portfolio Management/i)).toBeInTheDocument();
      expect(screen.getByText(/Options Strategies/i)).toBeInTheDocument();

      // Enhanced AI features
      expect(screen.getByText(/Grok AI/i)).toBeInTheDocument();
      expect(screen.getByText(/News Insights/i)).toBeInTheDocument();
      expect(screen.getByText(/Web Search/i)).toBeInTheDocument();
      expect(screen.getByText(/Causal Reasoning/i)).toBeInTheDocument();
    });

    test('maintains 35%+ returns performance standard messaging', () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      // Should reference performance standards
      expect(screen.getByText(/6-point Stock Market God/i)).toBeInTheDocument();
      expect(screen.getByText(/Ultra-responsive/i)).toBeInTheDocument();
    });

    test('preserves real-time scanner capabilities', () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      expect(screen.getByText(/Lee Method/i)).toBeInTheDocument();
      expect(screen.getByText(/real-time/i)).toBeInTheDocument();
      expect(screen.getByText(/pattern detection/i)).toBeInTheDocument();
    });
  });
});
