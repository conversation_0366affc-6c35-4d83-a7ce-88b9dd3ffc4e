<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Alert Management</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .alert-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-group label {
            font-weight: 500;
        }

        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
            color: white;
            backdrop-filter: blur(10px);
        }

        .filter-group select option {
            background: #2a5298;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            backdrop-filter: blur(10px);
        }

        .btn-danger {
            background: linear-gradient(45deg, #f44336, #da190b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .alert-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #4CAF50;
        }

        .stat-card p {
            opacity: 0.9;
        }

        .alerts-container {
            display: grid;
            gap: 15px;
        }

        .alert-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
        }

        .alert-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .alert-item.critical {
            border-left-color: #ff4444;
            background: rgba(255,68,68,0.1);
        }

        .alert-item.high {
            border-left-color: #ff8800;
            background: rgba(255,136,0,0.1);
        }

        .alert-item.medium {
            border-left-color: #4488ff;
            background: rgba(68,136,255,0.1);
        }

        .alert-item.low {
            border-left-color: #888888;
            background: rgba(136,136,136,0.1);
        }

        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .alert-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-title h3 {
            font-size: 1.3rem;
            margin: 0;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .priority-badge.critical {
            background: #ff4444;
            color: white;
        }

        .priority-badge.high {
            background: #ff8800;
            color: white;
        }

        .priority-badge.medium {
            background: #4488ff;
            color: white;
        }

        .priority-badge.low {
            background: #888888;
            color: white;
        }

        .alert-timestamp {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .alert-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .alert-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .alert-field:last-child {
            border-bottom: none;
        }

        .field-label {
            font-weight: 500;
            opacity: 0.8;
        }

        .field-value {
            font-weight: bold;
        }

        .positive {
            color: #4CAF50;
        }

        .negative {
            color: #f44336;
        }

        .alert-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.9rem;
        }

        .snooze-controls {
            display: none;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
            padding: 10px;
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
        }

        .snooze-controls.active {
            display: flex;
        }

        .confidence-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }

        .alert-history {
            margin-top: 30px;
        }

        .history-toggle {
            margin-bottom: 15px;
        }

        .history-content {
            display: none;
            max-height: 400px;
            overflow-y: auto;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 15px;
        }

        .history-content.active {
            display: block;
        }

        .no-alerts {
            text-align: center;
            padding: 40px;
            opacity: 0.7;
        }

        .no-alerts i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .alert-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                justify-content: space-between;
            }

            .action-buttons {
                justify-content: center;
            }

            .alert-content {
                grid-template-columns: 1fr;
            }

            .alert-actions {
                justify-content: center;
            }
        }

        .websocket-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            z-index: 1000;
        }

        .websocket-status.connected {
            background: #4CAF50;
            color: white;
        }

        .websocket-status.disconnected {
            background: #f44336;
            color: white;
        }

        .alert-sound-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(25px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bell"></i> A.T.L.A.S. Alert Management</h1>
            <p>Real-time Lee Method signal alerts and management</p>
        </div>

        <div class="websocket-status" id="websocketStatus">
            <i class="fas fa-circle"></i> Connecting...
        </div>

        <div class="alert-controls">
            <div class="filter-group">
                <label for="priorityFilter">Priority:</label>
                <select id="priorityFilter">
                    <option value="">All Priorities</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>

                <label for="signalTypeFilter">Signal Type:</label>
                <select id="signalTypeFilter">
                    <option value="">All Types</option>
                    <option value="active_decline_reversal_opportunity">Active Decline Reversal</option>
                    <option value="moderate_decline_opportunity">Moderate Decline</option>
                    <option value="bullish_momentum">Bullish Momentum</option>
                    <option value="bearish_momentum">Bearish Momentum</option>
                </select>

                <label for="symbolFilter">Symbol:</label>
                <input type="text" id="symbolFilter" placeholder="Enter symbol...">
            </div>

            <div class="action-buttons">
                <div class="alert-sound-toggle">
                    <label>Alert Sound:</label>
                    <div class="toggle-switch" id="soundToggle"></div>
                </div>
                <button class="btn btn-secondary" onclick="clearAllAlerts()">
                    <i class="fas fa-trash"></i> Clear All
                </button>
                <button class="btn btn-primary" onclick="testAlert()">
                    <i class="fas fa-test-tube"></i> Test Alert
                </button>
            </div>
        </div>

        <div class="alert-stats">
            <div class="stat-card">
                <h3 id="totalAlerts">0</h3>
                <p>Total Active Alerts</p>
            </div>
            <div class="stat-card">
                <h3 id="criticalAlerts">0</h3>
                <p>Critical Alerts</p>
            </div>
            <div class="stat-card">
                <h3 id="todayAlerts">0</h3>
                <p>Alerts Today</p>
            </div>
            <div class="stat-card">
                <h3 id="avgConfidence">0%</h3>
                <p>Average Confidence</p>
            </div>
        </div>

        <div class="alerts-container" id="alertsContainer">
            <div class="no-alerts">
                <i class="fas fa-bell-slash"></i>
                <h3>No Active Alerts</h3>
                <p>When Lee Method signals are detected, they will appear here.</p>
            </div>
        </div>

        <div class="alert-history">
            <button class="btn btn-secondary history-toggle" onclick="toggleHistory()">
                <i class="fas fa-history"></i> Show Alert History
            </button>
            <div class="history-content" id="historyContent">
                <!-- History will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let alerts = [];
        let alertHistory = [];
        let soundEnabled = localStorage.getItem('alertSoundEnabled') !== 'false';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            loadAlerts();
            updateSoundToggle();
            
            // Set up filters
            document.getElementById('priorityFilter').addEventListener('change', filterAlerts);
            document.getElementById('signalTypeFilter').addEventListener('change', filterAlerts);
            document.getElementById('symbolFilter').addEventListener('input', filterAlerts);
            
            // Sound toggle
            document.getElementById('soundToggle').addEventListener('click', toggleSound);
        });

        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/alerts`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                updateWebSocketStatus('connected');
                console.log('WebSocket connected');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'lee_method_alert') {
                    handleNewAlert(data.data);
                }
            };
            
            websocket.onclose = function(event) {
                updateWebSocketStatus('disconnected');
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 5 seconds
                setTimeout(initializeWebSocket, 5000);
            };
            
            websocket.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateWebSocketStatus('disconnected');
            };
        }

        function updateWebSocketStatus(status) {
            const statusElement = document.getElementById('websocketStatus');
            statusElement.className = `websocket-status ${status}`;
            statusElement.innerHTML = status === 'connected' 
                ? '<i class="fas fa-circle"></i> Connected'
                : '<i class="fas fa-circle"></i> Disconnected';
        }

        function handleNewAlert(alertData) {
            alerts.unshift(alertData);
            alertHistory.unshift(alertData);
            
            // Play sound if enabled
            if (soundEnabled) {
                playAlertSound(alertData.priority);
            }
            
            // Show browser notification
            showBrowserNotification(alertData);
            
            renderAlerts();
            updateStats();
        }

        function playAlertSound(priority) {
            // Create audio context and play appropriate sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // Different frequencies for different priorities
            const frequencies = {
                'critical': 800,
                'high': 600,
                'medium': 400,
                'low': 300
            };
            
            oscillator.frequency.setValueAtTime(frequencies[priority] || 400, audioContext.currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        }

        function showBrowserNotification(alertData) {
            if (Notification.permission === 'granted') {
                const notification = new Notification(`A.T.L.A.S. Alert: ${alertData.symbol}`, {
                    body: `${alertData.signal_type.replace(/_/g, ' ')} - $${alertData.current_price.toFixed(2)} (${alertData.percentage_change > 0 ? '+' : ''}${alertData.percentage_change.toFixed(2)}%)`,
                    icon: '/static/favicon.ico',
                    tag: alertData.id
                });
                
                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };
                
                setTimeout(() => notification.close(), 10000);
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission();
            }
        }

        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts/active');
                const data = await response.json();
                alerts = data.alerts || [];
                renderAlerts();
                updateStats();
            } catch (error) {
                console.error('Error loading alerts:', error);
            }
        }

        function renderAlerts() {
            const container = document.getElementById('alertsContainer');
            
            if (alerts.length === 0) {
                container.innerHTML = `
                    <div class="no-alerts">
                        <i class="fas fa-bell-slash"></i>
                        <h3>No Active Alerts</h3>
                        <p>When Lee Method signals are detected, they will appear here.</p>
                    </div>
                `;
                return;
            }
            
            const filteredAlerts = getFilteredAlerts();
            
            container.innerHTML = filteredAlerts.map(alert => `
                <div class="alert-item ${alert.priority}" data-alert-id="${alert.id}">
                    <div class="alert-header">
                        <div class="alert-title">
                            <h3>${alert.symbol}</h3>
                            <span class="priority-badge ${alert.priority}">${alert.priority}</span>
                        </div>
                        <div class="alert-timestamp">
                            ${new Date(alert.timestamp).toLocaleString()}
                        </div>
                    </div>
                    
                    <div class="alert-content">
                        <div class="alert-field">
                            <span class="field-label">Signal Type:</span>
                            <span class="field-value">${alert.signal_type.replace(/_/g, ' ')}</span>
                        </div>
                        <div class="alert-field">
                            <span class="field-label">Current Price:</span>
                            <span class="field-value">$${alert.current_price.toFixed(2)}</span>
                        </div>
                        <div class="alert-field">
                            <span class="field-label">Price Change:</span>
                            <span class="field-value ${alert.percentage_change >= 0 ? 'positive' : 'negative'}">
                                ${alert.percentage_change > 0 ? '+' : ''}${alert.percentage_change.toFixed(2)}%
                            </span>
                        </div>
                        <div class="alert-field">
                            <span class="field-label">TTM Squeeze:</span>
                            <span class="field-value">${alert.ttm_squeeze_status}</span>
                        </div>
                        <div class="alert-field">
                            <span class="field-label">Timeframe:</span>
                            <span class="field-value">${alert.timeframe}</span>
                        </div>
                        <div class="alert-field">
                            <span class="field-label">Scanner Tier:</span>
                            <span class="field-value">${alert.scanner_tier}</span>
                        </div>
                    </div>
                    
                    <div class="alert-field">
                        <span class="field-label">Confidence:</span>
                        <span class="field-value">${(alert.confidence * 100).toFixed(1)}%</span>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${alert.confidence * 100}%"></div>
                    </div>
                    
                    <div class="alert-actions">
                        <button class="btn btn-primary btn-small" onclick="viewAnalysis('${alert.symbol}')">
                            <i class="fas fa-chart-line"></i> Analyze
                        </button>
                        <button class="btn btn-secondary btn-small" onclick="showSnoozeControls('${alert.id}')">
                            <i class="fas fa-clock"></i> Snooze
                        </button>
                        <button class="btn btn-danger btn-small" onclick="dismissAlert('${alert.id}')">
                            <i class="fas fa-times"></i> Dismiss
                        </button>
                    </div>
                    
                    <div class="snooze-controls" id="snooze-${alert.id}">
                        <label>Snooze for:</label>
                        <select id="snooze-duration-${alert.id}">
                            <option value="15">15 minutes</option>
                            <option value="30" selected>30 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="120">2 hours</option>
                            <option value="240">4 hours</option>
                        </select>
                        <button class="btn btn-primary btn-small" onclick="snoozeAlert('${alert.id}')">
                            <i class="fas fa-check"></i> Confirm
                        </button>
                        <button class="btn btn-secondary btn-small" onclick="hideSnoozeControls('${alert.id}')">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function getFilteredAlerts() {
            const priorityFilter = document.getElementById('priorityFilter').value;
            const signalTypeFilter = document.getElementById('signalTypeFilter').value;
            const symbolFilter = document.getElementById('symbolFilter').value.toUpperCase();
            
            return alerts.filter(alert => {
                if (priorityFilter && alert.priority !== priorityFilter) return false;
                if (signalTypeFilter && alert.signal_type !== signalTypeFilter) return false;
                if (symbolFilter && !alert.symbol.includes(symbolFilter)) return false;
                return true;
            });
        }

        function filterAlerts() {
            renderAlerts();
        }

        function updateStats() {
            const totalAlerts = alerts.length;
            const criticalAlerts = alerts.filter(a => a.priority === 'critical').length;
            const todayAlerts = alerts.filter(a => {
                const alertDate = new Date(a.timestamp);
                const today = new Date();
                return alertDate.toDateString() === today.toDateString();
            }).length;
            
            const avgConfidence = totalAlerts > 0 
                ? (alerts.reduce((sum, a) => sum + a.confidence, 0) / totalAlerts * 100).toFixed(0)
                : 0;
            
            document.getElementById('totalAlerts').textContent = totalAlerts;
            document.getElementById('criticalAlerts').textContent = criticalAlerts;
            document.getElementById('todayAlerts').textContent = todayAlerts;
            document.getElementById('avgConfidence').textContent = avgConfidence + '%';
        }

        function viewAnalysis(symbol) {
            window.open(`/analysis/${symbol}`, '_blank');
        }

        function showSnoozeControls(alertId) {
            document.getElementById(`snooze-${alertId}`).classList.add('active');
        }

        function hideSnoozeControls(alertId) {
            document.getElementById(`snooze-${alertId}`).classList.remove('active');
        }

        async function snoozeAlert(alertId) {
            const duration = document.getElementById(`snooze-duration-${alertId}`).value;
            
            try {
                const response = await fetch(`/api/alerts/${alertId}/snooze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ duration_minutes: parseInt(duration) })
                });
                
                if (response.ok) {
                    alerts = alerts.filter(a => a.id !== alertId);
                    renderAlerts();
                    updateStats();
                }
            } catch (error) {
                console.error('Error snoozing alert:', error);
            }
        }

        async function dismissAlert(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/dismiss`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    alerts = alerts.filter(a => a.id !== alertId);
                    renderAlerts();
                    updateStats();
                }
            } catch (error) {
                console.error('Error dismissing alert:', error);
            }
        }

        async function clearAllAlerts() {
            if (confirm('Are you sure you want to clear all alerts?')) {
                try {
                    const response = await fetch('/api/alerts/clear', {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        alerts = [];
                        renderAlerts();
                        updateStats();
                    }
                } catch (error) {
                    console.error('Error clearing alerts:', error);
                }
            }
        }

        async function testAlert() {
            try {
                const response = await fetch('/api/alerts/test', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    console.log('Test alert sent');
                }
            } catch (error) {
                console.error('Error sending test alert:', error);
            }
        }

        function toggleHistory() {
            const content = document.getElementById('historyContent');
            const button = document.querySelector('.history-toggle');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                button.innerHTML = '<i class="fas fa-history"></i> Show Alert History';
            } else {
                content.classList.add('active');
                button.innerHTML = '<i class="fas fa-history"></i> Hide Alert History';
                loadAlertHistory();
            }
        }

        async function loadAlertHistory() {
            try {
                const response = await fetch('/api/alerts/history');
                const data = await response.json();
                const history = data.history || [];
                
                const historyContent = document.getElementById('historyContent');
                historyContent.innerHTML = history.length > 0 
                    ? history.map(alert => `
                        <div class="alert-item ${alert.priority}" style="margin-bottom: 10px;">
                            <div class="alert-header">
                                <div class="alert-title">
                                    <h4>${alert.symbol}</h4>
                                    <span class="priority-badge ${alert.priority}">${alert.priority}</span>
                                </div>
                                <div class="alert-timestamp">
                                    ${new Date(alert.timestamp).toLocaleString()}
                                </div>
                            </div>
                            <p>${alert.signal_type.replace(/_/g, ' ')} - $${alert.current_price.toFixed(2)} (${alert.percentage_change > 0 ? '+' : ''}${alert.percentage_change.toFixed(2)}%)</p>
                        </div>
                    `).join('')
                    : '<p>No alert history available.</p>';
            } catch (error) {
                console.error('Error loading alert history:', error);
            }
        }

        function toggleSound() {
            soundEnabled = !soundEnabled;
            localStorage.setItem('alertSoundEnabled', soundEnabled);
            updateSoundToggle();
        }

        function updateSoundToggle() {
            const toggle = document.getElementById('soundToggle');
            toggle.classList.toggle('active', soundEnabled);
        }
    </script>
</body>
</html>
