"""
A.T.L.A.S. Advanced AI Integration Test
Test suite for advanced AI capabilities (v5.0)
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add current directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Import test components
try:
    from atlas_causal_reasoning import AtlasCausalReasoningEngine
    from atlas_autonomous_agents import AtlasAutonomousAgentManager, MomentumTradingAgent
    from atlas_theory_of_mind import AtlasTheoryOfMindEngine, ParticipantType
    from atlas_ai_core import AtlasAIEngine
    from atlas_orchestrator import AtlasOrchestrator
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    logger.error(f"Import failed: {e}")
    IMPORTS_SUCCESSFUL = False

class AdvancedAITester:
    """Test suite for advanced AI capabilities"""
    
    def __init__(self):
        self.test_results = {}
        self.orchestrator = None
        self.ai_engine = None
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all advanced AI tests"""
        logger.info("=" * 60)
        logger.info("A.T.L.A.S. ADVANCED AI INTEGRATION TEST SUITE")
        logger.info("=" * 60)
        
        if not IMPORTS_SUCCESSFUL:
            return {
                'success': False,
                'error': 'Failed to import required modules',
                'timestamp': datetime.now().isoformat()
            }
        
        # Test individual components
        await self.test_causal_reasoning()
        await self.test_theory_of_mind()
        await self.test_autonomous_agents()
        await self.test_ai_engine_integration()
        await self.test_orchestrator_integration()
        
        # Generate summary
        return self.generate_test_summary()
    
    async def test_causal_reasoning(self):
        """Test causal reasoning engine"""
        logger.info("\n[TEST] Causal Reasoning Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasCausalReasoningEngine()
            await engine.initialize()
            
            # Test causal impact analysis
            intervention = {'sentiment': 0.2, 'volume': 1.5}
            result = await engine.analyze_causal_impact('AAPL', intervention, 5)
            
            # Validate results
            success = (
                'scenario_id' in result and
                'predicted_effects' in result and
                'confidence' in result
            )
            
            self.test_results['causal_reasoning'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'sample_result': result,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Causal reasoning test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Causal reasoning test failed: {e}")
            self.test_results['causal_reasoning'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_theory_of_mind(self):
        """Test theory of mind engine"""
        logger.info("\n[TEST] Theory of Mind Engine")
        logger.info("-" * 40)
        
        try:
            # Initialize engine
            engine = AtlasTheoryOfMindEngine()
            await engine.initialize()
            
            # Test market psychology analysis
            market_data = {
                'price_change_percent': 0.03,
                'volume_ratio': 1.8,
                'volatility': 0.25
            }
            
            sentiment_profile = await engine.analyze_market_psychology('AAPL', market_data)
            
            # Test participant behavior prediction
            prediction = await engine.predict_participant_behavior(
                ParticipantType.RETAIL_TRADER, 'AAPL', {'market_data': market_data}
            )
            
            # Validate results
            success = (
                hasattr(sentiment_profile, 'dominant_emotion') and
                hasattr(sentiment_profile, 'fear_greed_index') and
                hasattr(prediction, 'predicted_action') and
                hasattr(prediction, 'probability')
            )
            
            self.test_results['theory_of_mind'] = {
                'success': success,
                'engine_status': engine.get_engine_status(),
                'sentiment_profile': {
                    'dominant_emotion': sentiment_profile.dominant_emotion.value,
                    'fear_greed_index': sentiment_profile.fear_greed_index,
                    'consensus_direction': sentiment_profile.consensus_direction
                },
                'behavior_prediction': {
                    'participant_type': prediction.participant_type.value,
                    'predicted_action': prediction.predicted_action,
                    'probability': prediction.probability
                },
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Theory of mind test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Theory of mind test failed: {e}")
            self.test_results['theory_of_mind'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_autonomous_agents(self):
        """Test autonomous agents system"""
        logger.info("\n[TEST] Autonomous Agents System")
        logger.info("-" * 40)
        
        try:
            # Initialize agent manager
            manager = AtlasAutonomousAgentManager()
            
            # Create a test agent
            agent = MomentumTradingAgent(
                agent_id="test_momentum_001",
                config={'max_position_size': 100, 'momentum_threshold': 0.02}
            )
            
            # Test agent status
            agent_status = agent.get_agent_status()
            
            # Test manager status
            manager_status = manager.get_agent_manager_status()
            
            # Validate results
            success = (
                agent_status['agent_type'] == 'momentum_trader' and
                agent_status['state'] == 'idle' and
                manager_status['status'] == 'initializing'
            )
            
            self.test_results['autonomous_agents'] = {
                'success': success,
                'agent_status': agent_status,
                'manager_status': manager_status,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Autonomous agents test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Autonomous agents test failed: {e}")
            self.test_results['autonomous_agents'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_ai_engine_integration(self):
        """Test AI engine integration with advanced components"""
        logger.info("\n[TEST] AI Engine Integration")
        logger.info("-" * 40)
        
        try:
            # Initialize AI engine
            self.ai_engine = AtlasAIEngine()
            await self.ai_engine.initialize()
            
            # Test advanced AI status
            advanced_status = self.ai_engine.get_advanced_ai_status()
            
            # Test causal analysis through AI engine
            causal_result = await self.ai_engine.analyze_causal_impact(
                'AAPL', {'sentiment': 0.1}, 3
            )
            
            # Test market psychology through AI engine
            psychology_result = await self.ai_engine.analyze_market_psychology(
                'AAPL', {'price_change_percent': 0.02, 'volume_ratio': 1.3}
            )
            
            # Validate results
            success = (
                advanced_status['advanced_ai_available'] and
                'scenario_id' in causal_result and
                'sentiment_profile' in psychology_result
            )
            
            self.test_results['ai_engine_integration'] = {
                'success': success,
                'advanced_ai_available': advanced_status['advanced_ai_available'],
                'components_count': len(advanced_status.get('components', {})),
                'causal_analysis_working': 'scenario_id' in causal_result,
                'psychology_analysis_working': 'sentiment_profile' in psychology_result,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ AI engine integration test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ AI engine integration test failed: {e}")
            self.test_results['ai_engine_integration'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def test_orchestrator_integration(self):
        """Test orchestrator integration with advanced AI"""
        logger.info("\n[TEST] Orchestrator Integration")
        logger.info("-" * 40)
        
        try:
            # Initialize orchestrator
            self.orchestrator = AtlasOrchestrator()
            await self.orchestrator.initialize()
            
            # Test system status with advanced AI
            system_status = await self.orchestrator.get_system_status()
            
            # Test advanced AI methods through orchestrator
            causal_result = await self.orchestrator.analyze_causal_impact(
                'AAPL', {'sentiment': 0.15}, 4
            )
            
            psychology_result = await self.orchestrator.analyze_market_psychology('AAPL')
            
            # Validate results
            success = (
                'advanced_ai' in system_status and
                causal_result.get('success', False) and
                psychology_result.get('success', False)
            )
            
            self.test_results['orchestrator_integration'] = {
                'success': success,
                'system_status': system_status.get('orchestrator_status'),
                'engines_count': system_status.get('total_engines', 0),
                'advanced_ai_integrated': 'advanced_ai' in system_status,
                'causal_analysis_accessible': causal_result.get('success', False),
                'psychology_analysis_accessible': psychology_result.get('success', False),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✓ Orchestrator integration test: {'PASSED' if success else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"✗ Orchestrator integration test failed: {e}")
            self.test_results['orchestrator_integration'] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def generate_test_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Detailed results
        for test_name, result in self.test_results.items():
            status = "✓ PASSED" if result.get('success', False) else "✗ FAILED"
            logger.info(f"{test_name}: {status}")
            if not result.get('success', False) and 'error' in result:
                logger.info(f"  Error: {result['error']}")
        
        return {
            'success': passed_tests == total_tests,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests/total_tests)*100 if total_tests > 0 else 0,
            'detailed_results': self.test_results,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main test execution"""
    tester = AdvancedAITester()
    results = await tester.run_all_tests()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    print(f"Overall Success: {results['success']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
