"""
A.T.L.A.S. Import Dependencies Validation Script
Validates all imports and identifies circular dependencies or missing modules
"""

import sys
import os
import ast
import importlib
import logging
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from collections import defaultdict, deque

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImportValidator:
    """Validates import dependencies in A.T.L.A.S. codebase"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.python_files = []
        self.import_graph = defaultdict(set)
        self.external_imports = defaultdict(set)
        self.missing_imports = []
        self.circular_dependencies = []
        
    def discover_python_files(self) -> List[Path]:
        """Discover all Python files in the project"""
        python_files = []
        for file_path in self.project_root.glob("*.py"):
            if file_path.name.startswith("__"):
                continue
            python_files.append(file_path)
        
        self.python_files = python_files
        logger.info(f"Discovered {len(python_files)} Python files")
        return python_files
    
    def extract_imports_from_file(self, file_path: Path) -> Dict[str, Any]:
        """Extract all imports from a Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = {
                'local_imports': [],
                'external_imports': [],
                'from_imports': [],
                'import_errors': []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        module_name = alias.name
                        if self.is_local_module(module_name):
                            imports['local_imports'].append(module_name)
                        else:
                            imports['external_imports'].append(module_name)
                
                elif isinstance(node, ast.ImportFrom):
                    module_name = node.module
                    if module_name:
                        if self.is_local_module(module_name):
                            imports['from_imports'].append({
                                'module': module_name,
                                'names': [alias.name for alias in node.names]
                            })
                        else:
                            imports['external_imports'].append(module_name)
            
            return imports
            
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            return {'import_errors': [str(e)]}
    
    def is_local_module(self, module_name: str) -> bool:
        """Check if a module is local to the project"""
        # Check if it's one of our atlas modules
        if module_name.startswith('atlas_'):
            return True
        
        # Check if the file exists in our project
        module_file = self.project_root / f"{module_name}.py"
        return module_file.exists()
    
    def build_dependency_graph(self) -> Dict[str, Set[str]]:
        """Build dependency graph of all modules"""
        for file_path in self.python_files:
            module_name = file_path.stem
            imports = self.extract_imports_from_file(file_path)
            
            # Add local imports to graph
            for local_import in imports.get('local_imports', []):
                self.import_graph[module_name].add(local_import)
            
            # Add from imports to graph
            for from_import in imports.get('from_imports', []):
                self.import_graph[module_name].add(from_import['module'])
            
            # Track external imports
            for ext_import in imports.get('external_imports', []):
                self.external_imports[module_name].add(ext_import)
        
        return dict(self.import_graph)
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies using DFS"""
        def dfs(node, path, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in self.import_graph.get(node, []):
                if neighbor not in visited:
                    cycle = dfs(neighbor, path.copy(), visited, rec_stack)
                    if cycle:
                        return cycle
                elif neighbor in rec_stack:
                    # Found a cycle
                    cycle_start = path.index(neighbor)
                    return path[cycle_start:] + [neighbor]
            
            rec_stack.remove(node)
            return None
        
        visited = set()
        cycles = []
        
        for node in self.import_graph:
            if node not in visited:
                cycle = dfs(node, [], visited, set())
                if cycle and cycle not in cycles:
                    cycles.append(cycle)
        
        self.circular_dependencies = cycles
        return cycles
    
    def validate_external_imports(self) -> List[str]:
        """Validate that all external imports are available"""
        missing = []
        all_external = set()
        
        for module_imports in self.external_imports.values():
            all_external.update(module_imports)
        
        for module_name in all_external:
            try:
                importlib.import_module(module_name)
            except ImportError:
                missing.append(module_name)
        
        self.missing_imports = missing
        return missing
    
    def generate_requirements_txt(self) -> str:
        """Generate requirements.txt based on external imports"""
        # Common package mappings
        package_mappings = {
            'sklearn': 'scikit-learn',
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'yaml': 'PyYAML',
            'dateutil': 'python-dateutil',
            'jwt': 'PyJWT',
            'bs4': 'beautifulsoup4',
            'requests_oauthlib': 'requests-oauthlib',
        }
        
        all_external = set()
        for module_imports in self.external_imports.values():
            all_external.update(module_imports)
        
        # Filter out standard library modules
        stdlib_modules = {
            'os', 'sys', 'json', 'logging', 'datetime', 'time', 'asyncio',
            'typing', 'pathlib', 'collections', 'itertools', 'functools',
            'dataclasses', 'enum', 'abc', 'inspect', 'importlib', 'ast',
            'hashlib', 'base64', 'urllib', 're', 'math', 'random', 'uuid',
            'threading', 'multiprocessing', 'queue', 'socket', 'ssl',
            'http', 'email', 'smtplib', 'sqlite3', 'csv', 'xml', 'html'
        }
        
        external_packages = []
        for module in sorted(all_external):
            if module not in stdlib_modules:
                # Map to correct package name if needed
                package_name = package_mappings.get(module, module)
                if package_name not in external_packages:
                    external_packages.append(package_name)
        
        return '\n'.join(external_packages)
    
    def analyze_import_complexity(self) -> Dict[str, Any]:
        """Analyze import complexity and dependencies"""
        complexity_metrics = {}
        
        for module, dependencies in self.import_graph.items():
            # Calculate various metrics
            direct_deps = len(dependencies)
            
            # Calculate transitive dependencies
            transitive_deps = set()
            queue = deque(dependencies)
            visited = set()
            
            while queue:
                dep = queue.popleft()
                if dep in visited:
                    continue
                visited.add(dep)
                transitive_deps.add(dep)
                
                # Add dependencies of this dependency
                for sub_dep in self.import_graph.get(dep, []):
                    if sub_dep not in visited:
                        queue.append(sub_dep)
            
            complexity_metrics[module] = {
                'direct_dependencies': direct_deps,
                'transitive_dependencies': len(transitive_deps),
                'external_dependencies': len(self.external_imports.get(module, [])),
                'complexity_score': direct_deps + len(transitive_deps) * 0.5
            }
        
        return complexity_metrics
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive import validation"""
        logger.info("Starting comprehensive import validation...")
        
        # Discover files
        self.discover_python_files()
        
        # Build dependency graph
        dependency_graph = self.build_dependency_graph()
        
        # Detect circular dependencies
        circular_deps = self.detect_circular_dependencies()
        
        # Validate external imports
        missing_imports = self.validate_external_imports()
        
        # Analyze complexity
        complexity_metrics = self.analyze_import_complexity()
        
        # Generate requirements
        requirements = self.generate_requirements_txt()
        
        results = {
            'total_files': len(self.python_files),
            'dependency_graph': {k: list(v) for k, v in dependency_graph.items()},
            'circular_dependencies': circular_deps,
            'missing_imports': missing_imports,
            'complexity_metrics': complexity_metrics,
            'external_imports': {k: list(v) for k, v in self.external_imports.items()},
            'generated_requirements': requirements,
            'validation_summary': {
                'has_circular_deps': len(circular_deps) > 0,
                'has_missing_imports': len(missing_imports) > 0,
                'most_complex_module': max(complexity_metrics.items(), 
                                         key=lambda x: x[1]['complexity_score'])[0] if complexity_metrics else None,
                'total_external_packages': len(set().union(*self.external_imports.values())) if self.external_imports else 0
            }
        }
        
        return results
    
    def save_validation_report(self, results: Dict[str, Any], filename: str = "import_validation_report.json"):
        """Save validation results to file"""
        try:
            import json
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Import validation report saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving validation report: {e}")
    
    def save_requirements_file(self, requirements: str, filename: str = "requirements_generated.txt"):
        """Save generated requirements to file"""
        try:
            with open(filename, 'w') as f:
                f.write(requirements)
            logger.info(f"Generated requirements saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving requirements file: {e}")


async def main():
    """Main validation function"""
    validator = ImportValidator()
    
    # Run validation
    results = await validator.run_comprehensive_validation()
    
    # Print summary
    print("\n" + "="*60)
    print("A.T.L.A.S. IMPORT DEPENDENCIES VALIDATION SUMMARY")
    print("="*60)
    print(f"Total Python Files: {results['total_files']}")
    print(f"Circular Dependencies: {len(results['circular_dependencies'])}")
    print(f"Missing Imports: {len(results['missing_imports'])}")
    print(f"Total External Packages: {results['validation_summary']['total_external_packages']}")
    
    if results['circular_dependencies']:
        print("\nCircular Dependencies Found:")
        for i, cycle in enumerate(results['circular_dependencies'], 1):
            print(f"  {i}. {' -> '.join(cycle)}")
    
    if results['missing_imports']:
        print("\nMissing Imports:")
        for missing in results['missing_imports']:
            print(f"  - {missing}")
    
    # Save reports
    validator.save_validation_report(results)
    validator.save_requirements_file(results['generated_requirements'])
    
    print(f"\nReports saved:")
    print("  - import_validation_report.json")
    print("  - requirements_generated.txt")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
