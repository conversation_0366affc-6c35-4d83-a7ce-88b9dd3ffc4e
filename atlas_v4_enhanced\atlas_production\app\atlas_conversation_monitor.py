"""
A.T.L.A.S. Conversation Monitoring System
Active monitoring and quality assurance for all user interactions
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import re

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ConversationIssue(Enum):
    """Types of conversation issues to monitor"""
    INCOMPLETE_RESPONSE = "incomplete_response"
    ERROR_RESPONSE = "error_response"
    LOW_CONFIDENCE = "low_confidence"
    AI_FALLBACK_FAILURE = "ai_fallback_failure"
    SLOW_RESPONSE = "slow_response"
    MISSING_DISCLAIMERS = "missing_disclaimers"
    INAPPROPRIATE_ADVICE = "inappropriate_advice"
    SYSTEM_ERROR = "system_error"
    DATA_FETCH_FAILURE = "data_fetch_failure"
    PATTERN_DETECTION_FAILURE = "pattern_detection_failure"

@dataclass
class ConversationAlert:
    """Alert for conversation issues"""
    alert_id: str
    session_id: str
    user_message: str
    bot_response: str
    issue_type: ConversationIssue
    alert_level: AlertLevel
    description: str
    timestamp: datetime
    metadata: Dict[str, Any]
    resolved: bool = False

@dataclass
class ConversationMetrics:
    """Metrics for conversation quality"""
    session_id: str
    total_messages: int
    successful_responses: int
    failed_responses: int
    average_response_time: float
    average_confidence: float
    ai_provider_usage: Dict[str, int]
    issues_detected: List[ConversationIssue]
    last_activity: datetime

class AtlasConversationMonitor:
    """Real-time conversation monitoring and quality assurance system"""
    
    def __init__(self):
        self.active_sessions: Dict[str, ConversationMetrics] = {}
        self.alerts: List[ConversationAlert] = []
        self.alert_callbacks: List[Callable] = []
        self.logger = logging.getLogger(__name__)
        
        # Quality thresholds
        self.response_time_threshold = 30.0  # seconds
        self.confidence_threshold = 0.6  # minimum confidence
        self.max_response_length = 10000  # characters
        self.min_response_length = 10  # characters
        
        # Financial advice patterns to detect
        self.advice_patterns = [
            r'\byou should (buy|sell|invest|trade)\b',
            r'\bi recommend (buying|selling|investing)\b',
            r'\bbuy (this|that|these|those)\b',
            r'\bsell (this|that|these|those)\b',
            r'\binvest in\b',
            r'\bguaranteed (profit|return|gain)\b'
        ]
        
        # Required disclaimer patterns
        self.disclaimer_patterns = [
            r'not financial advice',
            r'educational purposes',
            r'consult.*financial advisor',
            r'do your own research',
            r'past performance',
            r'investment risk'
        ]
        
        self.logger.info("[MONITOR] Conversation monitoring system initialized")
    
    def start_conversation_tracking(self, session_id: str) -> ConversationMetrics:
        """Start tracking a new conversation session"""
        if session_id not in self.active_sessions:
            metrics = ConversationMetrics(
                session_id=session_id,
                total_messages=0,
                successful_responses=0,
                failed_responses=0,
                average_response_time=0.0,
                average_confidence=0.0,
                ai_provider_usage={},
                issues_detected=[],
                last_activity=datetime.now()
            )
            self.active_sessions[session_id] = metrics
            self.logger.info(f"[MONITOR] Started tracking session {session_id}")
        
        return self.active_sessions[session_id]
    
    async def monitor_conversation(self, session_id: str, user_message: str, 
                                 bot_response: str, response_time: float,
                                 confidence: float, ai_provider: str = None,
                                 context: Dict[str, Any] = None) -> List[ConversationAlert]:
        """Monitor a conversation exchange and detect issues"""
        
        # Get or create session metrics
        metrics = self.start_conversation_tracking(session_id)
        
        # Update metrics
        metrics.total_messages += 1
        metrics.last_activity = datetime.now()
        
        # Update response time average
        if metrics.average_response_time == 0:
            metrics.average_response_time = response_time
        else:
            metrics.average_response_time = (metrics.average_response_time + response_time) / 2
        
        # Update confidence average
        if metrics.average_confidence == 0:
            metrics.average_confidence = confidence
        else:
            metrics.average_confidence = (metrics.average_confidence + confidence) / 2
        
        # Track AI provider usage
        if ai_provider:
            metrics.ai_provider_usage[ai_provider] = metrics.ai_provider_usage.get(ai_provider, 0) + 1
        
        # Detect issues and generate alerts
        alerts = []
        
        # Check for response issues
        if not bot_response or len(bot_response.strip()) < self.min_response_length:
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.INCOMPLETE_RESPONSE,
                AlertLevel.ERROR,
                "Bot response is too short or empty",
                {"response_length": len(bot_response) if bot_response else 0}
            )
            alerts.append(alert)
            metrics.failed_responses += 1
            
        elif "error" in bot_response.lower() or "sorry" in bot_response.lower():
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.ERROR_RESPONSE,
                AlertLevel.WARNING,
                "Bot response indicates an error occurred",
                {"contains_error": True}
            )
            alerts.append(alert)
            metrics.failed_responses += 1
            
        else:
            metrics.successful_responses += 1
        
        # Check response time
        if response_time > self.response_time_threshold:
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.SLOW_RESPONSE,
                AlertLevel.WARNING,
                f"Response time exceeded threshold: {response_time:.2f}s",
                {"response_time": response_time, "threshold": self.response_time_threshold}
            )
            alerts.append(alert)
        
        # Check confidence level
        if confidence < self.confidence_threshold:
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.LOW_CONFIDENCE,
                AlertLevel.WARNING,
                f"Low confidence response: {confidence:.2f}",
                {"confidence": confidence, "threshold": self.confidence_threshold}
            )
            alerts.append(alert)
        
        # Check for inappropriate financial advice
        if self._contains_direct_advice(bot_response):
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.INAPPROPRIATE_ADVICE,
                AlertLevel.CRITICAL,
                "Response contains direct financial advice without proper disclaimers",
                {"advice_detected": True}
            )
            alerts.append(alert)
        
        # Check for missing disclaimers in financial responses
        if self._is_financial_query(user_message) and not self._has_disclaimers(bot_response):
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.MISSING_DISCLAIMERS,
                AlertLevel.WARNING,
                "Financial response missing required disclaimers",
                {"financial_query": True, "has_disclaimers": False}
            )
            alerts.append(alert)
        
        # Check for AI fallback failures
        if context and context.get("ai_fallback_used") and not context.get("ai_fallback_success"):
            alert = await self._create_alert(
                session_id, user_message, bot_response,
                ConversationIssue.AI_FALLBACK_FAILURE,
                AlertLevel.ERROR,
                "AI fallback mechanism failed",
                {"fallback_details": context.get("fallback_details", {})}
            )
            alerts.append(alert)
        
        # Update issues detected
        for alert in alerts:
            if alert.issue_type not in metrics.issues_detected:
                metrics.issues_detected.append(alert.issue_type)
        
        # Store alerts and notify callbacks
        self.alerts.extend(alerts)
        for alert in alerts:
            await self._notify_alert_callbacks(alert)
        
        return alerts
    
    async def _create_alert(self, session_id: str, user_message: str, bot_response: str,
                          issue_type: ConversationIssue, alert_level: AlertLevel,
                          description: str, metadata: Dict[str, Any]) -> ConversationAlert:
        """Create a new conversation alert"""
        alert = ConversationAlert(
            alert_id=f"alert_{int(time.time() * 1000)}",
            session_id=session_id,
            user_message=user_message[:200] + "..." if len(user_message) > 200 else user_message,
            bot_response=bot_response[:200] + "..." if len(bot_response) > 200 else bot_response,
            issue_type=issue_type,
            alert_level=alert_level,
            description=description,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        self.logger.warning(f"[MONITOR] Alert created: {alert_level.value.upper()} - {description} (Session: {session_id})")
        return alert
    
    async def _notify_alert_callbacks(self, alert: ConversationAlert):
        """Notify registered alert callbacks"""
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                self.logger.error(f"Alert callback failed: {e}")
    
    def _contains_direct_advice(self, response: str) -> bool:
        """Check if response contains direct financial advice"""
        response_lower = response.lower()
        for pattern in self.advice_patterns:
            if re.search(pattern, response_lower):
                return True
        return False
    
    def _has_disclaimers(self, response: str) -> bool:
        """Check if response contains appropriate disclaimers"""
        response_lower = response.lower()
        for pattern in self.disclaimer_patterns:
            if re.search(pattern, response_lower):
                return True
        return False
    
    def _is_financial_query(self, message: str) -> bool:
        """Check if user message is asking for financial advice"""
        financial_keywords = [
            'should i buy', 'should i sell', 'should i invest', 'recommend',
            'advice', 'what stock', 'which stock', 'best investment',
            'trading strategy', 'portfolio', 'options trading'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in financial_keywords)
    
    def register_alert_callback(self, callback: Callable):
        """Register a callback for alerts"""
        self.alert_callbacks.append(callback)
        self.logger.info("[MONITOR] Alert callback registered")
    
    def get_session_metrics(self, session_id: str) -> Optional[ConversationMetrics]:
        """Get metrics for a specific session"""
        return self.active_sessions.get(session_id)
    
    def get_recent_alerts(self, hours: int = 24, alert_level: AlertLevel = None) -> List[ConversationAlert]:
        """Get recent alerts within specified timeframe"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_alerts = [
            alert for alert in self.alerts
            if alert.timestamp >= cutoff_time
        ]
        
        if alert_level:
            recent_alerts = [alert for alert in recent_alerts if alert.alert_level == alert_level]
        
        return sorted(recent_alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_system_health_report(self) -> Dict[str, Any]:
        """Generate system health report based on conversation monitoring"""
        total_sessions = len(self.active_sessions)
        recent_alerts = self.get_recent_alerts(hours=1)
        critical_alerts = [a for a in recent_alerts if a.alert_level == AlertLevel.CRITICAL]
        
        # Calculate success rate
        total_messages = sum(m.total_messages for m in self.active_sessions.values())
        successful_messages = sum(m.successful_responses for m in self.active_sessions.values())
        success_rate = (successful_messages / total_messages * 100) if total_messages > 0 else 100
        
        # Calculate average response time
        avg_response_time = sum(m.average_response_time for m in self.active_sessions.values()) / total_sessions if total_sessions > 0 else 0
        
        # Calculate average confidence
        avg_confidence = sum(m.average_confidence for m in self.active_sessions.values()) / total_sessions if total_sessions > 0 else 0
        
        return {
            "timestamp": datetime.now().isoformat(),
            "active_sessions": total_sessions,
            "total_messages": total_messages,
            "success_rate": round(success_rate, 2),
            "average_response_time": round(avg_response_time, 2),
            "average_confidence": round(avg_confidence, 2),
            "recent_alerts": len(recent_alerts),
            "critical_alerts": len(critical_alerts),
            "system_status": "healthy" if len(critical_alerts) == 0 else "issues_detected"
        }

# Global conversation monitor instance
conversation_monitor = AtlasConversationMonitor()
