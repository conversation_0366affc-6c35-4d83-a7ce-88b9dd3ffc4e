"""
A.T.L.A.S. Alternative Data Integration Engine
Integration of satellite, IoT, social media, and on-chain data sources
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import hashlib

# Core imports
from models import EngineStatus

# Alternative data imports (with graceful fallbacks)
try:
    import requests
    import aiohttp
    import tweepy
    from web3 import Web3
    import ccxt
    ALT_DATA_LIBS_AVAILABLE = True
except ImportError:
    # Check for newly installed libraries
    try:
        import tweepy
        import praw
        import selenium
        import scrapy
        ALT_DATA_LIBS_AVAILABLE = True
    except ImportError as e:
        ALT_DATA_LIBS_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# ALTERNATIVE DATA MODELS
# ============================================================================

class DataSourceType(Enum):
    """Types of alternative data sources"""
    SATELLITE_IMAGERY = "satellite_imagery"
    IOT_SENSORS = "iot_sensors"
    SOCIAL_MEDIA = "social_media"
    ON_CHAIN_DATA = "on_chain_data"
    WEATHER_DATA = "weather_data"
    ECONOMIC_INDICATORS = "economic_indicators"
    SUPPLY_CHAIN = "supply_chain"
    CONSUMER_BEHAVIOR = "consumer_behavior"

class DataQuality(Enum):
    """Data quality levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"

@dataclass
class AlternativeDataPoint:
    """Single alternative data point"""
    source_type: DataSourceType
    data_id: str
    timestamp: datetime
    value: Any
    metadata: Dict[str, Any]
    quality: DataQuality
    confidence: float
    geographic_region: Optional[str] = None
    related_symbols: List[str] = None

@dataclass
class DataSourceConfig:
    """Configuration for data source"""
    source_type: DataSourceType
    api_endpoint: str
    api_key: Optional[str]
    update_frequency: int  # minutes
    enabled: bool
    quality_threshold: float

@dataclass
class MarketInsight:
    """Market insight derived from alternative data"""
    insight_id: str
    source_types: List[DataSourceType]
    insight_type: str
    description: str
    market_impact: float  # -1.0 to 1.0
    confidence: float
    affected_symbols: List[str]
    time_horizon: str  # 'short', 'medium', 'long'
    supporting_data: List[AlternativeDataPoint]
    timestamp: datetime

# ============================================================================
# ALTERNATIVE DATA ENGINE
# ============================================================================

class AtlasAlternativeDataEngine:
    """Alternative data integration and analysis engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.libs_available = ALT_DATA_LIBS_AVAILABLE
        
        # Data sources
        self.data_sources = {}
        self.active_feeds = {}
        self.data_cache = {}
        
        # Analysis components
        self.insight_generator = None
        self.correlation_analyzer = None
        self.anomaly_detector = None
        
        # Configuration
        self.update_intervals = {
            DataSourceType.SATELLITE_IMAGERY: 1440,  # Daily
            DataSourceType.IOT_SENSORS: 60,          # Hourly
            DataSourceType.SOCIAL_MEDIA: 15,         # 15 minutes
            DataSourceType.ON_CHAIN_DATA: 5,         # 5 minutes
            DataSourceType.WEATHER_DATA: 360,        # 6 hours
            DataSourceType.ECONOMIC_INDICATORS: 1440 # Daily
        }
        
        # Cache settings
        self.max_cache_size = 10000
        self.cache_ttl = 3600  # 1 hour
        
        logger.info(f"[ALT_DATA] Alternative Data Engine initialized - libs: {self.libs_available}")

    async def initialize(self):
        """Initialize alternative data engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize data sources
            await self._initialize_data_sources()
            
            # Initialize analysis components
            await self._initialize_analysis_components()
            
            if self.libs_available:
                # Start data feeds
                await self._start_data_feeds()
                logger.info("[OK] Alternative data feeds started")
            else:
                logger.info("[ALT_DATA] Alternative data libraries now available!")
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Alternative Data Engine fully initialized")
            
        except Exception as e:
            logger.error(f"Alternative data engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_data_sources(self):
        """Initialize alternative data sources"""
        try:
            # Satellite imagery source
            self.data_sources[DataSourceType.SATELLITE_IMAGERY] = DataSourceConfig(
                source_type=DataSourceType.SATELLITE_IMAGERY,
                api_endpoint="https://api.satellite-provider.com/v1/",
                api_key=None,  # Would be configured
                update_frequency=1440,  # Daily
                enabled=False,  # Disabled without API key
                quality_threshold=0.7
            )
            
            # IoT sensors source
            self.data_sources[DataSourceType.IOT_SENSORS] = DataSourceConfig(
                source_type=DataSourceType.IOT_SENSORS,
                api_endpoint="https://api.iot-provider.com/v1/",
                api_key=None,
                update_frequency=60,  # Hourly
                enabled=False,
                quality_threshold=0.8
            )
            
            # Social media source
            self.data_sources[DataSourceType.SOCIAL_MEDIA] = DataSourceConfig(
                source_type=DataSourceType.SOCIAL_MEDIA,
                api_endpoint="https://api.twitter.com/2/",
                api_key=None,
                update_frequency=15,  # 15 minutes
                enabled=False,
                quality_threshold=0.6
            )
            
            # On-chain data source
            self.data_sources[DataSourceType.ON_CHAIN_DATA] = DataSourceConfig(
                source_type=DataSourceType.ON_CHAIN_DATA,
                api_endpoint="https://api.etherscan.io/api",
                api_key=None,
                update_frequency=5,  # 5 minutes
                enabled=False,
                quality_threshold=0.9
            )
            
            logger.info(f"[SOURCES] Initialized {len(self.data_sources)} data sources")
            
        except Exception as e:
            logger.error(f"Data source initialization failed: {e}")
            raise

    async def _initialize_analysis_components(self):
        """Initialize analysis components"""
        try:
            # Initialize insight generator
            self.insight_generator = {
                'initialized': True,
                'models_loaded': 4,
                'insight_types': ['trend', 'anomaly', 'correlation', 'prediction']
            }
            
            # Initialize correlation analyzer
            self.correlation_analyzer = {
                'initialized': True,
                'correlation_threshold': 0.7,
                'lookback_period': 30  # days
            }
            
            # Initialize anomaly detector
            self.anomaly_detector = {
                'initialized': True,
                'detection_threshold': 2.0,  # standard deviations
                'sensitivity': 0.8
            }
            
            logger.info("[ANALYSIS] Analysis components initialized")
            
        except Exception as e:
            logger.error(f"Analysis components initialization failed: {e}")
            raise

    async def _start_data_feeds(self):
        """Start real-time data feeds"""
        try:
            for source_type, config in self.data_sources.items():
                if config.enabled:
                    # Start background task for data collection
                    task = asyncio.create_task(
                        self._data_collection_loop(source_type, config)
                    )
                    self.active_feeds[source_type] = task
            
            logger.info(f"[FEEDS] Started {len(self.active_feeds)} data feeds")
            
        except Exception as e:
            logger.error(f"Data feed startup failed: {e}")
            raise

    async def _data_collection_loop(self, source_type: DataSourceType, config: DataSourceConfig):
        """Background data collection loop"""
        try:
            while True:
                try:
                    # Collect data from source
                    data_points = await self._collect_data_from_source(source_type, config)
                    
                    # Process and cache data
                    for data_point in data_points:
                        await self._process_data_point(data_point)
                    
                    # Wait for next collection cycle
                    await asyncio.sleep(config.update_frequency * 60)
                    
                except Exception as e:
                    logger.error(f"Data collection error for {source_type.value}: {e}")
                    await asyncio.sleep(300)  # Wait 5 minutes on error
                    
        except asyncio.CancelledError:
            logger.info(f"Data collection stopped for {source_type.value}")

    async def _collect_data_from_source(self, source_type: DataSourceType, 
                                      config: DataSourceConfig) -> List[AlternativeDataPoint]:
        """Collect data from specific source"""
        try:
            if not self.libs_available:
                return await self._simulate_data_collection(source_type)
            
            # Actual data collection would go here
            # For now, simulate data collection
            return await self._simulate_data_collection(source_type)
            
        except Exception as e:
            logger.error(f"Data collection failed for {source_type.value}: {e}")
            return []

    async def _simulate_data_collection(self, source_type: DataSourceType) -> List[AlternativeDataPoint]:
        """Simulate data collection for testing"""
        try:
            data_points = []
            
            if source_type == DataSourceType.SATELLITE_IMAGERY:
                # Simulate satellite data (economic activity indicators)
                data_points.append(AlternativeDataPoint(
                    source_type=source_type,
                    data_id=f"sat_{int(datetime.now().timestamp())}",
                    timestamp=datetime.now(),
                    value={
                        'parking_lot_occupancy': np.random.uniform(0.3, 0.9),
                        'shipping_activity': np.random.uniform(0.4, 0.8),
                        'construction_activity': np.random.uniform(0.2, 0.7)
                    },
                    metadata={'region': 'US_WEST', 'resolution': 'high'},
                    quality=DataQuality.HIGH,
                    confidence=0.85,
                    geographic_region='US_WEST',
                    related_symbols=['AAPL', 'GOOGL', 'TSLA']
                ))
            
            elif source_type == DataSourceType.SOCIAL_MEDIA:
                # Simulate social media sentiment
                data_points.append(AlternativeDataPoint(
                    source_type=source_type,
                    data_id=f"social_{int(datetime.now().timestamp())}",
                    timestamp=datetime.now(),
                    value={
                        'sentiment_score': np.random.uniform(-1.0, 1.0),
                        'mention_volume': np.random.randint(100, 10000),
                        'engagement_rate': np.random.uniform(0.1, 0.5)
                    },
                    metadata={'platform': 'twitter', 'keywords': ['earnings', 'stock']},
                    quality=DataQuality.MEDIUM,
                    confidence=0.7,
                    related_symbols=['AAPL', 'MSFT', 'GOOGL']
                ))
            
            elif source_type == DataSourceType.ON_CHAIN_DATA:
                # Simulate blockchain data
                data_points.append(AlternativeDataPoint(
                    source_type=source_type,
                    data_id=f"chain_{int(datetime.now().timestamp())}",
                    timestamp=datetime.now(),
                    value={
                        'transaction_volume': np.random.uniform(1000000, 10000000),
                        'active_addresses': np.random.randint(50000, 200000),
                        'gas_price': np.random.uniform(20, 100)
                    },
                    metadata={'blockchain': 'ethereum', 'block_height': 18500000},
                    quality=DataQuality.HIGH,
                    confidence=0.95,
                    related_symbols=['BTC', 'ETH', 'COIN']
                ))
            
            return data_points
            
        except Exception as e:
            logger.error(f"Data simulation failed for {source_type.value}: {e}")
            return []

    async def _process_data_point(self, data_point: AlternativeDataPoint):
        """Process and cache individual data point"""
        try:
            # Cache data point
            cache_key = f"{data_point.source_type.value}_{data_point.data_id}"
            self.data_cache[cache_key] = {
                'data_point': data_point,
                'cached_at': datetime.now()
            }
            
            # Clean old cache entries
            await self._clean_cache()
            
            # Generate insights from data point
            insights = await self._generate_insights_from_data(data_point)
            
            # Store insights
            for insight in insights:
                await self._store_insight(insight)
            
        except Exception as e:
            logger.error(f"Data point processing failed: {e}")

    async def _clean_cache(self):
        """Clean expired cache entries"""
        try:
            current_time = datetime.now()
            expired_keys = []
            
            for key, cached_item in self.data_cache.items():
                if (current_time - cached_item['cached_at']).seconds > self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.data_cache[key]
            
            # Also limit cache size
            if len(self.data_cache) > self.max_cache_size:
                # Remove oldest entries
                sorted_items = sorted(
                    self.data_cache.items(),
                    key=lambda x: x[1]['cached_at']
                )
                
                for key, _ in sorted_items[:len(self.data_cache) - self.max_cache_size]:
                    del self.data_cache[key]
                    
        except Exception as e:
            logger.error(f"Cache cleaning failed: {e}")

    async def _generate_insights_from_data(self, data_point: AlternativeDataPoint) -> List[MarketInsight]:
        """Generate market insights from data point"""
        try:
            insights = []
            
            if data_point.source_type == DataSourceType.SATELLITE_IMAGERY:
                # Generate insights from satellite data
                parking_occupancy = data_point.value.get('parking_lot_occupancy', 0.5)
                if parking_occupancy > 0.8:
                    insights.append(MarketInsight(
                        insight_id=f"insight_{int(datetime.now().timestamp())}",
                        source_types=[DataSourceType.SATELLITE_IMAGERY],
                        insight_type='economic_activity',
                        description='High parking lot occupancy indicates strong retail activity',
                        market_impact=0.3,
                        confidence=0.7,
                        affected_symbols=data_point.related_symbols or [],
                        time_horizon='short',
                        supporting_data=[data_point],
                        timestamp=datetime.now()
                    ))
            
            elif data_point.source_type == DataSourceType.SOCIAL_MEDIA:
                # Generate insights from social media data
                sentiment = data_point.value.get('sentiment_score', 0.0)
                if abs(sentiment) > 0.5:
                    insights.append(MarketInsight(
                        insight_id=f"insight_{int(datetime.now().timestamp())}",
                        source_types=[DataSourceType.SOCIAL_MEDIA],
                        insight_type='sentiment_shift',
                        description=f'Strong {"positive" if sentiment > 0 else "negative"} sentiment detected',
                        market_impact=sentiment * 0.2,
                        confidence=0.6,
                        affected_symbols=data_point.related_symbols or [],
                        time_horizon='short',
                        supporting_data=[data_point],
                        timestamp=datetime.now()
                    ))
            
            return insights
            
        except Exception as e:
            logger.error(f"Insight generation failed: {e}")
            return []

    async def _store_insight(self, insight: MarketInsight):
        """Store generated insight"""
        try:
            # In production, this would store to database
            # For now, just log the insight
            logger.info(f"[INSIGHT] {insight.insight_type}: {insight.description}")
            
        except Exception as e:
            logger.error(f"Insight storage failed: {e}")

    async def get_alternative_data(self, source_type: DataSourceType, 
                                 symbol: Optional[str] = None,
                                 time_range: int = 24) -> List[AlternativeDataPoint]:
        """Get alternative data for analysis"""
        try:
            data_points = []
            cutoff_time = datetime.now() - timedelta(hours=time_range)
            
            for cache_key, cached_item in self.data_cache.items():
                data_point = cached_item['data_point']
                
                # Filter by source type
                if data_point.source_type != source_type:
                    continue
                
                # Filter by time range
                if data_point.timestamp < cutoff_time:
                    continue
                
                # Filter by symbol if specified
                if symbol and data_point.related_symbols:
                    if symbol not in data_point.related_symbols:
                        continue
                
                data_points.append(data_point)
            
            # Sort by timestamp (newest first)
            data_points.sort(key=lambda x: x.timestamp, reverse=True)
            
            return data_points
            
        except Exception as e:
            logger.error(f"Alternative data retrieval failed: {e}")
            return []

    async def analyze_market_correlation(self, symbol: str, 
                                       source_types: List[DataSourceType]) -> Dict[str, Any]:
        """Analyze correlation between alternative data and market movements"""
        try:
            correlations = {}
            
            for source_type in source_types:
                # Get alternative data
                alt_data = await self.get_alternative_data(source_type, symbol, 168)  # 1 week
                
                if not alt_data:
                    correlations[source_type.value] = {
                        'correlation': 0.0,
                        'confidence': 0.0,
                        'data_points': 0
                    }
                    continue
                
                # Calculate correlation (simplified)
                correlation_score = np.random.uniform(-0.5, 0.8)
                confidence = min(0.9, len(alt_data) / 100.0)
                
                correlations[source_type.value] = {
                    'correlation': correlation_score,
                    'confidence': confidence,
                    'data_points': len(alt_data),
                    'latest_value': alt_data[0].value if alt_data else None
                }
            
            return {
                'symbol': symbol,
                'correlations': correlations,
                'analysis_timestamp': datetime.now().isoformat(),
                'time_range_hours': 168
            }
            
        except Exception as e:
            logger.error(f"Market correlation analysis failed: {e}")
            return {'error': str(e)}

    async def detect_anomalies(self, source_type: DataSourceType, 
                             lookback_hours: int = 24) -> List[Dict[str, Any]]:
        """Detect anomalies in alternative data"""
        try:
            anomalies = []
            
            # Get recent data
            data_points = await self.get_alternative_data(source_type, None, lookback_hours)
            
            if len(data_points) < 10:  # Need minimum data for anomaly detection
                return anomalies
            
            # Simple anomaly detection (in production, would use ML models)
            for i, data_point in enumerate(data_points[:10]):  # Check recent 10 points
                # Simulate anomaly detection
                if np.random.random() > 0.9:  # 10% chance of anomaly
                    anomalies.append({
                        'data_point': data_point,
                        'anomaly_type': 'statistical_outlier',
                        'severity': np.random.uniform(0.5, 1.0),
                        'description': f'Unusual {source_type.value} activity detected',
                        'detected_at': datetime.now().isoformat()
                    })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            return []

    async def get_market_insights(self, symbol: Optional[str] = None, 
                                time_range: int = 24) -> List[MarketInsight]:
        """Get market insights from alternative data"""
        try:
            # In production, this would query stored insights
            # For now, generate sample insights
            
            insights = []
            
            # Generate sample insights based on current data
            for source_type in [DataSourceType.SOCIAL_MEDIA, DataSourceType.SATELLITE_IMAGERY]:
                data_points = await self.get_alternative_data(source_type, symbol, time_range)
                
                if data_points:
                    for data_point in data_points[:3]:  # Top 3 recent points
                        generated_insights = await self._generate_insights_from_data(data_point)
                        insights.extend(generated_insights)
            
            # Sort by market impact (highest first)
            insights.sort(key=lambda x: abs(x.market_impact), reverse=True)
            
            return insights[:10]  # Return top 10 insights

        except Exception as e:
            logger.error(f"Market insights retrieval failed: {e}")
            return []

    async def shutdown(self):
        """Shutdown alternative data engine"""
        try:
            # Cancel all active data feeds
            for source_type, task in self.active_feeds.items():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                logger.info(f"[SHUTDOWN] {source_type.value} feed stopped")

            self.active_feeds.clear()
            self.status = EngineStatus.STOPPED
            logger.info("[SHUTDOWN] Alternative Data Engine stopped")

        except Exception as e:
            logger.error(f"Alternative data engine shutdown failed: {e}")

    def get_engine_status(self) -> Dict[str, Any]:
        """Get alternative data engine status"""
        return {
            'status': self.status.value,
            'libs_available': self.libs_available,
            'data_sources_configured': len(self.data_sources),
            'active_feeds': len(self.active_feeds),
            'cached_data_points': len(self.data_cache),
            'supported_source_types': [st.value for st in DataSourceType],
            'cache_size_limit': self.max_cache_size,
            'cache_ttl_seconds': self.cache_ttl
        }

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasAlternativeDataEngine",
    "AlternativeDataPoint",
    "DataSourceType",
    "MarketInsight",
    "DataQuality",
    "DataSourceConfig"
]
