#!/usr/bin/env python3
"""
A.T.L.A.S. AI Integration & Fallback Testing
Tests Grok API integration, OpenAI fallback, and static response mechanisms
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class AIIntegrationTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session_id = f"ai_test_session_{int(time.time())}"
        
    async def test_ai_provider_response(self, query: str, expected_provider: str = None):
        """Test AI provider response and identify which AI is being used"""
        print(f"\n🧪 Testing AI Integration: {query}")
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "message": query,
                    "session_id": self.session_id,
                    "user_id": "ai_test_user"
                }
                
                async with session.post(
                    f"{self.base_url}/api/v1/chat",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=45)
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        response_text = data.get("response", "")
                        context = data.get("context", {})
                        
                        # Try to identify AI provider from context or response characteristics
                        ai_provider = context.get("ai_provider", "unknown")
                        metadata = context.get("metadata", {})
                        
                        print(f"✅ Success ({response_time:.2f}s)")
                        print(f"🤖 AI Provider: {ai_provider}")
                        print(f"🎯 Confidence: {data.get('confidence', 0):.2f}")
                        print(f"📝 Type: {data.get('type', 'unknown')}")
                        print(f"📊 Metadata: {metadata}")
                        
                        # Response quality indicators
                        response_length = len(response_text)
                        print(f"💬 Response Length: {response_length} chars")
                        
                        # Check for AI-specific patterns
                        ai_indicators = self.analyze_ai_patterns(response_text)
                        print(f"🔍 AI Indicators: {ai_indicators}")
                        
                        return {
                            "success": True,
                            "ai_provider": ai_provider,
                            "response": response_text,
                            "response_time": response_time,
                            "confidence": data.get("confidence", 0),
                            "type": data.get("type", "unknown"),
                            "metadata": metadata,
                            "ai_indicators": ai_indicators,
                            "response_length": response_length
                        }
                    else:
                        print(f"❌ HTTP Error: {response.status}")
                        return {"success": False, "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            response_time = time.time() - start_time
            print(f"❌ Exception: {str(e)} ({response_time:.2f}s)")
            return {"success": False, "error": str(e)}
    
    def analyze_ai_patterns(self, response: str) -> dict:
        """Analyze response patterns to identify AI characteristics"""
        patterns = {
            "grok_indicators": 0,
            "openai_indicators": 0,
            "static_indicators": 0,
            "advanced_reasoning": 0,
            "financial_expertise": 0
        }
        
        response_lower = response.lower()
        
        # Grok-specific patterns (more conversational, direct)
        grok_patterns = ["let me", "i'll", "right now", "real-time", "pulling data"]
        for pattern in grok_patterns:
            if pattern in response_lower:
                patterns["grok_indicators"] += 1
        
        # OpenAI-specific patterns (more structured, formal)
        openai_patterns = ["as an ai", "i should note", "it's important to", "please note"]
        for pattern in openai_patterns:
            if pattern in response_lower:
                patterns["openai_indicators"] += 1
        
        # Static response patterns
        static_patterns = ["welcome to a.t.l.a.s.", "trading & learning analysis system"]
        for pattern in static_patterns:
            if pattern in response_lower:
                patterns["static_indicators"] += 1
        
        # Advanced reasoning indicators
        reasoning_patterns = ["analysis", "comprehensive", "data-driven", "methodology"]
        for pattern in reasoning_patterns:
            if pattern in response_lower:
                patterns["advanced_reasoning"] += 1
        
        # Financial expertise indicators
        financial_patterns = ["rsi", "macd", "support", "resistance", "trading", "market"]
        for pattern in financial_patterns:
            if pattern in response_lower:
                patterns["financial_expertise"] += 1
        
        return patterns

async def main():
    tester = AIIntegrationTester()
    
    # Test queries designed to trigger different AI providers and capabilities
    test_scenarios = [
        {
            "query": "Hello, test Grok AI integration",
            "description": "Basic Grok test",
            "expected_provider": "grok"
        },
        {
            "query": "Use advanced AI reasoning to analyze market volatility patterns",
            "description": "Advanced AI capabilities test",
            "expected_provider": "grok"
        },
        {
            "query": "What does Grok think about current market conditions?",
            "description": "Explicit Grok request",
            "expected_provider": "grok"
        },
        {
            "query": "Provide a comprehensive technical analysis using machine learning",
            "description": "Complex analysis request",
            "expected_provider": "grok"
        },
        {
            "query": "Hello",
            "description": "Simple greeting (may use static response)",
            "expected_provider": "static"
        },
        {
            "query": "What is A.T.L.A.S.?",
            "description": "System information (may use static response)",
            "expected_provider": "static"
        },
        {
            "query": "Explain quantum computing in trading" * 10,  # Very long query
            "description": "Stress test with long query",
            "expected_provider": "any"
        }
    ]
    
    print("🚀 Starting A.T.L.A.S. AI Integration & Fallback Testing")
    print("="*70)
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 Test {i}/{len(test_scenarios)}: {scenario['description']}")
        print(f"🎯 Expected Provider: {scenario['expected_provider']}")
        
        result = await tester.test_ai_provider_response(
            scenario["query"], 
            scenario["expected_provider"]
        )
        
        result.update({
            "test_scenario": scenario["description"],
            "expected_provider": scenario["expected_provider"],
            "query": scenario["query"],
            "timestamp": datetime.now().isoformat()
        })
        
        results.append(result)
        
        # Brief pause between tests
        await asyncio.sleep(3)
        print("-" * 70)
    
    # Analysis and Summary
    print("\n📊 AI INTEGRATION TEST SUMMARY")
    print("="*70)
    
    successful_tests = [r for r in results if r.get("success")]
    failed_tests = [r for r in results if not r.get("success")]
    
    print(f"✅ Successful Tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed Tests: {len(failed_tests)}")
    
    if successful_tests:
        # AI Provider Analysis
        providers = {}
        for result in successful_tests:
            provider = result.get("ai_provider", "unknown")
            providers[provider] = providers.get(provider, 0) + 1
        
        print(f"\n🤖 AI Provider Distribution:")
        for provider, count in providers.items():
            percentage = (count / len(successful_tests)) * 100
            print(f"   {provider}: {count} responses ({percentage:.1f}%)")
        
        # Performance Analysis
        avg_response_time = sum(r.get("response_time", 0) for r in successful_tests) / len(successful_tests)
        avg_confidence = sum(r.get("confidence", 0) for r in successful_tests) / len(successful_tests)
        
        print(f"\n⚡ Performance Metrics:")
        print(f"   Average Response Time: {avg_response_time:.2f}s")
        print(f"   Average Confidence: {avg_confidence:.2f}")
        
        # AI Pattern Analysis
        total_patterns = {
            "grok_indicators": 0,
            "openai_indicators": 0,
            "static_indicators": 0,
            "advanced_reasoning": 0,
            "financial_expertise": 0
        }
        
        for result in successful_tests:
            indicators = result.get("ai_indicators", {})
            for key, value in indicators.items():
                total_patterns[key] += value
        
        print(f"\n🔍 AI Pattern Analysis:")
        for pattern, count in total_patterns.items():
            print(f"   {pattern}: {count} occurrences")
        
        # Fallback Testing Results
        grok_responses = sum(1 for r in successful_tests if "grok" in r.get("ai_provider", "").lower())
        openai_responses = sum(1 for r in successful_tests if "openai" in r.get("ai_provider", "").lower())
        static_responses = sum(1 for r in successful_tests if r.get("ai_indicators", {}).get("static_indicators", 0) > 0)
        
        print(f"\n🔄 Fallback Mechanism Analysis:")
        print(f"   Grok Responses: {grok_responses}")
        print(f"   OpenAI Fallback: {openai_responses}")
        print(f"   Static Responses: {static_responses}")
        
        if openai_responses > 0:
            print("   ✅ Fallback mechanism is working - OpenAI was used when needed")
        else:
            print("   ⚠️  No OpenAI fallback detected in this test run")
    
    if failed_tests:
        print(f"\n❌ Failed Test Details:")
        for test in failed_tests:
            print(f"   Scenario: {test.get('test_scenario', 'Unknown')}")
            print(f"   Error: {test.get('error', 'Unknown')}")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"atlas_ai_integration_test_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {filename}")
    print("🎉 AI Integration Testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
