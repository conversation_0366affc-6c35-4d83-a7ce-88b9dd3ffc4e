# A.T.L.A.S. Multi-Agent System - Production Environment Configuration

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
PYTHONPATH=/app
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================
HOST=0.0.0.0
PORT=8001
WORKERS=4
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_CONNECTIONS=1000
MAX_REQUESTS=10000
MAX_REQUESTS_JITTER=1000
TIMEOUT=300
KEEPALIVE=5

# ============================================================================
# MULTI-AGENT SYSTEM CONFIGURATION
# ============================================================================
ORCHESTRATION_MODE=hybrid
MAX_CONCURRENT_REQUESTS=100
AGENT_TIMEOUT_SECONDS=300
AGENT_RETRY_ATTEMPTS=3
AGENT_RETRY_DELAY=5

# Agent-specific settings
DATA_VALIDATOR_TIMEOUT=60
PATTERN_DETECTOR_TIMEOUT=120
ANALYSIS_AGENT_TIMEOUT=180
RISK_MANAGER_TIMEOUT=90
TRADE_EXECUTOR_TIMEOUT=60
VALIDATION_AGENT_TIMEOUT=120

# ============================================================================
# MONITORING AND METRICS
# ============================================================================
PROMETHEUS_PORT=8000
PROMETHEUS_ENABLED=true
HEALTH_CHECK_INTERVAL=30
METRICS_COLLECTION_INTERVAL=60
PERFORMANCE_MONITORING_ENABLED=true

# Alert thresholds
CPU_WARNING_THRESHOLD=80
CPU_CRITICAL_THRESHOLD=95
MEMORY_WARNING_THRESHOLD=80
MEMORY_CRITICAL_THRESHOLD=95
RESPONSE_TIME_WARNING=5.0
RESPONSE_TIME_CRITICAL=10.0

# ============================================================================
# SECURITY AND COMPLIANCE
# ============================================================================
SESSION_TIMEOUT=3600
MAX_FAILED_ATTEMPTS=5
RATE_LIMIT_WINDOW=60
MAX_REQUESTS_PER_WINDOW=100
AUDIT_LOG_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=90
COMPLIANCE_CHECKS_ENABLED=true

# Encryption settings
ENCRYPTION_ENABLED=true
API_KEY_ENCRYPTION_ENABLED=true
AUDIT_LOG_ENCRYPTION_ENABLED=true

# ============================================================================
# DATABASE CONFIGURATION (if using)
# ============================================================================
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_SIZE=10000
CACHE_TYPE=memory

# Redis configuration (if using)
REDIS_HOST=redis-service
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD_FILE=/run/secrets/redis_password
REDIS_SSL=false
REDIS_TIMEOUT=5

# ============================================================================
# TRADING CONFIGURATION
# ============================================================================
# Risk management
MAX_POSITION_SIZE=0.1
MAX_DAILY_VAR=0.02
MAX_PORTFOLIO_RISK=0.05
POSITION_SIZE_LIMIT_ENABLED=true
VAR_LIMIT_ENABLED=true

# Trading hours (UTC)
MARKET_OPEN_TIME=14:30
MARKET_CLOSE_TIME=21:00
AFTER_HOURS_TRADING_ENABLED=false

# Compliance
COMPLIANCE_CHECK_ENABLED=true
TRADE_LOGGING_ENABLED=true
REGULATORY_REPORTING_ENABLED=true

# ============================================================================
# EXTERNAL API CONFIGURATION
# ============================================================================
# Grok API
GROK_API_TIMEOUT=30
GROK_MAX_RETRIES=3
GROK_RETRY_DELAY=5
GROK_RATE_LIMIT=100

# Alpaca API
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_TIMEOUT=30
ALPACA_MAX_RETRIES=3

# Financial Modeling Prep API
FMP_BASE_URL=https://financialmodelingprep.com/api/v3
FMP_TIMEOUT=30
FMP_MAX_RETRIES=3

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
LOG_FORMAT=json
LOG_FILE=/app/logs/atlas.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_ROTATION_ENABLED=true

# Structured logging
STRUCTURED_LOGGING=true
LOG_CORRELATION_ID=true
LOG_REQUEST_ID=true
LOG_USER_ID=true

# Log levels by component
LOG_LEVEL_ORCHESTRATOR=INFO
LOG_LEVEL_AGENTS=INFO
LOG_LEVEL_SECURITY=INFO
LOG_LEVEL_MONITORING=INFO
LOG_LEVEL_GROK=INFO

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================
# Connection pooling
HTTP_POOL_CONNECTIONS=100
HTTP_POOL_MAXSIZE=100
HTTP_MAX_RETRIES=3

# Async settings
ASYNC_POOL_SIZE=100
ASYNC_MAX_WORKERS=50

# Memory management
MAX_MEMORY_USAGE=**********  # 4GB
GARBAGE_COLLECTION_THRESHOLD=1000

# ============================================================================
# KUBERNETES INTEGRATION
# ============================================================================
KUBERNETES_NAMESPACE=atlas-trading
KUBERNETES_SERVICE_NAME=atlas-service
KUBERNETES_POD_NAME=${HOSTNAME}
KUBERNETES_CLUSTER_NAME=production

# Health checks
HEALTH_CHECK_PATH=/api/v1/monitoring/health
READINESS_CHECK_PATH=/api/v1/monitoring/health
LIVENESS_CHECK_PATH=/api/v1/monitoring/health

# ============================================================================
# FEATURE FLAGS
# ============================================================================
FEATURE_MULTI_AGENT_ENABLED=true
FEATURE_PATTERN_DETECTION_ENABLED=true
FEATURE_SENTIMENT_ANALYSIS_ENABLED=true
FEATURE_RISK_MANAGEMENT_ENABLED=true
FEATURE_TRADE_EXECUTION_ENABLED=true
FEATURE_COMPLIANCE_ENABLED=true
FEATURE_MONITORING_ENABLED=true
FEATURE_SECURITY_ENABLED=true

# Experimental features
FEATURE_ADVANCED_ANALYTICS_ENABLED=false
FEATURE_ML_PREDICTIONS_ENABLED=false
FEATURE_AUTOMATED_TRADING_ENABLED=false

# ============================================================================
# BACKUP AND RECOVERY
# ============================================================================
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600  # 1 hour
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/app/backups

# ============================================================================
# ALERTING CONFIGURATION
# ============================================================================
ALERTING_ENABLED=true
ALERT_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL_ENABLED=false
ALERT_SMS_ENABLED=false

# Alert channels
CRITICAL_ALERTS_CHANNEL=critical-alerts
WARNING_ALERTS_CHANNEL=warnings
INFO_ALERTS_CHANNEL=info

# ============================================================================
# DEVELOPMENT AND TESTING
# ============================================================================
# Only for non-production environments
TESTING_MODE=false
MOCK_EXTERNAL_APIS=false
SIMULATION_MODE=false
PAPER_TRADING_ENABLED=true

# ============================================================================
# SECRETS MANAGEMENT
# ============================================================================
# Paths to secret files (mounted by Kubernetes)
GROK_API_KEY_FILE=/run/secrets/grok_api_key
ALPACA_API_KEY_FILE=/run/secrets/alpaca_api_key
ALPACA_SECRET_KEY_FILE=/run/secrets/alpaca_secret_key
FMP_API_KEY_FILE=/run/secrets/fmp_api_key
JWT_SECRET_KEY_FILE=/run/secrets/jwt_secret_key
ENCRYPTION_KEY_FILE=/run/secrets/encryption_key

# Database credentials
DB_USERNAME_FILE=/run/secrets/db_username
DB_PASSWORD_FILE=/run/secrets/db_password
