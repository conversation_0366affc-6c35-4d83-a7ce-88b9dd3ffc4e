"""
A.T.L.A.S. Terminal Output Streamer
Stream backend processing information to web interface for complete transparency
"""

import asyncio
import json
import logging
import sys
import io
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from contextlib import redirect_stdout, redirect_stderr
import threading
from queue import Queue, Empty

logger = logging.getLogger(__name__)

class TerminalCapture:
    """Capture terminal output and stream to web interface"""
    
    def __init__(self):
        self.output_queue = Queue()
        self.websocket_connections: Dict[str, Any] = {}
        self.capture_active = False
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.captured_output = []
        self.max_output_lines = 1000  # Keep last 1000 lines
        
        # Custom stream that captures output
        self.capture_stream = self.CaptureStream(self.output_queue)
        
        logger.info("[TERMINAL] Terminal output streamer initialized")
    
    class CaptureStream(io.StringIO):
        """Custom stream that captures output and queues it"""
        
        def __init__(self, output_queue: Queue):
            super().__init__()
            self.output_queue = output_queue
            self.buffer = ""
        
        def write(self, text: str) -> int:
            # Write to original stdout/stderr
            sys.__stdout__.write(text)
            sys.__stdout__.flush()
            
            # Queue for web streaming
            if text.strip():  # Only queue non-empty lines
                self.output_queue.put({
                    "type": "terminal_output",
                    "content": text.strip(),
                    "timestamp": datetime.now().isoformat(),
                    "level": "info"
                })
            
            return len(text)
        
        def flush(self):
            sys.__stdout__.flush()
    
    def start_capture(self):
        """Start capturing terminal output"""
        if not self.capture_active:
            self.capture_active = True
            
            # Start background thread to process output queue
            self.output_thread = threading.Thread(target=self._process_output_queue, daemon=True)
            self.output_thread.start()
            
            logger.info("[TERMINAL] Started terminal output capture")
    
    def stop_capture(self):
        """Stop capturing terminal output"""
        if self.capture_active:
            self.capture_active = False
            logger.info("[TERMINAL] Stopped terminal output capture")
    
    def _process_output_queue(self):
        """Process output queue and send to WebSocket connections"""
        while self.capture_active:
            try:
                # Get output from queue with timeout
                output_data = self.output_queue.get(timeout=1.0)
                
                # Add to captured output history
                self.captured_output.append(output_data)
                
                # Keep only last max_output_lines
                if len(self.captured_output) > self.max_output_lines:
                    self.captured_output = self.captured_output[-self.max_output_lines:]
                
                # Send to all connected WebSocket clients
                asyncio.create_task(self._broadcast_output(output_data))
                
            except Empty:
                continue
            except Exception as e:
                logger.error(f"Error processing output queue: {e}")
    
    async def _broadcast_output(self, output_data: Dict[str, Any]):
        """Broadcast output to all WebSocket connections"""
        disconnected_sessions = []
        
        for session_id, websocket in self.websocket_connections.items():
            try:
                await websocket.send_text(json.dumps(output_data))
            except Exception as e:
                logger.warning(f"Failed to send output to session {session_id}: {e}")
                disconnected_sessions.append(session_id)
        
        # Clean up disconnected sessions
        for session_id in disconnected_sessions:
            self.websocket_connections.pop(session_id, None)
    
    def register_websocket(self, session_id: str, websocket: Any):
        """Register a WebSocket connection for terminal output"""
        self.websocket_connections[session_id] = websocket
        logger.info(f"[TERMINAL] Registered WebSocket for terminal output: {session_id}")
        
        # Send recent output history to new connection
        asyncio.create_task(self._send_output_history(websocket))
    
    def unregister_websocket(self, session_id: str):
        """Unregister a WebSocket connection"""
        if session_id in self.websocket_connections:
            del self.websocket_connections[session_id]
            logger.info(f"[TERMINAL] Unregistered WebSocket for terminal output: {session_id}")
    
    async def _send_output_history(self, websocket: Any):
        """Send recent output history to a new WebSocket connection"""
        try:
            # Send last 50 lines of output
            recent_output = self.captured_output[-50:] if len(self.captured_output) > 50 else self.captured_output
            
            for output_data in recent_output:
                await websocket.send_text(json.dumps(output_data))
                
        except Exception as e:
            logger.error(f"Failed to send output history: {e}")
    
    def log_operation(self, operation: str, details: str = "", level: str = "info"):
        """Log an operation with details for web interface"""
        log_data = {
            "type": "operation_log",
            "operation": operation,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "level": level
        }
        
        # Add to queue for web streaming
        self.output_queue.put(log_data)
        
        # Also log to standard logger
        if level == "error":
            logger.error(f"[OPERATION] {operation}: {details}")
        elif level == "warning":
            logger.warning(f"[OPERATION] {operation}: {details}")
        else:
            logger.info(f"[OPERATION] {operation}: {details}")
    
    def log_market_data_fetch(self, symbol: str, status: str, details: str = ""):
        """Log market data fetch operation"""
        self.log_operation(
            f"Fetching market data for {symbol}",
            f"Status: {status}. {details}",
            "info" if status == "success" else "warning"
        )
    
    def log_technical_analysis(self, symbol: str, indicators: List[str], status: str):
        """Log technical analysis operation"""
        self.log_operation(
            f"Technical analysis for {symbol}",
            f"Indicators: {', '.join(indicators)}. Status: {status}",
            "info" if status == "success" else "warning"
        )
    
    def log_lee_method_scan(self, symbol: str, pattern_found: bool, confidence: float):
        """Log Lee Method pattern detection"""
        status = "Pattern detected" if pattern_found else "No pattern"
        self.log_operation(
            f"Lee Method scan for {symbol}",
            f"{status}. Confidence: {confidence:.2f}",
            "info"
        )
    
    def log_options_analysis(self, symbol: str, contracts_analyzed: int, opportunities: int):
        """Log options analysis operation"""
        self.log_operation(
            f"Options analysis for {symbol}",
            f"Analyzed {contracts_analyzed} contracts, found {opportunities} opportunities",
            "info"
        )
    
    def log_ai_processing(self, query: str, ai_provider: str, response_time: float, success: bool):
        """Log AI processing operation"""
        status = "success" if success else "failed"
        self.log_operation(
            f"AI processing via {ai_provider}",
            f"Query: {query[:50]}... Response time: {response_time:.2f}s. Status: {status}",
            "info" if success else "error"
        )
    
    def log_trading_recommendation(self, symbol: str, action: str, confidence: float, risk_level: str):
        """Log trading recommendation generation"""
        self.log_operation(
            f"Trading recommendation for {symbol}",
            f"Action: {action}. Confidence: {confidence:.2f}. Risk: {risk_level}",
            "info"
        )
    
    def get_recent_output(self, lines: int = 100) -> List[Dict[str, Any]]:
        """Get recent terminal output"""
        return self.captured_output[-lines:] if len(self.captured_output) > lines else self.captured_output

class LoggingHandler(logging.Handler):
    """Custom logging handler that streams to web interface"""
    
    def __init__(self, terminal_streamer: TerminalCapture):
        super().__init__()
        self.terminal_streamer = terminal_streamer
    
    def emit(self, record: logging.LogRecord):
        """Emit a log record to the terminal streamer"""
        try:
            # Format the log message
            message = self.format(record)
            
            # Determine log level
            level_map = {
                logging.DEBUG: "debug",
                logging.INFO: "info", 
                logging.WARNING: "warning",
                logging.ERROR: "error",
                logging.CRITICAL: "critical"
            }
            
            level = level_map.get(record.levelno, "info")
            
            # Queue for web streaming
            log_data = {
                "type": "log_message",
                "content": message,
                "level": level,
                "logger": record.name,
                "timestamp": datetime.now().isoformat()
            }
            
            self.terminal_streamer.output_queue.put(log_data)
            
        except Exception:
            self.handleError(record)

# Global terminal streamer instance
terminal_streamer = TerminalCapture()

def setup_terminal_streaming():
    """Setup terminal streaming for the application"""
    # Start terminal capture
    terminal_streamer.start_capture()
    
    # Add custom logging handler to root logger
    root_logger = logging.getLogger()
    handler = LoggingHandler(terminal_streamer)
    handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    root_logger.addHandler(handler)
    
    logger.info("[TERMINAL] Terminal streaming setup complete")

def log_detailed_operation(operation: str, symbol: str = "", details: str = "", level: str = "info"):
    """Convenience function for detailed operation logging"""
    if symbol:
        operation = f"{operation} for {symbol}"
    
    terminal_streamer.log_operation(operation, details, level)
