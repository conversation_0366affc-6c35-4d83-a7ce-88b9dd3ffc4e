"""
A.T.L.A.S. Ethical AI and Bias Auditing Engine
Fairness assessment, bias detection, and ethical AI governance
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

# Core imports
from models import EngineStatus

# Fairness and bias detection imports (with graceful fallbacks)
try:
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.model_selection import train_test_split
    import scipy.stats as stats
    FAIRNESS_LIBS_AVAILABLE = True
except ImportError:
    FAIRNESS_LIBS_AVAILABLE = False

# Grok integration bias auditing (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# ETHICAL AI MODELS
# ============================================================================

class BiasType(Enum):
    """Types of bias to detect"""
    DEMOGRAPHIC_PARITY = "demographic_parity"
    EQUALIZED_ODDS = "equalized_odds"
    EQUAL_OPPORTUNITY = "equal_opportunity"
    CALIBRATION = "calibration"
    INDIVIDUAL_FAIRNESS = "individual_fairness"
    COUNTERFACTUAL_FAIRNESS = "counterfactual_fairness"

class ProtectedAttribute(Enum):
    """Protected attributes for fairness assessment"""
    AGE = "age"
    GENDER = "gender"
    RACE = "race"
    INCOME_LEVEL = "income_level"
    GEOGRAPHIC_LOCATION = "geographic_location"
    ACCOUNT_SIZE = "account_size"
    TRADING_EXPERIENCE = "trading_experience"

class EthicalPrinciple(Enum):
    """Ethical AI principles"""
    FAIRNESS = "fairness"
    TRANSPARENCY = "transparency"
    ACCOUNTABILITY = "accountability"
    PRIVACY = "privacy"
    HUMAN_AUTONOMY = "human_autonomy"
    NON_MALEFICENCE = "non_maleficence"
    BENEFICENCE = "beneficence"

class AuditSeverity(Enum):
    """Severity levels for ethical issues"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class BiasAssessment:
    """Bias assessment result"""
    assessment_id: str
    model_id: str
    bias_type: BiasType
    protected_attribute: ProtectedAttribute
    bias_score: float  # 0.0 = no bias, 1.0 = maximum bias
    statistical_significance: float
    affected_groups: List[str]
    mitigation_recommendations: List[str]
    timestamp: datetime

@dataclass
class FairnessMetrics:
    """Comprehensive fairness metrics"""
    demographic_parity_difference: float
    equalized_odds_difference: float
    equal_opportunity_difference: float
    calibration_difference: float
    individual_fairness_score: float
    overall_fairness_score: float
    group_metrics: Dict[str, Dict[str, float]]

@dataclass
class EthicalAuditReport:
    """Comprehensive ethical audit report"""
    report_id: str
    model_id: str
    audit_timestamp: datetime
    ethical_principles_assessment: Dict[EthicalPrinciple, float]
    bias_assessments: List[BiasAssessment]
    fairness_metrics: FairnessMetrics
    transparency_score: float
    accountability_measures: List[str]
    recommendations: List[str]
    overall_ethical_score: float
    severity_level: AuditSeverity

@dataclass
class MitigationStrategy:
    """Bias mitigation strategy"""
    strategy_id: str
    bias_type: BiasType
    mitigation_method: str
    implementation_steps: List[str]
    expected_improvement: float
    computational_cost: str
    trade_offs: List[str]

# ============================================================================
# ETHICAL AI ENGINE
# ============================================================================

class AtlasEthicalAIEngine:
    """Ethical AI and bias auditing engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.fairness_libs_available = FAIRNESS_LIBS_AVAILABLE
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE

        # Ethical AI components
        self.bias_detectors = {}
        self.fairness_assessors = {}
        self.mitigation_strategies = {}

        # Audit history
        self.audit_reports = {}
        self.bias_assessments = {}

        # Grok bias auditing
        self.grok_bias_assessments = {}
        self.grok_output_analysis = []
        self.bias_detection_patterns = {
            'gender_bias': ['he/she', 'his/her', 'man/woman', 'male/female'],
            'racial_bias': ['race', 'ethnicity', 'nationality', 'cultural'],
            'age_bias': ['young', 'old', 'elderly', 'millennial', 'boomer'],
            'economic_bias': ['rich', 'poor', 'wealthy', 'low-income', 'affluent'],
            'geographic_bias': ['urban', 'rural', 'city', 'country', 'region']
        }

        # Ethical guidelines
        self.ethical_thresholds = {
            BiasType.DEMOGRAPHIC_PARITY: 0.1,  # Max 10% difference
            BiasType.EQUALIZED_ODDS: 0.1,
            BiasType.EQUAL_OPPORTUNITY: 0.1,
            BiasType.CALIBRATION: 0.05,
            BiasType.INDIVIDUAL_FAIRNESS: 0.2
        }
        
        # Protected groups monitoring
        self.protected_groups = {}
        self.group_performance_history = {}
        
        logger.info(f"[ETHICAL] Ethical AI Engine initialized - libs: {self.fairness_libs_available}, grok: {self.grok_integration_available}")

    async def initialize(self):
        """Initialize ethical AI engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if self.fairness_libs_available:
                await self._initialize_bias_detectors()
                await self._initialize_fairness_assessors()
                await self._initialize_mitigation_strategies()
                logger.info("[OK] Ethical AI components initialized")
            else:
                logger.warning("[FALLBACK] Fairness libraries not available")
            
            # Initialize ethical guidelines
            await self._initialize_ethical_guidelines()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Ethical AI Engine fully initialized")
            
        except Exception as e:
            logger.error(f"Ethical AI engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def audit_grok_output_bias(self, grok_response: str, request_context: Dict[str, Any]) -> Dict[str, Any]:
        """Audit Grok output for potential bias"""
        try:
            audit_id = str(uuid.uuid4())
            timestamp = datetime.now()

            # Analyze response for bias patterns
            bias_analysis = self._analyze_text_for_bias(grok_response)

            # Check for contextual bias
            contextual_bias = self._check_contextual_bias(grok_response, request_context)

            # Assess fairness in recommendations
            fairness_assessment = self._assess_recommendation_fairness(grok_response)

            # Calculate overall bias score
            bias_score = self._calculate_bias_score(bias_analysis, contextual_bias, fairness_assessment)

            # Create audit record
            audit_record = {
                'audit_id': audit_id,
                'timestamp': timestamp.isoformat(),
                'grok_response_length': len(grok_response),
                'request_context': request_context,
                'bias_analysis': bias_analysis,
                'contextual_bias': contextual_bias,
                'fairness_assessment': fairness_assessment,
                'overall_bias_score': bias_score,
                'bias_level': self._categorize_bias_level(bias_score),
                'recommendations': self._generate_bias_mitigation_recommendations(bias_analysis, bias_score)
            }

            # Store audit record
            self.grok_bias_assessments[audit_id] = audit_record
            self.grok_output_analysis.append({
                'audit_id': audit_id,
                'timestamp': timestamp.isoformat(),
                'bias_score': bias_score,
                'bias_level': audit_record['bias_level']
            })

            logger.info(f"[ETHICAL] Grok output bias audit completed - ID: {audit_id}, Bias Level: {audit_record['bias_level']}")

            return audit_record

        except Exception as e:
            logger.error(f"Grok output bias audit failed: {e}")
            return {'error': str(e)}

    def _analyze_text_for_bias(self, text: str) -> Dict[str, Any]:
        """Analyze text for bias patterns"""
        bias_indicators = {}
        text_lower = text.lower()

        for bias_type, patterns in self.bias_detection_patterns.items():
            matches = []
            for pattern in patterns:
                if pattern in text_lower:
                    # Find context around the match
                    start_idx = text_lower.find(pattern)
                    context_start = max(0, start_idx - 50)
                    context_end = min(len(text), start_idx + len(pattern) + 50)
                    context = text[context_start:context_end]
                    matches.append({
                        'pattern': pattern,
                        'context': context,
                        'position': start_idx
                    })

            bias_indicators[bias_type] = {
                'detected': len(matches) > 0,
                'count': len(matches),
                'matches': matches
            }

        return bias_indicators

    def _check_contextual_bias(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check for contextual bias in response"""
        contextual_issues = {}

        # Check for financial advice bias
        if context.get('task_type') == 'market_psychology':
            if any(term in response.lower() for term in ['always', 'never', 'guaranteed', 'certain']):
                contextual_issues['overconfidence_bias'] = True

        # Check for demographic assumptions
        if any(demo in response.lower() for demo in ['typical investor', 'average trader', 'most people']):
            contextual_issues['demographic_assumptions'] = True

        # Check for geographic bias
        if context.get('symbol') and any(region in response.lower() for region in ['american', 'european', 'asian']):
            contextual_issues['geographic_bias'] = True

        return contextual_issues

    def _assess_recommendation_fairness(self, response: str) -> Dict[str, Any]:
        """Assess fairness of recommendations in response"""
        fairness_metrics = {
            'inclusive_language': True,
            'balanced_perspective': True,
            'accessibility_considered': True
        }

        response_lower = response.lower()

        # Check for exclusive language
        exclusive_terms = ['obviously', 'clearly', 'everyone knows', 'any fool']
        if any(term in response_lower for term in exclusive_terms):
            fairness_metrics['inclusive_language'] = False

        # Check for balanced perspective
        if response_lower.count('however') == 0 and response_lower.count('but') == 0:
            if len(response) > 200:  # Only for longer responses
                fairness_metrics['balanced_perspective'] = False

        # Check for accessibility considerations
        complex_terms = ['sophisticated', 'advanced', 'complex', 'institutional']
        if sum(response_lower.count(term) for term in complex_terms) > 3:
            fairness_metrics['accessibility_considered'] = False

        return fairness_metrics

    def _calculate_bias_score(self, bias_analysis: Dict[str, Any],
                            contextual_bias: Dict[str, Any],
                            fairness_assessment: Dict[str, Any]) -> float:
        """Calculate overall bias score (0.0 = no bias, 1.0 = high bias)"""
        score = 0.0

        # Bias pattern detection (40% weight)
        bias_count = sum(indicators['count'] for indicators in bias_analysis.values())
        bias_score = min(bias_count * 0.1, 0.4)
        score += bias_score

        # Contextual bias (30% weight)
        contextual_score = len(contextual_bias) * 0.1
        score += min(contextual_score, 0.3)

        # Fairness assessment (30% weight)
        unfair_count = sum(1 for fair in fairness_assessment.values() if not fair)
        fairness_score = unfair_count * 0.1
        score += min(fairness_score, 0.3)

        return min(score, 1.0)

    def _categorize_bias_level(self, bias_score: float) -> str:
        """Categorize bias level based on score"""
        if bias_score >= 0.7:
            return 'HIGH'
        elif bias_score >= 0.4:
            return 'MEDIUM'
        elif bias_score >= 0.2:
            return 'LOW'
        else:
            return 'MINIMAL'

    def _generate_bias_mitigation_recommendations(self, bias_analysis: Dict[str, Any],
                                                bias_score: float) -> List[str]:
        """Generate recommendations for bias mitigation"""
        recommendations = []

        if bias_score >= 0.7:
            recommendations.append("High bias detected - consider regenerating response with bias-aware prompting")

        for bias_type, indicators in bias_analysis.items():
            if indicators['detected'] and indicators['count'] > 2:
                recommendations.append(f"Multiple {bias_type} indicators detected - review language for inclusivity")

        if bias_score >= 0.4:
            recommendations.append("Implement bias detection filters before presenting Grok responses to users")
            recommendations.append("Consider adding disclaimers about potential AI bias in responses")

        if not recommendations:
            recommendations.append("Bias levels within acceptable thresholds - continue monitoring")

        return recommendations

    async def get_grok_bias_report(self) -> Dict[str, Any]:
        """Generate comprehensive Grok bias audit report"""
        try:
            total_audits = len(self.grok_bias_assessments)

            if total_audits == 0:
                return {
                    'summary': 'No Grok outputs audited yet',
                    'total_audits': 0
                }

            # Bias level distribution
            bias_levels = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'MINIMAL': 0}
            bias_scores = []

            for assessment in self.grok_bias_assessments.values():
                bias_level = assessment.get('bias_level', 'MINIMAL')
                bias_levels[bias_level] += 1
                bias_scores.append(assessment.get('overall_bias_score', 0.0))

            # Bias type analysis
            bias_type_counts = {}
            for assessment in self.grok_bias_assessments.values():
                for bias_type, indicators in assessment.get('bias_analysis', {}).items():
                    if indicators.get('detected', False):
                        bias_type_counts[bias_type] = bias_type_counts.get(bias_type, 0) + 1

            report = {
                'audit_summary': {
                    'total_audits': total_audits,
                    'average_bias_score': sum(bias_scores) / len(bias_scores),
                    'bias_level_distribution': bias_levels,
                    'high_bias_percentage': (bias_levels['HIGH'] / total_audits) * 100
                },
                'bias_type_analysis': bias_type_counts,
                'trend_analysis': self._analyze_bias_trends(),
                'recommendations': self._generate_system_recommendations(bias_levels, bias_type_counts),
                'timestamp': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            logger.error(f"Grok bias report generation failed: {e}")
            return {'error': str(e)}

    def _analyze_bias_trends(self) -> Dict[str, Any]:
        """Analyze bias trends over time"""
        try:
            if len(self.grok_output_analysis) < 10:
                return {'insufficient_data': True}

            # Recent vs older bias scores
            recent_scores = [entry['bias_score'] for entry in self.grok_output_analysis[-20:]]
            older_scores = [entry['bias_score'] for entry in self.grok_output_analysis[-40:-20]] if len(self.grok_output_analysis) >= 40 else []

            trend_analysis = {
                'recent_average': sum(recent_scores) / len(recent_scores),
                'trend_direction': 'stable'
            }

            if older_scores:
                older_average = sum(older_scores) / len(older_scores)
                if trend_analysis['recent_average'] > older_average + 0.1:
                    trend_analysis['trend_direction'] = 'increasing'
                elif trend_analysis['recent_average'] < older_average - 0.1:
                    trend_analysis['trend_direction'] = 'decreasing'

            return trend_analysis

        except Exception as e:
            logger.error(f"Bias trend analysis failed: {e}")
            return {'error': str(e)}

    def _generate_system_recommendations(self, bias_levels: Dict[str, int],
                                       bias_types: Dict[str, int]) -> List[str]:
        """Generate system-level recommendations"""
        recommendations = []

        total_audits = sum(bias_levels.values())
        high_bias_rate = bias_levels['HIGH'] / max(total_audits, 1)

        if high_bias_rate > 0.1:
            recommendations.append("High bias rate detected - implement pre-processing bias filters")

        if bias_types.get('gender_bias', 0) > 5:
            recommendations.append("Frequent gender bias detected - add gender-neutral language guidelines")

        if bias_types.get('economic_bias', 0) > 5:
            recommendations.append("Economic bias detected - ensure inclusive financial advice")

        if not recommendations:
            recommendations.append("Bias levels within acceptable ranges - continue regular monitoring")

        return recommendations

    async def _initialize_bias_detectors(self):
        """Initialize bias detection algorithms"""
        try:
            self.bias_detectors = {
                BiasType.DEMOGRAPHIC_PARITY: self._detect_demographic_parity_bias,
                BiasType.EQUALIZED_ODDS: self._detect_equalized_odds_bias,
                BiasType.EQUAL_OPPORTUNITY: self._detect_equal_opportunity_bias,
                BiasType.CALIBRATION: self._detect_calibration_bias,
                BiasType.INDIVIDUAL_FAIRNESS: self._detect_individual_fairness_bias
            }
            
            logger.info("[BIAS] Bias detectors initialized")
            
        except Exception as e:
            logger.error(f"Bias detector initialization failed: {e}")
            raise

    async def _initialize_fairness_assessors(self):
        """Initialize fairness assessment methods"""
        try:
            self.fairness_assessors = {
                'statistical_parity': self._assess_statistical_parity,
                'equalized_odds': self._assess_equalized_odds,
                'equal_opportunity': self._assess_equal_opportunity,
                'calibration': self._assess_calibration,
                'individual_fairness': self._assess_individual_fairness
            }
            
            logger.info("[FAIRNESS] Fairness assessors initialized")
            
        except Exception as e:
            logger.error(f"Fairness assessor initialization failed: {e}")
            raise

    async def _initialize_mitigation_strategies(self):
        """Initialize bias mitigation strategies"""
        try:
            self.mitigation_strategies = {
                BiasType.DEMOGRAPHIC_PARITY: [
                    MitigationStrategy(
                        strategy_id="dp_reweighting",
                        bias_type=BiasType.DEMOGRAPHIC_PARITY,
                        mitigation_method="reweighting",
                        implementation_steps=[
                            "Calculate group weights inversely proportional to group size",
                            "Apply weights during model training",
                            "Validate fairness improvement"
                        ],
                        expected_improvement=0.3,
                        computational_cost="low",
                        trade_offs=["Potential accuracy reduction"]
                    )
                ],
                BiasType.EQUALIZED_ODDS: [
                    MitigationStrategy(
                        strategy_id="eo_postprocessing",
                        bias_type=BiasType.EQUALIZED_ODDS,
                        mitigation_method="post_processing",
                        implementation_steps=[
                            "Train model without fairness constraints",
                            "Adjust decision thresholds per group",
                            "Optimize for equalized odds"
                        ],
                        expected_improvement=0.4,
                        computational_cost="medium",
                        trade_offs=["Complex threshold management"]
                    )
                ]
            }
            
            logger.info("[MITIGATION] Mitigation strategies initialized")
            
        except Exception as e:
            logger.error(f"Mitigation strategy initialization failed: {e}")
            raise

    async def _initialize_ethical_guidelines(self):
        """Initialize ethical guidelines and principles"""
        try:
            # Ethical principle weights
            self.ethical_principle_weights = {
                EthicalPrinciple.FAIRNESS: 0.25,
                EthicalPrinciple.TRANSPARENCY: 0.20,
                EthicalPrinciple.ACCOUNTABILITY: 0.15,
                EthicalPrinciple.PRIVACY: 0.15,
                EthicalPrinciple.HUMAN_AUTONOMY: 0.10,
                EthicalPrinciple.NON_MALEFICENCE: 0.10,
                EthicalPrinciple.BENEFICENCE: 0.05
            }
            
            logger.info("[ETHICS] Ethical guidelines initialized")
            
        except Exception as e:
            logger.error(f"Ethical guidelines initialization failed: {e}")
            raise

    async def conduct_bias_audit(self, model_id: str, predictions: np.ndarray,
                               true_labels: np.ndarray, protected_attributes: Dict[str, np.ndarray],
                               feature_data: Optional[pd.DataFrame] = None) -> EthicalAuditReport:
        """Conduct comprehensive bias audit"""
        try:
            report_id = str(uuid.uuid4())
            
            # Perform bias assessments for each protected attribute
            bias_assessments = []
            for attr_name, attr_values in protected_attributes.items():
                try:
                    protected_attr = ProtectedAttribute(attr_name.lower())
                    
                    for bias_type in BiasType:
                        assessment = await self._assess_bias(
                            model_id, bias_type, protected_attr,
                            predictions, true_labels, attr_values
                        )
                        if assessment:
                            bias_assessments.append(assessment)
                            
                except ValueError:
                    logger.warning(f"Unknown protected attribute: {attr_name}")
                    continue
            
            # Calculate fairness metrics
            fairness_metrics = await self._calculate_fairness_metrics(
                predictions, true_labels, protected_attributes
            )
            
            # Assess ethical principles
            ethical_assessment = await self._assess_ethical_principles(
                model_id, bias_assessments, fairness_metrics
            )
            
            # Calculate transparency score
            transparency_score = await self._calculate_transparency_score(model_id)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(bias_assessments)
            
            # Calculate overall ethical score
            overall_score = await self._calculate_overall_ethical_score(
                ethical_assessment, fairness_metrics, transparency_score
            )
            
            # Determine severity level
            severity = await self._determine_severity_level(bias_assessments, overall_score)
            
            # Create audit report
            audit_report = EthicalAuditReport(
                report_id=report_id,
                model_id=model_id,
                audit_timestamp=datetime.now(),
                ethical_principles_assessment=ethical_assessment,
                bias_assessments=bias_assessments,
                fairness_metrics=fairness_metrics,
                transparency_score=transparency_score,
                accountability_measures=await self._get_accountability_measures(),
                recommendations=recommendations,
                overall_ethical_score=overall_score,
                severity_level=severity
            )
            
            # Store audit report
            self.audit_reports[report_id] = audit_report
            
            return audit_report
            
        except Exception as e:
            logger.error(f"Bias audit failed for model {model_id}: {e}")
            raise

    async def _assess_bias(self, model_id: str, bias_type: BiasType, 
                         protected_attribute: ProtectedAttribute,
                         predictions: np.ndarray, true_labels: np.ndarray,
                         attribute_values: np.ndarray) -> Optional[BiasAssessment]:
        """Assess specific type of bias"""
        try:
            if bias_type not in self.bias_detectors:
                return None
            
            detector = self.bias_detectors[bias_type]
            bias_result = await detector(predictions, true_labels, attribute_values)
            
            if bias_result is None:
                return None
            
            bias_score, significance, affected_groups = bias_result
            
            # Generate mitigation recommendations
            recommendations = await self._get_mitigation_recommendations(bias_type, bias_score)
            
            assessment = BiasAssessment(
                assessment_id=str(uuid.uuid4()),
                model_id=model_id,
                bias_type=bias_type,
                protected_attribute=protected_attribute,
                bias_score=bias_score,
                statistical_significance=significance,
                affected_groups=affected_groups,
                mitigation_recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            self.bias_assessments[assessment.assessment_id] = assessment
            
            return assessment
            
        except Exception as e:
            logger.error(f"Bias assessment failed: {e}")
            return None

    async def _detect_demographic_parity_bias(self, predictions: np.ndarray, 
                                            true_labels: np.ndarray,
                                            attribute_values: np.ndarray) -> Optional[Tuple[float, float, List[str]]]:
        """Detect demographic parity bias"""
        try:
            unique_groups = np.unique(attribute_values)
            if len(unique_groups) < 2:
                return None
            
            group_rates = {}
            for group in unique_groups:
                group_mask = attribute_values == group
                group_predictions = predictions[group_mask]
                positive_rate = np.mean(group_predictions > 0.5) if len(group_predictions) > 0 else 0
                group_rates[str(group)] = positive_rate
            
            # Calculate maximum difference between groups
            rates = list(group_rates.values())
            bias_score = max(rates) - min(rates)
            
            # Statistical significance (simplified)
            significance = min(0.99, bias_score * 2)
            
            # Identify affected groups
            min_rate = min(rates)
            affected_groups = [group for group, rate in group_rates.items() if rate == min_rate]
            
            return bias_score, significance, affected_groups
            
        except Exception as e:
            logger.error(f"Demographic parity bias detection failed: {e}")
            return None

    async def _detect_equalized_odds_bias(self, predictions: np.ndarray,
                                        true_labels: np.ndarray,
                                        attribute_values: np.ndarray) -> Optional[Tuple[float, float, List[str]]]:
        """Detect equalized odds bias"""
        try:
            unique_groups = np.unique(attribute_values)
            if len(unique_groups) < 2:
                return None
            
            group_tpr = {}  # True Positive Rate
            group_fpr = {}  # False Positive Rate
            
            for group in unique_groups:
                group_mask = attribute_values == group
                group_pred = predictions[group_mask]
                group_true = true_labels[group_mask]
                
                if len(group_pred) == 0:
                    continue
                
                # Calculate TPR and FPR
                tp = np.sum((group_pred > 0.5) & (group_true == 1))
                fn = np.sum((group_pred <= 0.5) & (group_true == 1))
                fp = np.sum((group_pred > 0.5) & (group_true == 0))
                tn = np.sum((group_pred <= 0.5) & (group_true == 0))
                
                tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
                fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
                
                group_tpr[str(group)] = tpr
                group_fpr[str(group)] = fpr
            
            # Calculate bias as maximum difference in TPR and FPR
            tpr_values = list(group_tpr.values())
            fpr_values = list(group_fpr.values())
            
            tpr_bias = max(tpr_values) - min(tpr_values) if tpr_values else 0
            fpr_bias = max(fpr_values) - min(fpr_values) if fpr_values else 0
            
            bias_score = max(tpr_bias, fpr_bias)
            significance = min(0.99, bias_score * 1.5)
            
            # Identify affected groups (those with lowest TPR)
            if tpr_values:
                min_tpr = min(tpr_values)
                affected_groups = [group for group, tpr in group_tpr.items() if tpr == min_tpr]
            else:
                affected_groups = []
            
            return bias_score, significance, affected_groups
            
        except Exception as e:
            logger.error(f"Equalized odds bias detection failed: {e}")
            return None

    async def _detect_equal_opportunity_bias(self, predictions: np.ndarray,
                                           true_labels: np.ndarray,
                                           attribute_values: np.ndarray) -> Optional[Tuple[float, float, List[str]]]:
        """Detect equal opportunity bias"""
        try:
            # Similar to equalized odds but only considers TPR
            unique_groups = np.unique(attribute_values)
            if len(unique_groups) < 2:
                return None
            
            group_tpr = {}
            
            for group in unique_groups:
                group_mask = attribute_values == group
                group_pred = predictions[group_mask]
                group_true = true_labels[group_mask]
                
                if len(group_pred) == 0:
                    continue
                
                tp = np.sum((group_pred > 0.5) & (group_true == 1))
                fn = np.sum((group_pred <= 0.5) & (group_true == 1))
                
                tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
                group_tpr[str(group)] = tpr
            
            tpr_values = list(group_tpr.values())
            bias_score = max(tpr_values) - min(tpr_values) if tpr_values else 0
            significance = min(0.99, bias_score * 1.8)
            
            min_tpr = min(tpr_values) if tpr_values else 0
            affected_groups = [group for group, tpr in group_tpr.items() if tpr == min_tpr]
            
            return bias_score, significance, affected_groups
            
        except Exception as e:
            logger.error(f"Equal opportunity bias detection failed: {e}")
            return None

    async def _detect_calibration_bias(self, predictions: np.ndarray,
                                     true_labels: np.ndarray,
                                     attribute_values: np.ndarray) -> Optional[Tuple[float, float, List[str]]]:
        """Detect calibration bias"""
        try:
            unique_groups = np.unique(attribute_values)
            if len(unique_groups) < 2:
                return None
            
            group_calibration = {}
            
            for group in unique_groups:
                group_mask = attribute_values == group
                group_pred = predictions[group_mask]
                group_true = true_labels[group_mask]
                
                if len(group_pred) == 0:
                    continue
                
                # Simple calibration: mean predicted probability vs actual rate
                mean_pred = np.mean(group_pred)
                actual_rate = np.mean(group_true)
                calibration_error = abs(mean_pred - actual_rate)
                
                group_calibration[str(group)] = calibration_error
            
            calibration_values = list(group_calibration.values())
            bias_score = max(calibration_values) - min(calibration_values) if calibration_values else 0
            significance = min(0.99, bias_score * 3)
            
            max_error = max(calibration_values) if calibration_values else 0
            affected_groups = [group for group, error in group_calibration.items() if error == max_error]
            
            return bias_score, significance, affected_groups
            
        except Exception as e:
            logger.error(f"Calibration bias detection failed: {e}")
            return None

    async def _detect_individual_fairness_bias(self, predictions: np.ndarray,
                                             true_labels: np.ndarray,
                                             attribute_values: np.ndarray) -> Optional[Tuple[float, float, List[str]]]:
        """Detect individual fairness bias"""
        try:
            # Simplified individual fairness: variance in predictions for similar individuals
            unique_groups = np.unique(attribute_values)
            if len(unique_groups) < 2:
                return None
            
            group_variances = {}
            
            for group in unique_groups:
                group_mask = attribute_values == group
                group_pred = predictions[group_mask]
                
                if len(group_pred) > 1:
                    variance = np.var(group_pred)
                    group_variances[str(group)] = variance
            
            if not group_variances:
                return None
            
            variance_values = list(group_variances.values())
            bias_score = max(variance_values) - min(variance_values)
            significance = min(0.99, bias_score * 5)
            
            max_variance = max(variance_values)
            affected_groups = [group for group, var in group_variances.items() if var == max_variance]
            
            return bias_score, significance, affected_groups
            
        except Exception as e:
            logger.error(f"Individual fairness bias detection failed: {e}")
            return None

    async def _calculate_fairness_metrics(self, predictions: np.ndarray,
                                        true_labels: np.ndarray,
                                        protected_attributes: Dict[str, np.ndarray]) -> FairnessMetrics:
        """Calculate comprehensive fairness metrics"""
        try:
            # Initialize metrics
            demographic_parity_diff = 0.0
            equalized_odds_diff = 0.0
            equal_opportunity_diff = 0.0
            calibration_diff = 0.0
            individual_fairness = 0.8  # Default score
            
            group_metrics = {}
            
            # Calculate metrics for each protected attribute
            for attr_name, attr_values in protected_attributes.items():
                unique_groups = np.unique(attr_values)
                
                if len(unique_groups) >= 2:
                    # Demographic parity
                    dp_result = await self._detect_demographic_parity_bias(predictions, true_labels, attr_values)
                    if dp_result:
                        demographic_parity_diff = max(demographic_parity_diff, dp_result[0])
                    
                    # Equalized odds
                    eo_result = await self._detect_equalized_odds_bias(predictions, true_labels, attr_values)
                    if eo_result:
                        equalized_odds_diff = max(equalized_odds_diff, eo_result[0])
                    
                    # Equal opportunity
                    eop_result = await self._detect_equal_opportunity_bias(predictions, true_labels, attr_values)
                    if eop_result:
                        equal_opportunity_diff = max(equal_opportunity_diff, eop_result[0])
                    
                    # Calibration
                    cal_result = await self._detect_calibration_bias(predictions, true_labels, attr_values)
                    if cal_result:
                        calibration_diff = max(calibration_diff, cal_result[0])
                
                # Group-specific metrics
                group_metrics[attr_name] = await self._calculate_group_metrics(
                    predictions, true_labels, attr_values
                )
            
            # Overall fairness score (lower is better for differences)
            overall_fairness = 1.0 - np.mean([
                demographic_parity_diff,
                equalized_odds_diff,
                equal_opportunity_diff,
                calibration_diff
            ])
            
            return FairnessMetrics(
                demographic_parity_difference=demographic_parity_diff,
                equalized_odds_difference=equalized_odds_diff,
                equal_opportunity_difference=equal_opportunity_diff,
                calibration_difference=calibration_diff,
                individual_fairness_score=individual_fairness,
                overall_fairness_score=max(0.0, overall_fairness),
                group_metrics=group_metrics
            )
            
        except Exception as e:
            logger.error(f"Fairness metrics calculation failed: {e}")
            return FairnessMetrics(0.0, 0.0, 0.0, 0.0, 0.5, 0.5, {})

    async def _calculate_group_metrics(self, predictions: np.ndarray,
                                     true_labels: np.ndarray,
                                     attribute_values: np.ndarray) -> Dict[str, Dict[str, float]]:
        """Calculate performance metrics for each group"""
        try:
            group_metrics = {}
            unique_groups = np.unique(attribute_values)
            
            for group in unique_groups:
                group_mask = attribute_values == group
                group_pred = predictions[group_mask]
                group_true = true_labels[group_mask]
                
                if len(group_pred) == 0:
                    continue
                
                # Convert predictions to binary
                group_pred_binary = (group_pred > 0.5).astype(int)
                
                # Calculate metrics
                if self.fairness_libs_available:
                    accuracy = accuracy_score(group_true, group_pred_binary)
                    precision = precision_score(group_true, group_pred_binary, zero_division=0)
                    recall = recall_score(group_true, group_pred_binary, zero_division=0)
                    f1 = f1_score(group_true, group_pred_binary, zero_division=0)
                else:
                    # Fallback calculations
                    accuracy = np.mean(group_true == group_pred_binary)
                    tp = np.sum((group_pred_binary == 1) & (group_true == 1))
                    fp = np.sum((group_pred_binary == 1) & (group_true == 0))
                    fn = np.sum((group_pred_binary == 0) & (group_true == 1))
                    
                    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                group_metrics[str(group)] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'sample_size': len(group_pred)
                }
            
            return group_metrics
            
        except Exception as e:
            logger.error(f"Group metrics calculation failed: {e}")
            return {}

    async def _assess_ethical_principles(self, model_id: str, bias_assessments: List[BiasAssessment],
                                       fairness_metrics: FairnessMetrics) -> Dict[EthicalPrinciple, float]:
        """Assess adherence to ethical principles"""
        try:
            assessment = {}
            
            # Fairness assessment
            assessment[EthicalPrinciple.FAIRNESS] = fairness_metrics.overall_fairness_score
            
            # Transparency assessment (simplified)
            assessment[EthicalPrinciple.TRANSPARENCY] = 0.8  # Based on explainability features
            
            # Accountability assessment
            assessment[EthicalPrinciple.ACCOUNTABILITY] = 0.9  # Based on audit capabilities
            
            # Privacy assessment
            assessment[EthicalPrinciple.PRIVACY] = 0.85  # Based on privacy features
            
            # Human autonomy assessment
            assessment[EthicalPrinciple.HUMAN_AUTONOMY] = 0.75  # Based on human-in-the-loop features
            
            # Non-maleficence assessment
            max_bias_score = max([ba.bias_score for ba in bias_assessments], default=0.0)
            assessment[EthicalPrinciple.NON_MALEFICENCE] = 1.0 - max_bias_score
            
            # Beneficence assessment
            assessment[EthicalPrinciple.BENEFICENCE] = 0.8  # Based on positive impact measures
            
            return assessment
            
        except Exception as e:
            logger.error(f"Ethical principles assessment failed: {e}")
            return {principle: 0.5 for principle in EthicalPrinciple}

    async def _calculate_transparency_score(self, model_id: str) -> float:
        """Calculate model transparency score"""
        try:
            # Simplified transparency scoring
            transparency_factors = {
                'explainability_available': 0.3,
                'audit_trail_complete': 0.25,
                'decision_process_documented': 0.2,
                'bias_testing_conducted': 0.15,
                'performance_metrics_public': 0.1
            }
            
            # Simulate transparency assessment
            score = sum(transparency_factors.values()) * 0.85  # 85% compliance
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Transparency score calculation failed: {e}")
            return 0.5

    async def _generate_recommendations(self, bias_assessments: List[BiasAssessment]) -> List[str]:
        """Generate recommendations based on bias assessments"""
        try:
            recommendations = []
            
            for assessment in bias_assessments:
                if assessment.bias_score > self.ethical_thresholds.get(assessment.bias_type, 0.1):
                    recommendations.extend(assessment.mitigation_recommendations)
            
            # Add general recommendations
            if any(ba.bias_score > 0.2 for ba in bias_assessments):
                recommendations.append("Consider implementing bias mitigation techniques")
                recommendations.append("Increase diversity in training data")
                recommendations.append("Regular bias monitoring and retraining")
            
            return list(set(recommendations))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Recommendation generation failed: {e}")
            return ["Conduct regular bias audits", "Implement fairness constraints"]

    async def _get_mitigation_recommendations(self, bias_type: BiasType, bias_score: float) -> List[str]:
        """Get specific mitigation recommendations for bias type"""
        try:
            if bias_type in self.mitigation_strategies:
                strategies = self.mitigation_strategies[bias_type]
                recommendations = []
                
                for strategy in strategies:
                    if bias_score > 0.1:  # Significant bias
                        recommendations.append(f"Apply {strategy.mitigation_method}")
                        recommendations.extend(strategy.implementation_steps[:2])  # Top 2 steps
                
                return recommendations
            
            return ["Implement fairness constraints", "Rebalance training data"]
            
        except Exception as e:
            logger.error(f"Mitigation recommendations failed: {e}")
            return []

    async def _calculate_overall_ethical_score(self, ethical_assessment: Dict[EthicalPrinciple, float],
                                             fairness_metrics: FairnessMetrics,
                                             transparency_score: float) -> float:
        """Calculate overall ethical score"""
        try:
            weighted_score = 0.0
            
            for principle, score in ethical_assessment.items():
                weight = self.ethical_principle_weights.get(principle, 0.1)
                weighted_score += score * weight
            
            # Adjust for transparency
            weighted_score = weighted_score * 0.8 + transparency_score * 0.2
            
            return min(1.0, max(0.0, weighted_score))
            
        except Exception as e:
            logger.error(f"Overall ethical score calculation failed: {e}")
            return 0.5

    async def _determine_severity_level(self, bias_assessments: List[BiasAssessment],
                                      overall_score: float) -> AuditSeverity:
        """Determine severity level of ethical issues"""
        try:
            max_bias_score = max([ba.bias_score for ba in bias_assessments], default=0.0)
            
            if overall_score < 0.3 or max_bias_score > 0.5:
                return AuditSeverity.CRITICAL
            elif overall_score < 0.5 or max_bias_score > 0.3:
                return AuditSeverity.HIGH
            elif overall_score < 0.7 or max_bias_score > 0.15:
                return AuditSeverity.MEDIUM
            else:
                return AuditSeverity.LOW
                
        except Exception as e:
            logger.error(f"Severity level determination failed: {e}")
            return AuditSeverity.MEDIUM

    async def _get_accountability_measures(self) -> List[str]:
        """Get accountability measures in place"""
        return [
            "Comprehensive audit trails maintained",
            "Regular bias assessments conducted",
            "Clear escalation procedures defined",
            "Human oversight mechanisms active",
            "Performance monitoring continuous",
            "Stakeholder feedback channels open"
        ]

    def get_audit_history(self, model_id: Optional[str] = None, 
                         days_back: int = 30) -> List[EthicalAuditReport]:
        """Get audit history"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            filtered_reports = []
            for report in self.audit_reports.values():
                if report.audit_timestamp < cutoff_date:
                    continue
                
                if model_id and report.model_id != model_id:
                    continue
                
                filtered_reports.append(report)
            
            # Sort by timestamp (newest first)
            filtered_reports.sort(key=lambda x: x.audit_timestamp, reverse=True)
            
            return filtered_reports
            
        except Exception as e:
            logger.error(f"Audit history retrieval failed: {e}")
            return []

    def get_engine_status(self) -> Dict[str, Any]:
        """Get ethical AI engine status"""
        return {
            'status': self.status.value,
            'fairness_libs_available': self.fairness_libs_available,
            'bias_detectors_count': len(self.bias_detectors),
            'fairness_assessors_count': len(self.fairness_assessors),
            'mitigation_strategies_count': sum(len(strategies) for strategies in self.mitigation_strategies.values()),
            'audit_reports_count': len(self.audit_reports),
            'bias_assessments_count': len(self.bias_assessments),
            'ethical_thresholds': {bt.value: threshold for bt, threshold in self.ethical_thresholds.items()},
            'supported_bias_types': [bt.value for bt in BiasType],
            'supported_protected_attributes': [pa.value for pa in ProtectedAttribute],
            'ethical_principles': [ep.value for ep in EthicalPrinciple]
        }

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasEthicalAIEngine",
    "BiasAssessment",
    "FairnessMetrics",
    "EthicalAuditReport",
    "MitigationStrategy",
    "BiasType",
    "ProtectedAttribute",
    "EthicalPrinciple",
    "AuditSeverity"
]
