"""
A.T.L.A.S Strategies - Consolidated Trading Strategies and Algorithms
Combines Advanced Strategies, Goal-Based Strategy Generator, and Beginner Trading Mentor
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# STRATEGY TYPES AND DATA STRUCTURES
# ============================================================================

class StrategyType(Enum):
    """Trading strategy types"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    SWING = "swing"
    DAY_TRADING = "day_trading"
    POSITION = "position"
    OPTIONS = "options"
    PAIRS = "pairs"


@dataclass
class TradingStrategy:
    """Trading strategy definition"""
    name: str
    strategy_type: StrategyType
    description: str
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_parameters: Dict[str, Any]
    expected_return: float
    max_drawdown: float
    time_horizon: str
    difficulty_level: str


# ============================================================================
# ADVANCED STRATEGIES ENGINE
# ============================================================================

class AtlasAdvancedStrategies:
    """Advanced trading strategies implementation"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.strategies = {}
        self.active_strategies = {}
        self._initialize_strategies()
        logger.info("[STRATEGY] Advanced Strategies Engine initialized")

    def _initialize_strategies(self):
        """Initialize predefined trading strategies"""
        self.strategies = {
            'momentum_breakout': TradingStrategy(
                name="Momentum Breakout",
                strategy_type=StrategyType.MOMENTUM,
                description="Trade breakouts with momentum confirmation",
                entry_conditions=[
                    "Price breaks above resistance with volume",
                    "RSI > 60",
                    "MACD bullish crossover"
                ],
                exit_conditions=[
                    "Price falls below 20-day MA",
                    "RSI < 40",
                    "Stop loss at 2% below entry"
                ],
                risk_parameters={
                    "max_position_size": 0.05,  # 5% of portfolio
                    "stop_loss": 0.02,  # 2% stop loss
                    "take_profit": 0.06  # 6% take profit
                },
                expected_return=0.15,
                max_drawdown=0.08,
                time_horizon="1-3 days",
                difficulty_level="intermediate"
            ),
            
            'mean_reversion': TradingStrategy(
                name="Mean Reversion",
                strategy_type=StrategyType.MEAN_REVERSION,
                description="Buy oversold, sell overbought conditions",
                entry_conditions=[
                    "RSI < 30 (oversold)",
                    "Price below 20-day MA",
                    "Bollinger Band lower touch"
                ],
                exit_conditions=[
                    "RSI > 70 (overbought)",
                    "Price above 20-day MA",
                    "Target 50% retracement"
                ],
                risk_parameters={
                    "max_position_size": 0.03,
                    "stop_loss": 0.03,
                    "take_profit": 0.05
                },
                expected_return=0.12,
                max_drawdown=0.06,
                time_horizon="3-7 days",
                difficulty_level="beginner"
            ),
            
            'swing_trading': TradingStrategy(
                name="Swing Trading",
                strategy_type=StrategyType.SWING,
                description="Capture multi-day price swings",
                entry_conditions=[
                    "Higher highs and higher lows",
                    "Volume confirmation",
                    "Support/resistance levels"
                ],
                exit_conditions=[
                    "Trend reversal signals",
                    "Target resistance levels",
                    "Time-based exit (10 days)"
                ],
                risk_parameters={
                    "max_position_size": 0.08,
                    "stop_loss": 0.04,
                    "take_profit": 0.10
                },
                expected_return=0.20,
                max_drawdown=0.10,
                time_horizon="3-10 days",
                difficulty_level="intermediate"
            )
        }

    async def get_strategy_recommendation(self, symbol: str, market_conditions: Dict[str, Any], 
                                        user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Get strategy recommendation based on conditions and user profile"""
        try:
            # Analyze market conditions
            volatility = market_conditions.get('volatility', 0.15)
            trend = market_conditions.get('trend', 'neutral')
            volume = market_conditions.get('volume', 'normal')
            
            # Get user preferences
            risk_tolerance = user_profile.get('risk_tolerance', 'medium')
            experience_level = user_profile.get('experience_level', 'beginner')
            time_horizon = user_profile.get('time_horizon', 'short')
            
            # Strategy selection logic
            recommended_strategies = []
            
            if experience_level == 'beginner':
                if volatility < 0.20 and trend == 'bullish':
                    recommended_strategies.append(self.strategies['mean_reversion'])
                
            elif experience_level == 'intermediate':
                if volatility > 0.15 and volume == 'high':
                    recommended_strategies.append(self.strategies['momentum_breakout'])
                if trend == 'sideways':
                    recommended_strategies.append(self.strategies['swing_trading'])
            
            # Default to mean reversion for beginners
            if not recommended_strategies:
                recommended_strategies.append(self.strategies['mean_reversion'])
            
            return {
                'symbol': symbol,
                'recommended_strategies': [
                    {
                        'name': strategy.name,
                        'type': strategy.strategy_type.value,
                        'description': strategy.description,
                        'entry_conditions': strategy.entry_conditions,
                        'exit_conditions': strategy.exit_conditions,
                        'risk_parameters': strategy.risk_parameters,
                        'expected_return': strategy.expected_return,
                        'difficulty_level': strategy.difficulty_level,
                        'time_horizon': strategy.time_horizon
                    }
                    for strategy in recommended_strategies
                ],
                'market_analysis': {
                    'volatility': volatility,
                    'trend': trend,
                    'volume': volume
                },
                'user_profile': user_profile
            }
            
        except Exception as e:
            logger.error(f"Strategy recommendation failed: {e}")
            return {'error': str(e)}


# ============================================================================
# GOAL-BASED STRATEGY GENERATOR
# ============================================================================

class GoalBasedStrategyGenerator:
    """Generate trading strategies based on user goals"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.goal_templates = {}
        self._initialize_goal_templates()
        logger.info("[GOAL] Goal-Based Strategy Generator initialized")

    def _initialize_goal_templates(self):
        """Initialize goal-based strategy templates"""
        self.goal_templates = {
            'income_generation': {
                'description': 'Generate steady income through dividends and covered calls',
                'strategies': ['covered_call', 'dividend_capture', 'cash_secured_puts'],
                'risk_level': 'low',
                'expected_return': 0.08,
                'time_commitment': 'low'
            },
            'capital_growth': {
                'description': 'Grow capital through appreciation and compound returns',
                'strategies': ['momentum_breakout', 'swing_trading', 'growth_stocks'],
                'risk_level': 'medium',
                'expected_return': 0.15,
                'time_commitment': 'medium'
            },
            'wealth_preservation': {
                'description': 'Preserve wealth with minimal risk and steady returns',
                'strategies': ['mean_reversion', 'defensive_stocks', 'bond_ladder'],
                'risk_level': 'low',
                'expected_return': 0.06,
                'time_commitment': 'low'
            },
            'aggressive_growth': {
                'description': 'Maximize returns with higher risk tolerance',
                'strategies': ['momentum_trading', 'options_strategies', 'small_cap_growth'],
                'risk_level': 'high',
                'expected_return': 0.25,
                'time_commitment': 'high'
            }
        }

    async def generate_strategy_for_goal(self, goal: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate strategy based on user goal"""
        try:
            if goal not in self.goal_templates:
                return {'error': f'Unknown goal: {goal}'}
            
            template = self.goal_templates[goal]
            
            # Customize based on user profile
            capital = user_profile.get('capital', 10000)
            risk_tolerance = user_profile.get('risk_tolerance', 'medium')
            time_available = user_profile.get('time_available', 'medium')
            
            # Adjust strategy based on profile
            adjusted_strategies = template['strategies'].copy()
            adjusted_return = template['expected_return']
            
            if risk_tolerance == 'low':
                adjusted_return *= 0.8  # Reduce expected return for lower risk
            elif risk_tolerance == 'high':
                adjusted_return *= 1.2  # Increase expected return for higher risk
            
            return {
                'goal': goal,
                'description': template['description'],
                'recommended_strategies': adjusted_strategies,
                'risk_level': template['risk_level'],
                'expected_annual_return': adjusted_return,
                'time_commitment': template['time_commitment'],
                'capital_allocation': self._calculate_allocation(capital, template),
                'implementation_steps': self._generate_implementation_steps(goal, user_profile)
            }
            
        except Exception as e:
            logger.error(f"Goal-based strategy generation failed: {e}")
            return {'error': str(e)}

    def _calculate_allocation(self, capital: float, template: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate capital allocation for strategy"""
        if template['risk_level'] == 'low':
            stock_allocation = 0.6
            bond_allocation = 0.3
            cash_allocation = 0.1
        elif template['risk_level'] == 'medium':
            stock_allocation = 0.7
            bond_allocation = 0.2
            cash_allocation = 0.1
        else:  # high risk
            stock_allocation = 0.8
            bond_allocation = 0.1
            cash_allocation = 0.1
        
        return {
            'total_capital': capital,
            'stock_allocation': stock_allocation * capital,
            'bond_allocation': bond_allocation * capital,
            'cash_allocation': cash_allocation * capital,
            'allocation_percentages': {
                'stocks': stock_allocation * 100,
                'bonds': bond_allocation * 100,
                'cash': cash_allocation * 100
            }
        }

    def _generate_implementation_steps(self, goal: str, user_profile: Dict[str, Any]) -> List[str]:
        """Generate implementation steps for the goal"""
        steps = [
            "1. Set up paper trading account to practice",
            "2. Define risk management rules (2% rule)",
            "3. Create watchlist of suitable stocks",
            "4. Start with small position sizes",
            "5. Track performance and adjust strategy"
        ]
        
        if goal == 'income_generation':
            steps.extend([
                "6. Research dividend-paying stocks",
                "7. Learn covered call strategy",
                "8. Set up dividend reinvestment plan"
            ])
        elif goal == 'capital_growth':
            steps.extend([
                "6. Study technical analysis",
                "7. Practice momentum trading",
                "8. Develop screening criteria"
            ])
        
        return steps


# ============================================================================
# BEGINNER STRATEGY MENTOR
# ============================================================================

class BeginnerStrategyMentor:
    """Specialized mentor for beginner trading strategies"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.beginner_strategies = {}
        self._initialize_beginner_strategies()
        logger.info("[MENTOR] Beginner Strategy Mentor initialized")

    def _initialize_beginner_strategies(self):
        """Initialize beginner-friendly strategies"""
        self.beginner_strategies = {
            'buy_and_hold': {
                'name': 'Buy and Hold',
                'description': 'Simple long-term investment strategy',
                'difficulty': 'very_easy',
                'time_required': '5 minutes per month',
                'steps': [
                    'Choose quality companies',
                    'Buy shares',
                    'Hold for years',
                    'Reinvest dividends'
                ],
                'pros': ['Simple', 'Low fees', 'Tax efficient'],
                'cons': ['No downside protection', 'Requires patience']
            },
            'dollar_cost_averaging': {
                'name': 'Dollar Cost Averaging',
                'description': 'Invest fixed amount regularly regardless of price',
                'difficulty': 'easy',
                'time_required': '10 minutes per week',
                'steps': [
                    'Set fixed investment amount',
                    'Choose investment schedule',
                    'Invest same amount each period',
                    'Continue regardless of market conditions'
                ],
                'pros': ['Reduces timing risk', 'Builds discipline'],
                'cons': ['May miss opportunities', 'Requires consistency']
            }
        }

    async def get_beginner_recommendation(self, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Get beginner strategy recommendation"""
        try:
            capital = user_profile.get('capital', 1000)
            time_available = user_profile.get('time_available', 'low')
            experience = user_profile.get('experience_level', 'beginner')
            
            if capital < 1000:
                recommended = 'dollar_cost_averaging'
            else:
                recommended = 'buy_and_hold'
            
            strategy = self.beginner_strategies[recommended]
            
            return {
                'recommended_strategy': recommended,
                'strategy_details': strategy,
                'why_recommended': self._explain_recommendation(recommended, user_profile),
                'getting_started': self._get_getting_started_guide(recommended),
                'next_steps': self._get_next_steps(recommended)
            }
            
        except Exception as e:
            logger.error(f"Beginner recommendation failed: {e}")
            return {'error': str(e)}

    def _explain_recommendation(self, strategy: str, profile: Dict[str, Any]) -> str:
        """Explain why this strategy is recommended"""
        if strategy == 'dollar_cost_averaging':
            return f"With ${profile.get('capital', 1000)} starting capital, dollar cost averaging helps you build a position gradually while reducing risk."
        else:
            return f"With ${profile.get('capital', 1000)} starting capital, buy and hold is perfect for building long-term wealth with minimal effort."

    def _get_getting_started_guide(self, strategy: str) -> List[str]:
        """Get getting started guide for strategy"""
        if strategy == 'dollar_cost_averaging':
            return [
                "Open a brokerage account with no commission fees",
                "Choose 1-3 broad market ETFs (like SPY, VTI)",
                "Set up automatic weekly/monthly investments",
                "Start with $50-100 per week if possible",
                "Don't check prices daily - stay consistent"
            ]
        else:
            return [
                "Research 5-10 quality companies you understand",
                "Open a brokerage account",
                "Buy shares in 2-3 companies to start",
                "Set up dividend reinvestment",
                "Review holdings quarterly, not daily"
            ]

    def _get_next_steps(self, strategy: str) -> List[str]:
        """Get next steps after implementing strategy"""
        return [
            "Track your investments monthly",
            "Learn about diversification",
            "Understand basic financial statements",
            "Consider adding international exposure",
            "Gradually increase investment amounts"
        ]


# ============================================================================
# STRATEGY ORCHESTRATOR
# ============================================================================

class AtlasStrategyOrchestrator:
    """Main strategy orchestrator combining all strategy engines"""
    
    def __init__(self):
        self.advanced_strategies = AtlasAdvancedStrategies()
        self.goal_based_generator = GoalBasedStrategyGenerator()
        self.beginner_mentor = BeginnerStrategyMentor()
        self.status = EngineStatus.ACTIVE
        logger.info("[ORCHESTRATOR] Strategy Orchestrator initialized")

    async def get_strategy_recommendation(self, user_profile: Dict[str, Any], 
                                        market_conditions: Dict[str, Any] = None,
                                        symbol: str = None) -> Dict[str, Any]:
        """Get comprehensive strategy recommendation"""
        try:
            experience_level = user_profile.get('experience_level', 'beginner')
            
            if experience_level == 'beginner':
                return await self.beginner_mentor.get_beginner_recommendation(user_profile)
            else:
                if symbol and market_conditions:
                    return await self.advanced_strategies.get_strategy_recommendation(
                        symbol, market_conditions, user_profile
                    )
                else:
                    # Goal-based recommendation
                    goal = user_profile.get('primary_goal', 'capital_growth')
                    return await self.goal_based_generator.generate_strategy_for_goal(goal, user_profile)
                    
        except Exception as e:
            logger.error(f"Strategy recommendation failed: {e}")
            return {'error': str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasAdvancedStrategies",
    "GoalBasedStrategyGenerator", 
    "BeginnerStrategyMentor",
    "AtlasStrategyOrchestrator",
    "TradingStrategy",
    "StrategyType"
]
