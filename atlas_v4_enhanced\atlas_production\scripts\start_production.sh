#!/bin/bash
# A.T.L.A.S. Production Environment Startup Script

set -e

echo "Starting A.T.L.A.S. Production Environment..."
echo "Environment: PRODUCTION"
echo "Trading Mode: PAPER TRADING ONLY"
echo "Port: 8000"
echo "Debug: Disabled"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "WARNING: Running as root is not recommended for production"
fi

# Set production environment
export ENVIRONMENT=production

# Navigate to application directory
cd /opt/atlas/app

# Activate virtual environment
if [ -d "/opt/atlas/venv" ]; then
    source /opt/atlas/venv/bin/activate
    echo "Virtual environment activated"
else
    echo "ERROR: Virtual environment not found at /opt/atlas/venv"
    exit 1
fi

# Load production environment variables
if [ -f "/opt/atlas/config/.env.production" ]; then
    export $(cat /opt/atlas/config/.env.production | grep -v '^#' | xargs)
    echo "Production environment variables loaded"
else
    echo "ERROR: Production environment file not found"
    exit 1
fi

# Check required environment variables
required_vars=("ALPACA_API_KEY" "FMP_API_KEY" "SECRET_KEY" "JWT_SECRET")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ] || [ "${!var}" = "your_production_${var,,}_here" ]; then
        echo "ERROR: Required environment variable $var is not set or still has placeholder value"
        exit 1
    fi
done

# Install/update dependencies
echo "Installing production dependencies..."
pip install -r /opt/atlas/config/requirements.txt --no-cache-dir

# Run database migrations if needed
echo "Running database setup..."
python -c "
import asyncio
from atlas_database import AtlasDatabase
async def setup_db():
    db = AtlasDatabase()
    await db.initialize()
    print('Production database initialized')
try:
    asyncio.run(setup_db())
except Exception as e:
    print(f'Database setup error: {e}')
"

# Create log directory
mkdir -p /var/log/atlas
chown atlas:atlas /var/log/atlas

# Start the A.T.L.A.S. server with production settings
echo "Starting A.T.L.A.S. server in production mode..."
exec gunicorn --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker atlas_server:app
