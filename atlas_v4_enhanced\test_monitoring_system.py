"""
Test A.T.L.A.S. Production Monitoring System
Demonstrates comprehensive monitoring, alerting, and metrics collection
"""

import asyncio
import logging
import json
from datetime import datetime
from atlas_monitoring import AtlasProductionMonitoring, Alert

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def custom_alert_handler(alert: Alert):
    """Custom alert handler for demonstration"""
    severity_emoji = {
        "CRITICAL": "🔴",
        "WARNING": "🟡", 
        "INFO": "🟢"
    }
    
    emoji = severity_emoji.get(alert.severity, "⚪")
    print(f"\n{emoji} ALERT RECEIVED:")
    print(f"   Severity: {alert.severity}")
    print(f"   Title: {alert.title}")
    print(f"   Description: {alert.description}")
    print(f"   Timestamp: {alert.timestamp}")
    print(f"   Source: {alert.source}")
    if alert.metric_name:
        print(f"   Metric: {alert.metric_name} = {alert.metric_value}")
        print(f"   Threshold: {alert.threshold}")
    print("-" * 60)


async def test_monitoring_system():
    """Test the production monitoring system"""
    
    print("="*80)
    print("A.T.L.A.S. PRODUCTION MONITORING SYSTEM TEST")
    print("="*80)
    
    # Configuration for monitoring
    config = {
        'collection_interval': 10,  # 10 seconds for testing
        'smtp': {
            'server': 'smtp.gmail.com',
            'port': 587,
            'username': '<EMAIL>',
            'password': 'your_app_password',
            'from_email': '<EMAIL>',
            'to_emails': ['<EMAIL>']
        }
    }
    
    # Initialize monitoring system
    monitoring = AtlasProductionMonitoring(config)
    
    # Add custom alert handler
    monitoring.add_alert_callback(custom_alert_handler)
    
    print("🚀 Starting A.T.L.A.S. monitoring system...")
    
    # Start monitoring
    await monitoring.start_monitoring()
    
    try:
        # Run monitoring for demonstration
        for i in range(6):  # Run for 6 cycles (1 minute)
            await asyncio.sleep(10)
            
            # Get system status
            status = monitoring.get_system_status()
            
            print(f"\n📊 MONITORING CYCLE {i+1}:")
            print(f"   Monitoring Active: {status['monitoring_active']}")
            print(f"   Uptime: {status['uptime_formatted']}")
            print(f"   Collections: {status['statistics']['collections_count']}")
            print(f"   Active Alerts: {status['active_alerts_count']}")
            print(f"   Avg Collection Time: {status['statistics']['average_collection_time']:.3f}s")
            
            # Show latest metrics
            latest_metrics = status['last_metrics']
            if latest_metrics:
                print(f"   CPU Usage: {latest_metrics.get('cpu_usage', 0):.1f}%")
                print(f"   Memory Usage: {latest_metrics.get('memory_usage', 0):.1f}%")
                print(f"   Disk Usage: {latest_metrics.get('disk_usage', 0):.1f}%")
                print(f"   API Status: {latest_metrics.get('api_status', 'unknown')}")
            
            # Show active alerts
            active_alerts = monitoring.get_active_alerts()
            if active_alerts:
                print(f"\n🚨 ACTIVE ALERTS ({len(active_alerts)}):")
                for alert in active_alerts:
                    print(f"   - {alert['severity']}: {alert['title']}")
            else:
                print(f"\n✅ No active alerts")
    
    except KeyboardInterrupt:
        print("\n⏹️ Stopping monitoring system...")
    
    finally:
        # Stop monitoring
        await monitoring.stop_monitoring()
        
        # Final status report
        print("\n" + "="*80)
        print("MONITORING TEST COMPLETED")
        print("="*80)
        
        final_status = monitoring.get_system_status()
        print(f"Total Collections: {final_status['statistics']['collections_count']}")
        print(f"Total Alerts Sent: {final_status['statistics']['alerts_sent']}")
        print(f"Average Collection Time: {final_status['statistics']['average_collection_time']:.3f}s")
        print(f"Total Uptime: {final_status['uptime_formatted']}")
        
        # Show alert history
        alert_history = monitoring.get_alert_history(1)  # Last hour
        if alert_history:
            print(f"\n📋 ALERT HISTORY ({len(alert_history)} alerts):")
            for alert in alert_history:
                print(f"   {alert['timestamp']}: {alert['severity']} - {alert['title']}")
        else:
            print("\n📋 No alerts in history")


async def test_alert_system():
    """Test the alert system specifically"""
    
    print("\n" + "="*80)
    print("TESTING ALERT SYSTEM")
    print("="*80)
    
    from atlas_monitoring import EnhancedAlertManager, MetricData
    
    # Initialize alert manager
    alert_manager = EnhancedAlertManager()
    
    # Add test callback
    alert_manager.add_notification_callback(custom_alert_handler)
    
    # Create test metrics that should trigger alerts
    test_metrics = [
        MetricData("system.cpu.usage", 85.0, "percent", datetime.now()),  # Should trigger warning
        MetricData("system.memory.usage", 97.0, "percent", datetime.now()),  # Should trigger critical
        MetricData("atlas.api.status", 0, "boolean", datetime.now()),  # Should trigger critical
        MetricData("atlas.api.response_time", 2000.0, "ms", datetime.now()),  # Should trigger warning
    ]
    
    print("🧪 Testing alert evaluation with sample metrics...")
    
    # Evaluate metrics
    alerts = alert_manager.evaluate_metrics(test_metrics)
    
    print(f"Generated {len(alerts)} alerts from {len(test_metrics)} test metrics")
    
    # Send alerts
    for alert in alerts:
        await alert_manager.send_alert(alert)
    
    # Show active alerts
    active_alerts = alert_manager.get_active_alerts()
    print(f"\n📊 Active Alerts: {len(active_alerts)}")
    
    for alert in active_alerts:
        print(f"   - {alert.severity}: {alert.title}")


async def main():
    """Main test function"""
    try:
        # Test 1: Full monitoring system
        await test_monitoring_system()
        
        # Test 2: Alert system specifically
        await test_alert_system()
        
        print("\n✅ All monitoring tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logger.error(f"Monitoring test failed: {e}")


if __name__ == "__main__":
    print("Starting A.T.L.A.S. Monitoring System Tests...")
    asyncio.run(main())
