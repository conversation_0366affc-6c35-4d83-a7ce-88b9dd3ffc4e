"""
A.T.L.A.S. Theory of Mind Engine
Market participant behavior modeling and prediction
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

# Core imports
from models import EngineStatus

# Grok integration (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# THEORY OF MIND MODELS
# ============================================================================

class ParticipantType(Enum):
    """Types of market participants"""
    RETAIL_TRADER = "retail_trader"
    INSTITUTIONAL_INVESTOR = "institutional_investor"
    HEDGE_FUND = "hedge_fund"
    ALGORITHMIC_TRADER = "algorithmic_trader"
    MARKET_MAKER = "market_maker"
    PENSION_FUND = "pension_fund"
    SOVEREIGN_WEALTH = "sovereign_wealth"
    HIGH_FREQUENCY_TRADER = "high_frequency_trader"

class EmotionalState(Enum):
    """Emotional states affecting trading behavior"""
    FEAR = "fear"
    GREED = "greed"
    EUPHORIA = "euphoria"
    PANIC = "panic"
    CONFIDENCE = "confidence"
    UNCERTAINTY = "uncertainty"
    NEUTRAL = "neutral"

class BehaviorPattern(Enum):
    """Common behavioral patterns in markets"""
    MOMENTUM_FOLLOWING = "momentum_following"
    CONTRARIAN = "contrarian"
    HERDING = "herding"
    PROFIT_TAKING = "profit_taking"
    LOSS_AVERSION = "loss_aversion"
    ANCHORING = "anchoring"
    OVERCONFIDENCE = "overconfidence"
    RECENCY_BIAS = "recency_bias"

@dataclass
class MarketParticipant:
    """Represents a market participant's characteristics"""
    participant_id: str
    participant_type: ParticipantType
    emotional_state: EmotionalState
    risk_tolerance: float  # 0.0 to 1.0
    time_horizon: int  # in days
    typical_position_size: float
    behavior_patterns: List[BehaviorPattern]
    influence_score: float  # 0.0 to 1.0
    confidence_level: float  # 0.0 to 1.0
    last_updated: datetime

@dataclass
class BehaviorPrediction:
    """Prediction of participant behavior"""
    participant_type: ParticipantType
    predicted_action: str  # 'buy', 'sell', 'hold'
    probability: float
    reasoning: str
    emotional_drivers: List[EmotionalState]
    time_frame: int  # minutes
    confidence: float
    market_conditions: Dict[str, Any]
    timestamp: datetime

@dataclass
class MarketSentimentProfile:
    """Overall market sentiment profile"""
    dominant_emotion: EmotionalState
    fear_greed_index: float  # 0.0 (extreme fear) to 1.0 (extreme greed)
    retail_sentiment: float
    institutional_sentiment: float
    volatility_expectation: float
    consensus_direction: str  # 'bullish', 'bearish', 'neutral'
    confidence: float
    timestamp: datetime
    grok_enhanced: bool = False
    grok_insights: Optional[str] = None
    reasoning_chain: Optional[List[str]] = None
    improvement_metrics: Optional[Dict[str, float]] = None
    grok_error: Optional[str] = None

# ============================================================================
# THEORY OF MIND ENGINE
# ============================================================================

class AtlasTheoryOfMindEngine:
    """Theory of mind engine for market participant behavior modeling"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE

        # Participant models
        self.participant_models = {}
        self.behavior_patterns = {}
        self.sentiment_history = []

        # Grok integration
        self.grok_engine = None
        self.grok_enhanced_analyses = {}
        
        # Behavioral parameters
        self.emotional_weights = {
            EmotionalState.FEAR: {'sell_probability': 0.8, 'volatility_impact': 0.3},
            EmotionalState.GREED: {'buy_probability': 0.7, 'risk_taking': 0.4},
            EmotionalState.EUPHORIA: {'buy_probability': 0.9, 'overconfidence': 0.6},
            EmotionalState.PANIC: {'sell_probability': 0.95, 'irrational_behavior': 0.8},
            EmotionalState.CONFIDENCE: {'buy_probability': 0.6, 'position_size': 0.3},
            EmotionalState.UNCERTAINTY: {'hold_probability': 0.7, 'reduced_activity': 0.4}
        }
        
        # Participant type characteristics
        self.participant_characteristics = {
            ParticipantType.RETAIL_TRADER: {
                'risk_tolerance': 0.6,
                'time_horizon': 30,  # days
                'emotional_sensitivity': 0.8,
                'herd_behavior': 0.7,
                'typical_patterns': [BehaviorPattern.MOMENTUM_FOLLOWING, BehaviorPattern.HERDING]
            },
            ParticipantType.INSTITUTIONAL_INVESTOR: {
                'risk_tolerance': 0.4,
                'time_horizon': 365,  # days
                'emotional_sensitivity': 0.3,
                'herd_behavior': 0.2,
                'typical_patterns': [BehaviorPattern.CONTRARIAN, BehaviorPattern.PROFIT_TAKING]
            },
            ParticipantType.HEDGE_FUND: {
                'risk_tolerance': 0.8,
                'time_horizon': 90,  # days
                'emotional_sensitivity': 0.4,
                'herd_behavior': 0.3,
                'typical_patterns': [BehaviorPattern.MOMENTUM_FOLLOWING, BehaviorPattern.CONTRARIAN]
            },
            ParticipantType.ALGORITHMIC_TRADER: {
                'risk_tolerance': 0.5,
                'time_horizon': 1,  # days
                'emotional_sensitivity': 0.1,
                'herd_behavior': 0.1,
                'typical_patterns': [BehaviorPattern.MOMENTUM_FOLLOWING]
            }
        }
        
        logger.info("[MIND] Theory of Mind Engine initialized")

    async def initialize(self):
        """Initialize the theory of mind engine"""
        try:
            self.status = EngineStatus.INITIALIZING

            # Initialize participant models
            await self._initialize_participant_models()

            # Load behavioral patterns
            await self._load_behavior_patterns()

            # Initialize Grok integration
            if self.grok_integration_available:
                try:
                    self.grok_engine = AtlasGrokIntegrationEngine()
                    grok_success = await self.grok_engine.initialize()
                    if grok_success:
                        logger.info("[OK] Grok integration initialized for theory of mind")
                    else:
                        logger.warning("[FALLBACK] Grok API not available - enhanced psychology analysis disabled")
                except Exception as e:
                    logger.error(f"Grok integration initialization failed: {e}")
                    self.grok_engine = None
            else:
                logger.warning("[FALLBACK] Grok integration not available")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Theory of Mind Engine fully initialized")

        except Exception as e:
            logger.error(f"Theory of mind engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_participant_models(self):
        """Initialize models for different participant types"""
        try:
            for participant_type, characteristics in self.participant_characteristics.items():
                model = MarketParticipant(
                    participant_id=f"model_{participant_type.value}",
                    participant_type=participant_type,
                    emotional_state=EmotionalState.NEUTRAL,
                    risk_tolerance=characteristics['risk_tolerance'],
                    time_horizon=characteristics['time_horizon'],
                    typical_position_size=1.0,  # Normalized
                    behavior_patterns=characteristics['typical_patterns'],
                    influence_score=self._calculate_influence_score(participant_type),
                    confidence_level=0.7,
                    last_updated=datetime.now()
                )
                
                self.participant_models[participant_type] = model
            
            logger.info(f"[MODELS] Initialized {len(self.participant_models)} participant models")
            
        except Exception as e:
            logger.error(f"Participant model initialization failed: {e}")
            raise

    def _calculate_influence_score(self, participant_type: ParticipantType) -> float:
        """Calculate influence score for participant type"""
        influence_scores = {
            ParticipantType.INSTITUTIONAL_INVESTOR: 0.9,
            ParticipantType.HEDGE_FUND: 0.8,
            ParticipantType.SOVEREIGN_WEALTH: 0.9,
            ParticipantType.PENSION_FUND: 0.7,
            ParticipantType.ALGORITHMIC_TRADER: 0.6,
            ParticipantType.MARKET_MAKER: 0.8,
            ParticipantType.HIGH_FREQUENCY_TRADER: 0.5,
            ParticipantType.RETAIL_TRADER: 0.3
        }
        
        return influence_scores.get(participant_type, 0.5)

    async def _load_behavior_patterns(self):
        """Load and initialize behavioral patterns"""
        try:
            # Define behavioral pattern characteristics
            self.behavior_patterns = {
                BehaviorPattern.MOMENTUM_FOLLOWING: {
                    'description': 'Following price trends and momentum',
                    'trigger_conditions': ['strong_price_movement', 'high_volume'],
                    'probability_modifiers': {'trending_market': 0.3, 'high_volatility': 0.2}
                },
                BehaviorPattern.CONTRARIAN: {
                    'description': 'Taking opposite positions to market sentiment',
                    'trigger_conditions': ['extreme_sentiment', 'overbought_oversold'],
                    'probability_modifiers': {'extreme_fear': 0.4, 'extreme_greed': 0.4}
                },
                BehaviorPattern.HERDING: {
                    'description': 'Following crowd behavior',
                    'trigger_conditions': ['strong_consensus', 'social_media_buzz'],
                    'probability_modifiers': {'retail_dominance': 0.5, 'news_events': 0.3}
                },
                BehaviorPattern.LOSS_AVERSION: {
                    'description': 'Avoiding losses more than seeking gains',
                    'trigger_conditions': ['unrealized_losses', 'market_decline'],
                    'probability_modifiers': {'portfolio_down': 0.6, 'volatility_spike': 0.4}
                }
            }
            
            logger.info(f"[PATTERNS] Loaded {len(self.behavior_patterns)} behavioral patterns")
            
        except Exception as e:
            logger.error(f"Behavior pattern loading failed: {e}")
            raise

    async def analyze_market_psychology(self, symbol: str, market_data: Dict[str, Any]) -> MarketSentimentProfile:
        """Analyze overall market psychology and sentiment"""
        try:
            # Extract market indicators
            price_change = market_data.get('price_change_percent', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            volatility = market_data.get('volatility', 0.2)
            
            # Determine dominant emotion
            dominant_emotion = await self._determine_dominant_emotion(price_change, volatility, volume_ratio)
            
            # Calculate fear-greed index
            fear_greed_index = await self._calculate_fear_greed_index(market_data)
            
            # Estimate participant sentiments
            retail_sentiment = await self._estimate_retail_sentiment(market_data)
            institutional_sentiment = await self._estimate_institutional_sentiment(market_data)
            
            # Determine consensus direction
            consensus_direction = await self._determine_consensus_direction(
                retail_sentiment, institutional_sentiment, price_change
            )
            
            # Create sentiment profile
            sentiment_profile = MarketSentimentProfile(
                dominant_emotion=dominant_emotion,
                fear_greed_index=fear_greed_index,
                retail_sentiment=retail_sentiment,
                institutional_sentiment=institutional_sentiment,
                volatility_expectation=volatility,
                consensus_direction=consensus_direction,
                confidence=0.75,  # Base confidence
                timestamp=datetime.now()
            )
            
            # Store in history
            self.sentiment_history.append(sentiment_profile)
            if len(self.sentiment_history) > 1000:
                self.sentiment_history = self.sentiment_history[-1000:]

            # Enhance with Grok reasoning if available
            if self.grok_engine:
                try:
                    # Convert sentiment profile to dict for Grok enhancement
                    profile_dict = {
                        'dominant_emotion': sentiment_profile.dominant_emotion.value,
                        'fear_greed_index': sentiment_profile.fear_greed_index,
                        'retail_sentiment': sentiment_profile.retail_sentiment,
                        'institutional_sentiment': sentiment_profile.institutional_sentiment,
                        'volatility_expectation': sentiment_profile.volatility_expectation,
                        'consensus_direction': sentiment_profile.consensus_direction,
                        'confidence': sentiment_profile.confidence,
                        'timestamp': sentiment_profile.timestamp.isoformat()
                    }

                    enhanced_result = await self.grok_engine.enhance_market_psychology(
                        profile_dict, symbol, market_data
                    )

                    # Store enhanced result
                    analysis_key = f"{symbol}_{int(datetime.now().timestamp())}"
                    self.grok_enhanced_analyses[analysis_key] = enhanced_result

                    # Create enhanced sentiment profile
                    enhanced_profile = MarketSentimentProfile(
                        dominant_emotion=sentiment_profile.dominant_emotion,
                        fear_greed_index=sentiment_profile.fear_greed_index,
                        retail_sentiment=sentiment_profile.retail_sentiment,
                        institutional_sentiment=sentiment_profile.institutional_sentiment,
                        volatility_expectation=sentiment_profile.volatility_expectation,
                        consensus_direction=sentiment_profile.consensus_direction,
                        confidence=enhanced_result.combined_confidence,
                        timestamp=sentiment_profile.timestamp,
                        grok_enhanced=True,
                        grok_insights=enhanced_result.grok_enhancement.content if enhanced_result.grok_enhancement.success else None,
                        reasoning_chain=enhanced_result.reasoning_chain,
                        improvement_metrics=enhanced_result.improvement_metrics
                    )

                    logger.info(f"[GROK] Enhanced market psychology for {symbol} - confidence improved from {sentiment_profile.confidence:.2f} to {enhanced_result.combined_confidence:.2f}")
                    return enhanced_profile

                except Exception as e:
                    logger.error(f"Grok enhancement failed for market psychology: {e}")
                    # Return original profile with error info
                    sentiment_profile.grok_enhanced = False
                    sentiment_profile.grok_error = str(e)

            return sentiment_profile
            
        except Exception as e:
            logger.error(f"Market psychology analysis failed for {symbol}: {e}")
            return MarketSentimentProfile(
                dominant_emotion=EmotionalState.NEUTRAL,
                fear_greed_index=0.5,
                retail_sentiment=0.5,
                institutional_sentiment=0.5,
                volatility_expectation=0.2,
                consensus_direction='neutral',
                confidence=0.3,
                timestamp=datetime.now()
            )

    async def _determine_dominant_emotion(self, price_change: float, volatility: float, volume_ratio: float) -> EmotionalState:
        """Determine the dominant emotional state in the market"""
        try:
            # Extreme negative movement with high volume
            if price_change < -0.05 and volume_ratio > 2.0:
                return EmotionalState.PANIC
            
            # Strong negative movement
            elif price_change < -0.02:
                return EmotionalState.FEAR
            
            # Strong positive movement with high volume
            elif price_change > 0.05 and volume_ratio > 1.5:
                return EmotionalState.EUPHORIA
            
            # Moderate positive movement
            elif price_change > 0.02:
                return EmotionalState.GREED
            
            # High volatility regardless of direction
            elif volatility > 0.4:
                return EmotionalState.UNCERTAINTY
            
            # Moderate positive movement with normal volume
            elif price_change > 0.01:
                return EmotionalState.CONFIDENCE
            
            else:
                return EmotionalState.NEUTRAL
                
        except Exception as e:
            logger.error(f"Dominant emotion determination failed: {e}")
            return EmotionalState.NEUTRAL

    async def _calculate_fear_greed_index(self, market_data: Dict[str, Any]) -> float:
        """Calculate fear-greed index (0.0 = extreme fear, 1.0 = extreme greed)"""
        try:
            # Components of fear-greed index
            price_momentum = market_data.get('price_change_percent', 0)
            volatility = market_data.get('volatility', 0.2)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            # Normalize components
            momentum_score = max(0, min(1, (price_momentum + 0.1) / 0.2))  # -10% to +10%
            volatility_score = max(0, min(1, 1 - (volatility / 0.5)))  # Lower volatility = less fear
            volume_score = max(0, min(1, (volume_ratio - 0.5) / 1.5))  # Higher volume = more conviction
            
            # Weighted average
            fear_greed_index = (
                momentum_score * 0.4 +
                volatility_score * 0.3 +
                volume_score * 0.3
            )
            
            return fear_greed_index
            
        except Exception as e:
            logger.error(f"Fear-greed index calculation failed: {e}")
            return 0.5

    async def _estimate_retail_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Estimate retail trader sentiment"""
        try:
            # Retail traders tend to be more emotional and momentum-driven
            price_change = market_data.get('price_change_percent', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            # Retail sentiment is amplified by price movements
            base_sentiment = 0.5 + (price_change * 5)  # Amplify price impact
            
            # High volume suggests retail participation
            if volume_ratio > 1.5:
                base_sentiment += 0.1 if price_change > 0 else -0.1
            
            return max(0, min(1, base_sentiment))
            
        except Exception as e:
            logger.error(f"Retail sentiment estimation failed: {e}")
            return 0.5

    async def _estimate_institutional_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Estimate institutional investor sentiment"""
        try:
            # Institutions are more contrarian and less emotional
            price_change = market_data.get('price_change_percent', 0)
            volatility = market_data.get('volatility', 0.2)
            
            # Institutions often buy on weakness, sell on strength
            base_sentiment = 0.5 - (price_change * 2)  # Contrarian tendency
            
            # Institutions prefer lower volatility
            if volatility < 0.15:
                base_sentiment += 0.1
            elif volatility > 0.3:
                base_sentiment -= 0.1
            
            return max(0, min(1, base_sentiment))
            
        except Exception as e:
            logger.error(f"Institutional sentiment estimation failed: {e}")
            return 0.5

    async def _determine_consensus_direction(self, retail_sentiment: float, 
                                          institutional_sentiment: float, 
                                          price_change: float) -> str:
        """Determine overall market consensus direction"""
        try:
            # Weight sentiments by influence
            weighted_sentiment = (retail_sentiment * 0.3 + institutional_sentiment * 0.7)
            
            # Consider recent price action
            if weighted_sentiment > 0.6 and price_change > 0:
                return 'bullish'
            elif weighted_sentiment < 0.4 and price_change < 0:
                return 'bearish'
            elif abs(weighted_sentiment - 0.5) < 0.1:
                return 'neutral'
            elif weighted_sentiment > 0.5:
                return 'cautiously_bullish'
            else:
                return 'cautiously_bearish'
                
        except Exception as e:
            logger.error(f"Consensus direction determination failed: {e}")
            return 'neutral'

    async def predict_participant_behavior(self, participant_type: ParticipantType, 
                                         symbol: str, market_conditions: Dict[str, Any]) -> BehaviorPrediction:
        """Predict behavior of specific participant type"""
        try:
            if participant_type not in self.participant_models:
                raise ValueError(f"Unknown participant type: {participant_type}")
            
            participant = self.participant_models[participant_type]
            
            # Analyze market conditions
            price_change = market_conditions.get('price_change_percent', 0)
            volatility = market_conditions.get('volatility', 0.2)
            sentiment_profile = market_conditions.get('sentiment_profile')
            
            # Update participant emotional state
            participant.emotional_state = await self._update_participant_emotion(
                participant, price_change, volatility, sentiment_profile
            )
            
            # Predict action based on characteristics and emotions
            predicted_action, probability = await self._predict_action(
                participant, market_conditions
            )
            
            # Generate reasoning
            reasoning = await self._generate_behavior_reasoning(
                participant, predicted_action, market_conditions
            )
            
            # Create prediction
            prediction = BehaviorPrediction(
                participant_type=participant_type,
                predicted_action=predicted_action,
                probability=probability,
                reasoning=reasoning,
                emotional_drivers=[participant.emotional_state],
                time_frame=participant.time_horizon,
                confidence=0.7,
                market_conditions=market_conditions,
                timestamp=datetime.now()
            )
            
            return prediction
            
        except Exception as e:
            logger.error(f"Participant behavior prediction failed: {e}")
            return BehaviorPrediction(
                participant_type=participant_type,
                predicted_action='hold',
                probability=0.5,
                reasoning='Error in prediction',
                emotional_drivers=[EmotionalState.NEUTRAL],
                time_frame=60,
                confidence=0.3,
                market_conditions={},
                timestamp=datetime.now()
            )

    async def _update_participant_emotion(self, participant: MarketParticipant, 
                                        price_change: float, volatility: float,
                                        sentiment_profile: Optional[MarketSentimentProfile]) -> EmotionalState:
        """Update participant's emotional state based on market conditions"""
        try:
            characteristics = self.participant_characteristics[participant.participant_type]
            emotional_sensitivity = characteristics['emotional_sensitivity']
            
            # Retail traders are more emotional
            if participant.participant_type == ParticipantType.RETAIL_TRADER:
                if price_change < -0.03:
                    return EmotionalState.FEAR if volatility < 0.3 else EmotionalState.PANIC
                elif price_change > 0.03:
                    return EmotionalState.GREED if volatility < 0.3 else EmotionalState.EUPHORIA
            
            # Institutional investors are more stable
            elif participant.participant_type == ParticipantType.INSTITUTIONAL_INVESTOR:
                if volatility > 0.4:
                    return EmotionalState.UNCERTAINTY
                elif abs(price_change) < 0.01:
                    return EmotionalState.CONFIDENCE
            
            # Default to current sentiment or neutral
            if sentiment_profile:
                return sentiment_profile.dominant_emotion
            
            return EmotionalState.NEUTRAL
            
        except Exception as e:
            logger.error(f"Participant emotion update failed: {e}")
            return EmotionalState.NEUTRAL

    async def _predict_action(self, participant: MarketParticipant, 
                            market_conditions: Dict[str, Any]) -> Tuple[str, float]:
        """Predict participant's trading action"""
        try:
            # Get emotional weights
            emotion_weights = self.emotional_weights.get(participant.emotional_state, {})
            
            # Base probabilities
            buy_prob = emotion_weights.get('buy_probability', 0.3)
            sell_prob = emotion_weights.get('sell_probability', 0.3)
            hold_prob = 1.0 - buy_prob - sell_prob
            
            # Adjust based on participant characteristics
            characteristics = self.participant_characteristics[participant.participant_type]
            
            # Risk tolerance adjustment
            if participant.risk_tolerance > 0.7:
                buy_prob *= 1.2
            elif participant.risk_tolerance < 0.3:
                sell_prob *= 1.2
                buy_prob *= 0.8
            
            # Normalize probabilities
            total_prob = buy_prob + sell_prob + hold_prob
            buy_prob /= total_prob
            sell_prob /= total_prob
            hold_prob /= total_prob
            
            # Determine action
            if buy_prob > sell_prob and buy_prob > hold_prob:
                return 'buy', buy_prob
            elif sell_prob > hold_prob:
                return 'sell', sell_prob
            else:
                return 'hold', hold_prob
                
        except Exception as e:
            logger.error(f"Action prediction failed: {e}")
            return 'hold', 0.5

    async def _generate_behavior_reasoning(self, participant: MarketParticipant, 
                                         predicted_action: str, 
                                         market_conditions: Dict[str, Any]) -> str:
        """Generate human-readable reasoning for predicted behavior"""
        try:
            participant_name = participant.participant_type.value.replace('_', ' ').title()
            emotion_name = participant.emotional_state.value.replace('_', ' ').title()
            
            price_change = market_conditions.get('price_change_percent', 0)
            
            reasoning_templates = {
                'buy': f"{participant_name} likely to buy due to {emotion_name} state and {price_change:.1%} price movement",
                'sell': f"{participant_name} likely to sell driven by {emotion_name} and risk management",
                'hold': f"{participant_name} likely to hold position given {emotion_name} state and market uncertainty"
            }
            
            return reasoning_templates.get(predicted_action, "Standard market behavior expected")
            
        except Exception as e:
            logger.error(f"Behavior reasoning generation failed: {e}")
            return "Unable to generate reasoning"

    def get_engine_status(self) -> Dict[str, Any]:
        """Get theory of mind engine status"""
        status = {
            'status': self.status.value,
            'participant_models': len(self.participant_models),
            'behavior_patterns': len(self.behavior_patterns),
            'sentiment_history': len(self.sentiment_history),
            'emotional_states': [state.value for state in EmotionalState],
            'participant_types': [ptype.value for ptype in ParticipantType],
            'grok_integration_available': self.grok_integration_available,
            'grok_enhanced_analyses': len(self.grok_enhanced_analyses)
        }

        # Add Grok engine status if available
        if self.grok_engine:
            status['grok_engine_status'] = self.grok_engine.get_engine_status()

        return status

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasTheoryOfMindEngine",
    "MarketParticipant",
    "BehaviorPrediction", 
    "MarketSentimentProfile",
    "ParticipantType",
    "EmotionalState",
    "BehaviorPattern"
]
