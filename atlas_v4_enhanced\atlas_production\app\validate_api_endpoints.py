"""
A.T.L.A.S. API Endpoints Validation Script
Validates all API endpoints are properly documented and functional
"""

import asyncio
import logging
import json
import inspect
from typing import Dict, List, Any, Optional
from pathlib import Path
import importlib.util

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIEndpointValidator:
    """Validates A.T.L.A.S. API endpoints"""
    
    def __init__(self):
        self.endpoints = []
        self.validation_results = {}
        
    async def discover_endpoints(self) -> List[Dict[str, Any]]:
        """Discover all API endpoints from atlas_server.py"""
        try:
            # Import the server module
            spec = importlib.util.spec_from_file_location("atlas_server", "atlas_server.py")
            server_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(server_module)
            
            # Get the FastAPI app instance
            app = getattr(server_module, 'app', None)
            if not app:
                logger.error("Could not find FastAPI app instance")
                return []
            
            # Extract endpoints from FastAPI routes
            endpoints = []
            for route in app.routes:
                if hasattr(route, 'methods') and hasattr(route, 'path'):
                    endpoint_info = {
                        'path': route.path,
                        'methods': list(route.methods),
                        'name': getattr(route, 'name', 'unknown'),
                        'endpoint_function': getattr(route, 'endpoint', None),
                        'summary': getattr(route, 'summary', ''),
                        'description': getattr(route, 'description', ''),
                        'tags': getattr(route, 'tags', [])
                    }
                    
                    # Get function signature and docstring
                    if endpoint_info['endpoint_function']:
                        func = endpoint_info['endpoint_function']
                        endpoint_info['signature'] = str(inspect.signature(func))
                        endpoint_info['docstring'] = inspect.getdoc(func) or ''
                    
                    endpoints.append(endpoint_info)
            
            self.endpoints = endpoints
            logger.info(f"Discovered {len(endpoints)} API endpoints")
            return endpoints
            
        except Exception as e:
            logger.error(f"Error discovering endpoints: {e}")
            return []
    
    def validate_endpoint_documentation(self, endpoint: Dict[str, Any]) -> Dict[str, Any]:
        """Validate endpoint documentation completeness"""
        validation = {
            'path': endpoint['path'],
            'methods': endpoint['methods'],
            'has_summary': bool(endpoint.get('summary')),
            'has_description': bool(endpoint.get('description')),
            'has_docstring': bool(endpoint.get('docstring')),
            'has_tags': bool(endpoint.get('tags')),
            'documentation_score': 0
        }
        
        # Calculate documentation score
        score = 0
        if validation['has_summary']:
            score += 25
        if validation['has_description']:
            score += 25
        if validation['has_docstring']:
            score += 25
        if validation['has_tags']:
            score += 25
        
        validation['documentation_score'] = score
        validation['documentation_status'] = 'EXCELLENT' if score >= 75 else 'GOOD' if score >= 50 else 'POOR'
        
        return validation
    
    def categorize_endpoints(self) -> Dict[str, List[Dict[str, Any]]]:
        """Categorize endpoints by functionality"""
        categories = {
            'trading': [],
            'market_data': [],
            'analysis': [],
            'ai_chat': [],
            'system': [],
            'websocket': [],
            'authentication': [],
            'portfolio': [],
            'risk_management': [],
            'education': [],
            'other': []
        }
        
        for endpoint in self.endpoints:
            path = endpoint['path'].lower()
            tags = [tag.lower() for tag in endpoint.get('tags', [])]
            
            # Categorize based on path and tags
            if any(keyword in path for keyword in ['trade', 'order', 'position']):
                categories['trading'].append(endpoint)
            elif any(keyword in path for keyword in ['quote', 'market', 'price', 'data']):
                categories['market_data'].append(endpoint)
            elif any(keyword in path for keyword in ['analyze', 'analysis', 'technical', 'sentiment']):
                categories['analysis'].append(endpoint)
            elif any(keyword in path for keyword in ['chat', 'ai', 'grok', 'conversation']):
                categories['ai_chat'].append(endpoint)
            elif any(keyword in path for keyword in ['ws', 'websocket', 'socket']):
                categories['websocket'].append(endpoint)
            elif any(keyword in path for keyword in ['auth', 'login', 'token']):
                categories['authentication'].append(endpoint)
            elif any(keyword in path for keyword in ['portfolio', 'balance', 'account']):
                categories['portfolio'].append(endpoint)
            elif any(keyword in path for keyword in ['risk', 'stop', 'limit']):
                categories['risk_management'].append(endpoint)
            elif any(keyword in path for keyword in ['education', 'learn', 'tutorial']):
                categories['education'].append(endpoint)
            elif any(keyword in path for keyword in ['status', 'health', 'system', 'config']):
                categories['system'].append(endpoint)
            else:
                categories['other'].append(endpoint)
        
        return categories
    
    def generate_api_documentation(self) -> str:
        """Generate comprehensive API documentation"""
        categories = self.categorize_endpoints()
        
        doc = "# A.T.L.A.S. API Endpoints Documentation\n\n"
        doc += f"**Total Endpoints**: {len(self.endpoints)}\n\n"
        
        for category, endpoints in categories.items():
            if not endpoints:
                continue
                
            doc += f"## {category.replace('_', ' ').title()} ({len(endpoints)} endpoints)\n\n"
            
            for endpoint in endpoints:
                doc += f"### {endpoint['methods']} {endpoint['path']}\n"
                
                if endpoint.get('summary'):
                    doc += f"**Summary**: {endpoint['summary']}\n\n"
                
                if endpoint.get('description'):
                    doc += f"**Description**: {endpoint['description']}\n\n"
                
                if endpoint.get('docstring'):
                    doc += f"**Details**:\n```\n{endpoint['docstring']}\n```\n\n"
                
                if endpoint.get('signature'):
                    doc += f"**Function Signature**: `{endpoint['signature']}`\n\n"
                
                doc += "---\n\n"
        
        return doc
    
    async def validate_all_endpoints(self) -> Dict[str, Any]:
        """Perform comprehensive endpoint validation"""
        logger.info("Starting comprehensive API endpoint validation...")
        
        # Discover endpoints
        await self.discover_endpoints()
        
        if not self.endpoints:
            return {'error': 'No endpoints discovered'}
        
        # Validate each endpoint
        validations = []
        for endpoint in self.endpoints:
            validation = self.validate_endpoint_documentation(endpoint)
            validations.append(validation)
        
        # Calculate overall statistics
        total_endpoints = len(validations)
        well_documented = sum(1 for v in validations if v['documentation_score'] >= 75)
        poorly_documented = sum(1 for v in validations if v['documentation_score'] < 50)
        
        # Categorize endpoints
        categories = self.categorize_endpoints()
        category_counts = {cat: len(endpoints) for cat, endpoints in categories.items() if endpoints}
        
        # Generate results
        results = {
            'total_endpoints': total_endpoints,
            'well_documented_count': well_documented,
            'poorly_documented_count': poorly_documented,
            'documentation_coverage': (well_documented / total_endpoints * 100) if total_endpoints > 0 else 0,
            'category_breakdown': category_counts,
            'endpoint_validations': validations,
            'categories': categories
        }
        
        self.validation_results = results
        return results
    
    def save_validation_report(self, filename: str = "api_validation_report.json"):
        """Save validation results to file"""
        try:
            with open(filename, 'w') as f:
                json.dump(self.validation_results, f, indent=2, default=str)
            logger.info(f"Validation report saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving validation report: {e}")
    
    def save_api_documentation(self, filename: str = "API_DOCUMENTATION.md"):
        """Save API documentation to markdown file"""
        try:
            doc = self.generate_api_documentation()
            with open(filename, 'w') as f:
                f.write(doc)
            logger.info(f"API documentation saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving API documentation: {e}")


async def main():
    """Main validation function"""
    validator = APIEndpointValidator()
    
    # Run validation
    results = await validator.validate_all_endpoints()
    
    if 'error' in results:
        logger.error(f"Validation failed: {results['error']}")
        return
    
    # Print summary
    print("\n" + "="*60)
    print("A.T.L.A.S. API ENDPOINTS VALIDATION SUMMARY")
    print("="*60)
    print(f"Total Endpoints: {results['total_endpoints']}")
    print(f"Well Documented: {results['well_documented_count']} ({results['documentation_coverage']:.1f}%)")
    print(f"Poorly Documented: {results['poorly_documented_count']}")
    print("\nCategory Breakdown:")
    for category, count in results['category_breakdown'].items():
        print(f"  {category.replace('_', ' ').title()}: {count}")
    
    # Save reports
    validator.save_validation_report()
    validator.save_api_documentation()
    
    print(f"\nReports saved:")
    print("  - api_validation_report.json")
    print("  - API_DOCUMENTATION.md")


if __name__ == "__main__":
    asyncio.run(main())
