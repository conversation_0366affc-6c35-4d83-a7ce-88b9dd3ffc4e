"""
A.T.L.A.S. Performance Validation and Optimization Suite
Comprehensive performance testing, validation, and optimization for the multi-agent system
"""

import asyncio
import logging
import time
import statistics
import json
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import aiohttp
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

# Core imports
from atlas_multi_agent_orchestrator import (
    AtlasMultiAgentOrchestrator, OrchestrationRequest, 
    IntentType, OrchestrationMode, TaskPriority
)

logger = logging.getLogger(__name__)

# ============================================================================
# PERFORMANCE TEST MODELS
# ============================================================================

@dataclass
class PerformanceMetrics:
    """Performance metrics for a test run"""
    test_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    median_response_time: float
    p95_response_time: float
    p99_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    success_rate: float
    error_rate: float
    total_duration: float
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class LoadTestConfig:
    """Configuration for load testing"""
    concurrent_users: int
    requests_per_user: int
    ramp_up_time: int  # seconds
    test_duration: int  # seconds
    target_symbols: List[str]
    orchestration_modes: List[OrchestrationMode]
    intents: List[IntentType]

@dataclass
class BacktestResult:
    """Backtesting result"""
    symbol: str
    total_signals: int
    correct_signals: int
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    sharpe_ratio: float
    max_drawdown: float
    total_return: float
    win_rate: float
    average_trade_duration: float

# ============================================================================
# PERFORMANCE VALIDATOR
# ============================================================================

class PerformanceValidator:
    """Comprehensive performance validation system"""
    
    def __init__(self, orchestrator: AtlasMultiAgentOrchestrator):
        self.orchestrator = orchestrator
        self.test_results: List[PerformanceMetrics] = []
        self.backtest_results: List[BacktestResult] = []
        
        # Performance targets
        self.targets = {
            "max_response_time": 10.0,  # seconds
            "min_success_rate": 0.95,   # 95%
            "min_accuracy": 0.95,       # 95%
            "max_concurrent_users": 100,
            "min_requests_per_second": 10
        }
        
        logger.info("Performance validator initialized")
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive performance validation"""
        logger.info("🚀 Starting comprehensive performance validation...")
        
        validation_results = {
            "timestamp": datetime.now().isoformat(),
            "performance_tests": {},
            "load_tests": {},
            "backtests": {},
            "optimization_results": {},
            "overall_score": 0.0,
            "passed_targets": {},
            "recommendations": []
        }
        
        try:
            # 1. Basic performance tests
            logger.info("📊 Running basic performance tests...")
            validation_results["performance_tests"] = await self._run_performance_tests()
            
            # 2. Load testing
            logger.info("🔥 Running load tests...")
            validation_results["load_tests"] = await self._run_load_tests()
            
            # 3. Backtesting
            logger.info("📈 Running backtests...")
            validation_results["backtests"] = await self._run_backtests()
            
            # 4. Agent collaboration optimization
            logger.info("🤝 Testing agent collaboration...")
            validation_results["optimization_results"] = await self._optimize_agent_collaboration()
            
            # 5. Calculate overall score
            validation_results["overall_score"] = self._calculate_overall_score(validation_results)
            validation_results["passed_targets"] = self._check_targets(validation_results)
            validation_results["recommendations"] = self._generate_recommendations(validation_results)
            
            logger.info(f"✅ Validation completed with overall score: {validation_results['overall_score']:.2f}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            validation_results["error"] = str(e)
            return validation_results
    
    async def _run_performance_tests(self) -> Dict[str, Any]:
        """Run basic performance tests"""
        test_configs = [
            {"name": "single_request", "concurrent": 1, "requests": 10},
            {"name": "low_load", "concurrent": 5, "requests": 20},
            {"name": "medium_load", "concurrent": 10, "requests": 50},
            {"name": "high_load", "concurrent": 20, "requests": 100}
        ]
        
        results = {}
        
        for config in test_configs:
            logger.info(f"Running {config['name']} test...")
            
            metrics = await self._run_single_performance_test(
                concurrent_users=config["concurrent"],
                requests_per_user=config["requests"] // config["concurrent"]
            )
            
            results[config["name"]] = {
                "metrics": metrics,
                "passed_response_time": metrics.p95_response_time <= self.targets["max_response_time"],
                "passed_success_rate": metrics.success_rate >= self.targets["min_success_rate"]
            }
            
            self.test_results.append(metrics)
        
        return results
    
    async def _run_single_performance_test(self, concurrent_users: int, requests_per_user: int) -> PerformanceMetrics:
        """Run a single performance test"""
        test_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        # Create tasks for concurrent execution
        tasks = []
        for user in range(concurrent_users):
            for request_num in range(requests_per_user):
                symbol = test_symbols[request_num % len(test_symbols)]
                task = self._make_test_request(symbol, user, request_num)
                tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                failed_requests += 1
            else:
                response_time, success = result
                response_times.append(response_time)
                if success:
                    successful_requests += 1
                else:
                    failed_requests += 1
        
        # Calculate metrics
        total_requests = len(tasks)
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        error_rate = failed_requests / total_requests if total_requests > 0 else 0
        requests_per_second = total_requests / total_duration if total_duration > 0 else 0
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            min_response_time = max_response_time = 0
        
        return PerformanceMetrics(
            test_name=f"concurrent_{concurrent_users}_requests_{requests_per_user}",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=avg_response_time,
            median_response_time=median_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            success_rate=success_rate,
            error_rate=error_rate,
            total_duration=total_duration
        )
    
    async def _make_test_request(self, symbol: str, user_id: int, request_id: int) -> Tuple[float, bool]:
        """Make a single test request"""
        try:
            request = OrchestrationRequest(
                request_id=f"perf_test_{user_id}_{request_id}",
                intent=IntentType.COMPREHENSIVE_ANALYSIS,
                symbol=symbol,
                input_data={"test_mode": True},
                orchestration_mode=OrchestrationMode.HYBRID,
                priority=TaskPriority.MEDIUM,
                timeout_seconds=30
            )
            
            start_time = time.time()
            result = await self.orchestrator.process_request(request)
            end_time = time.time()
            
            response_time = end_time - start_time
            success = result is not None and result.success
            
            return response_time, success
            
        except Exception as e:
            logger.error(f"Test request failed: {e}")
            return 0.0, False
    
    async def _run_load_tests(self) -> Dict[str, Any]:
        """Run comprehensive load tests"""
        load_configs = [
            LoadTestConfig(
                concurrent_users=10,
                requests_per_user=10,
                ramp_up_time=30,
                test_duration=300,
                target_symbols=["AAPL", "GOOGL", "MSFT"],
                orchestration_modes=[OrchestrationMode.PARALLEL, OrchestrationMode.HYBRID],
                intents=[IntentType.PATTERN_DETECTION, IntentType.COMPREHENSIVE_ANALYSIS]
            ),
            LoadTestConfig(
                concurrent_users=50,
                requests_per_user=5,
                ramp_up_time=60,
                test_duration=300,
                target_symbols=["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"],
                orchestration_modes=[OrchestrationMode.HYBRID],
                intents=[IntentType.COMPREHENSIVE_ANALYSIS]
            ),
            LoadTestConfig(
                concurrent_users=100,
                requests_per_user=3,
                ramp_up_time=120,
                test_duration=300,
                target_symbols=["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META"],
                orchestration_modes=[OrchestrationMode.PARALLEL],
                intents=[IntentType.PATTERN_DETECTION, IntentType.DATA_ANALYSIS]
            )
        ]
        
        results = {}
        
        for i, config in enumerate(load_configs):
            test_name = f"load_test_{config.concurrent_users}_users"
            logger.info(f"Running {test_name}...")
            
            try:
                metrics = await self._run_load_test(config)
                
                results[test_name] = {
                    "config": {
                        "concurrent_users": config.concurrent_users,
                        "requests_per_user": config.requests_per_user,
                        "test_duration": config.test_duration
                    },
                    "metrics": metrics,
                    "passed_concurrent_users": config.concurrent_users <= self.targets["max_concurrent_users"],
                    "passed_response_time": metrics.p95_response_time <= self.targets["max_response_time"],
                    "passed_success_rate": metrics.success_rate >= self.targets["min_success_rate"],
                    "passed_throughput": metrics.requests_per_second >= self.targets["min_requests_per_second"]
                }
                
            except Exception as e:
                logger.error(f"Load test {test_name} failed: {e}")
                results[test_name] = {"error": str(e)}
        
        return results
    
    async def _run_load_test(self, config: LoadTestConfig) -> PerformanceMetrics:
        """Run a single load test"""
        logger.info(f"Starting load test: {config.concurrent_users} users, {config.requests_per_user} requests each")
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        # Create semaphore to control concurrency
        semaphore = asyncio.Semaphore(config.concurrent_users)
        
        async def user_session(user_id: int):
            async with semaphore:
                user_response_times = []
                user_successes = 0
                user_failures = 0
                
                for request_num in range(config.requests_per_user):
                    try:
                        symbol = config.target_symbols[request_num % len(config.target_symbols)]
                        mode = config.orchestration_modes[request_num % len(config.orchestration_modes)]
                        intent = config.intents[request_num % len(config.intents)]
                        
                        request = OrchestrationRequest(
                            request_id=f"load_test_{user_id}_{request_num}",
                            intent=intent,
                            symbol=symbol,
                            input_data={"load_test": True, "user_id": user_id},
                            orchestration_mode=mode,
                            priority=TaskPriority.MEDIUM,
                            timeout_seconds=30
                        )
                        
                        req_start = time.time()
                        result = await self.orchestrator.process_request(request)
                        req_end = time.time()
                        
                        response_time = req_end - req_start
                        user_response_times.append(response_time)
                        
                        if result and result.success:
                            user_successes += 1
                        else:
                            user_failures += 1
                        
                        # Small delay between requests
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"Request failed for user {user_id}: {e}")
                        user_failures += 1
                
                return user_response_times, user_successes, user_failures
        
        # Run all user sessions concurrently
        tasks = [user_session(user_id) for user_id in range(config.concurrent_users)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Aggregate results
        for result in results:
            if isinstance(result, Exception):
                failed_requests += config.requests_per_user
            else:
                user_times, user_successes, user_failures = result
                response_times.extend(user_times)
                successful_requests += user_successes
                failed_requests += user_failures
        
        # Calculate metrics
        total_requests = config.concurrent_users * config.requests_per_user
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        error_rate = failed_requests / total_requests if total_requests > 0 else 0
        requests_per_second = total_requests / total_duration if total_duration > 0 else 0
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            min_response_time = max_response_time = 0
        
        return PerformanceMetrics(
            test_name=f"load_test_{config.concurrent_users}_users",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=avg_response_time,
            median_response_time=median_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            success_rate=success_rate,
            error_rate=error_rate,
            total_duration=total_duration
        )

    async def _run_backtests(self) -> Dict[str, Any]:
        """Run backtesting to validate signal accuracy"""
        test_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META"]
        backtest_results = {}

        for symbol in test_symbols:
            logger.info(f"Running backtest for {symbol}...")

            try:
                result = await self._run_single_backtest(symbol)
                self.backtest_results.append(result)

                backtest_results[symbol] = {
                    "accuracy": result.accuracy,
                    "precision": result.precision,
                    "recall": result.recall,
                    "f1_score": result.f1_score,
                    "sharpe_ratio": result.sharpe_ratio,
                    "total_return": result.total_return,
                    "win_rate": result.win_rate,
                    "passed_accuracy": result.accuracy >= self.targets["min_accuracy"]
                }

            except Exception as e:
                logger.error(f"Backtest failed for {symbol}: {e}")
                backtest_results[symbol] = {"error": str(e)}

        # Calculate overall backtest metrics
        if self.backtest_results:
            overall_accuracy = statistics.mean([r.accuracy for r in self.backtest_results])
            overall_precision = statistics.mean([r.precision for r in self.backtest_results])
            overall_recall = statistics.mean([r.recall for r in self.backtest_results])
            overall_f1 = statistics.mean([r.f1_score for r in self.backtest_results])

            backtest_results["overall"] = {
                "accuracy": overall_accuracy,
                "precision": overall_precision,
                "recall": overall_recall,
                "f1_score": overall_f1,
                "passed_accuracy": overall_accuracy >= self.targets["min_accuracy"]
            }

        return backtest_results

    async def _run_single_backtest(self, symbol: str) -> BacktestResult:
        """Run backtest for a single symbol"""
        # Simulate historical data analysis
        # In a real implementation, this would use actual historical data

        total_signals = 100
        correct_predictions = 0
        true_positives = 0
        false_positives = 0
        false_negatives = 0
        returns = []

        # Simulate backtesting over historical periods
        for i in range(total_signals):
            try:
                # Create a test request for historical analysis
                request = OrchestrationRequest(
                    request_id=f"backtest_{symbol}_{i}",
                    intent=IntentType.COMPREHENSIVE_ANALYSIS,
                    symbol=symbol,
                    input_data={
                        "backtest_mode": True,
                        "historical_date": (datetime.now() - timedelta(days=i)).isoformat(),
                        "test_period": i
                    },
                    orchestration_mode=OrchestrationMode.HYBRID,
                    priority=TaskPriority.LOW,
                    timeout_seconds=60
                )

                result = await self.orchestrator.process_request(request)

                if result and result.success:
                    # Simulate signal validation
                    # In reality, this would compare against actual market movements
                    predicted_direction = self._extract_signal_direction(result)
                    actual_direction = self._simulate_actual_direction()

                    if predicted_direction == actual_direction:
                        correct_predictions += 1
                        if predicted_direction == "bullish":
                            true_positives += 1
                        returns.append(0.02)  # Simulate 2% return
                    else:
                        if predicted_direction == "bullish":
                            false_positives += 1
                        else:
                            false_negatives += 1
                        returns.append(-0.01)  # Simulate -1% return
                else:
                    false_negatives += 1
                    returns.append(-0.005)  # Small negative return for failed signals

            except Exception as e:
                logger.error(f"Backtest signal {i} failed for {symbol}: {e}")
                false_negatives += 1
                returns.append(-0.005)

        # Calculate metrics
        accuracy = correct_predictions / total_signals if total_signals > 0 else 0
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        # Calculate financial metrics
        total_return = sum(returns)
        win_rate = len([r for r in returns if r > 0]) / len(returns) if returns else 0

        # Calculate Sharpe ratio (simplified)
        if returns:
            avg_return = statistics.mean(returns)
            return_std = statistics.stdev(returns) if len(returns) > 1 else 0.01
            sharpe_ratio = avg_return / return_std if return_std > 0 else 0
        else:
            sharpe_ratio = 0

        # Calculate max drawdown (simplified)
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if drawdowns.size > 0 else 0

        return BacktestResult(
            symbol=symbol,
            total_signals=total_signals,
            correct_signals=correct_predictions,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            total_return=total_return,
            win_rate=win_rate,
            average_trade_duration=24.0  # Assume 24 hours average
        )

    def _extract_signal_direction(self, result) -> str:
        """Extract signal direction from orchestration result"""
        try:
            if result.final_recommendation:
                # Look for trading recommendation
                recommendation = result.final_recommendation.get("trading_recommendation", {})
                signal_strength = recommendation.get("signal_strength", 0)

                if signal_strength > 0.6:
                    return "bullish"
                elif signal_strength < -0.6:
                    return "bearish"
                else:
                    return "neutral"

            return "neutral"

        except Exception:
            return "neutral"

    def _simulate_actual_direction(self) -> str:
        """Simulate actual market direction for backtesting"""
        # In a real implementation, this would use actual historical data
        import random
        directions = ["bullish", "bearish", "neutral"]
        weights = [0.4, 0.3, 0.3]  # Slightly bullish bias
        return random.choices(directions, weights=weights)[0]

    async def _optimize_agent_collaboration(self) -> Dict[str, Any]:
        """Optimize agent collaboration and coordination"""
        logger.info("Optimizing agent collaboration...")

        optimization_results = {
            "orchestration_modes": {},
            "agent_timeouts": {},
            "parallel_vs_sequential": {},
            "confidence_thresholds": {},
            "recommendations": []
        }

        # Test different orchestration modes
        modes_to_test = [OrchestrationMode.SEQUENTIAL, OrchestrationMode.PARALLEL, OrchestrationMode.HYBRID]

        for mode in modes_to_test:
            logger.info(f"Testing {mode.value} orchestration mode...")

            metrics = await self._test_orchestration_mode(mode)
            optimization_results["orchestration_modes"][mode.value] = {
                "average_response_time": metrics.average_response_time,
                "success_rate": metrics.success_rate,
                "requests_per_second": metrics.requests_per_second
            }

        # Find optimal mode
        best_mode = min(
            optimization_results["orchestration_modes"].items(),
            key=lambda x: x[1]["average_response_time"]
        )

        optimization_results["recommendations"].append(
            f"Optimal orchestration mode: {best_mode[0]} "
            f"(avg response time: {best_mode[1]['average_response_time']:.2f}s)"
        )

        # Test different confidence thresholds
        thresholds_to_test = [0.6, 0.7, 0.8, 0.9]

        for threshold in thresholds_to_test:
            logger.info(f"Testing confidence threshold: {threshold}")

            metrics = await self._test_confidence_threshold(threshold)
            optimization_results["confidence_thresholds"][str(threshold)] = {
                "success_rate": metrics.success_rate,
                "average_response_time": metrics.average_response_time
            }

        # Find optimal threshold
        best_threshold = max(
            optimization_results["confidence_thresholds"].items(),
            key=lambda x: x[1]["success_rate"]
        )

        optimization_results["recommendations"].append(
            f"Optimal confidence threshold: {best_threshold[0]} "
            f"(success rate: {best_threshold[1]['success_rate']:.2f})"
        )

        return optimization_results

    async def _test_orchestration_mode(self, mode: OrchestrationMode) -> PerformanceMetrics:
        """Test a specific orchestration mode"""
        test_requests = 20
        response_times = []
        successful_requests = 0

        start_time = time.time()

        for i in range(test_requests):
            try:
                request = OrchestrationRequest(
                    request_id=f"mode_test_{mode.value}_{i}",
                    intent=IntentType.COMPREHENSIVE_ANALYSIS,
                    symbol="AAPL",
                    input_data={"optimization_test": True},
                    orchestration_mode=mode,
                    priority=TaskPriority.MEDIUM,
                    timeout_seconds=60
                )

                req_start = time.time()
                result = await self.orchestrator.process_request(request)
                req_end = time.time()

                response_times.append(req_end - req_start)

                if result and result.success:
                    successful_requests += 1

            except Exception as e:
                logger.error(f"Mode test request failed: {e}")

        end_time = time.time()
        total_duration = end_time - start_time

        return PerformanceMetrics(
            test_name=f"mode_test_{mode.value}",
            total_requests=test_requests,
            successful_requests=successful_requests,
            failed_requests=test_requests - successful_requests,
            average_response_time=statistics.mean(response_times) if response_times else 0,
            median_response_time=statistics.median(response_times) if response_times else 0,
            p95_response_time=np.percentile(response_times, 95) if response_times else 0,
            p99_response_time=np.percentile(response_times, 99) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            requests_per_second=test_requests / total_duration if total_duration > 0 else 0,
            success_rate=successful_requests / test_requests if test_requests > 0 else 0,
            error_rate=(test_requests - successful_requests) / test_requests if test_requests > 0 else 0,
            total_duration=total_duration
        )

    async def _test_confidence_threshold(self, threshold: float) -> PerformanceMetrics:
        """Test a specific confidence threshold"""
        test_requests = 15
        response_times = []
        successful_requests = 0

        start_time = time.time()

        for i in range(test_requests):
            try:
                request = OrchestrationRequest(
                    request_id=f"threshold_test_{threshold}_{i}",
                    intent=IntentType.COMPREHENSIVE_ANALYSIS,
                    symbol="AAPL",
                    input_data={"threshold_test": True},
                    orchestration_mode=OrchestrationMode.HYBRID,
                    priority=TaskPriority.MEDIUM,
                    confidence_threshold=threshold,
                    timeout_seconds=60
                )

                req_start = time.time()
                result = await self.orchestrator.process_request(request)
                req_end = time.time()

                response_times.append(req_end - req_start)

                if result and result.success and result.confidence_score >= threshold:
                    successful_requests += 1

            except Exception as e:
                logger.error(f"Threshold test request failed: {e}")

        end_time = time.time()
        total_duration = end_time - start_time

        return PerformanceMetrics(
            test_name=f"threshold_test_{threshold}",
            total_requests=test_requests,
            successful_requests=successful_requests,
            failed_requests=test_requests - successful_requests,
            average_response_time=statistics.mean(response_times) if response_times else 0,
            median_response_time=statistics.median(response_times) if response_times else 0,
            p95_response_time=np.percentile(response_times, 95) if response_times else 0,
            p99_response_time=np.percentile(response_times, 99) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            requests_per_second=test_requests / total_duration if total_duration > 0 else 0,
            success_rate=successful_requests / test_requests if test_requests > 0 else 0,
            error_rate=(test_requests - successful_requests) / test_requests if test_requests > 0 else 0,
            total_duration=total_duration
        )

    def _calculate_overall_score(self, validation_results: Dict[str, Any]) -> float:
        """Calculate overall performance score"""
        try:
            scores = []

            # Performance test scores (30% weight)
            perf_tests = validation_results.get("performance_tests", {})
            if perf_tests:
                perf_score = 0
                perf_count = 0

                for test_name, test_result in perf_tests.items():
                    if "metrics" in test_result:
                        metrics = test_result["metrics"]

                        # Response time score (0-100)
                        response_score = max(0, 100 - (metrics.p95_response_time / self.targets["max_response_time"]) * 100)

                        # Success rate score (0-100)
                        success_score = metrics.success_rate * 100

                        test_score = (response_score + success_score) / 2
                        perf_score += test_score
                        perf_count += 1

                if perf_count > 0:
                    scores.append(("performance", perf_score / perf_count, 0.3))

            # Load test scores (25% weight)
            load_tests = validation_results.get("load_tests", {})
            if load_tests:
                load_score = 0
                load_count = 0

                for test_name, test_result in load_tests.items():
                    if "metrics" in test_result:
                        metrics = test_result["metrics"]

                        # Combined score based on multiple factors
                        response_score = max(0, 100 - (metrics.p95_response_time / self.targets["max_response_time"]) * 100)
                        success_score = metrics.success_rate * 100
                        throughput_score = min(100, (metrics.requests_per_second / self.targets["min_requests_per_second"]) * 100)

                        test_score = (response_score + success_score + throughput_score) / 3
                        load_score += test_score
                        load_count += 1

                if load_count > 0:
                    scores.append(("load_tests", load_score / load_count, 0.25))

            # Backtest scores (35% weight)
            backtests = validation_results.get("backtests", {})
            if backtests and "overall" in backtests:
                overall_backtest = backtests["overall"]
                accuracy_score = overall_backtest.get("accuracy", 0) * 100
                precision_score = overall_backtest.get("precision", 0) * 100
                f1_score = overall_backtest.get("f1_score", 0) * 100

                backtest_score = (accuracy_score + precision_score + f1_score) / 3
                scores.append(("backtests", backtest_score, 0.35))

            # Optimization scores (10% weight)
            optimization = validation_results.get("optimization_results", {})
            if optimization:
                opt_score = 75  # Base score for completing optimization

                # Bonus for good orchestration mode performance
                orchestration_modes = optimization.get("orchestration_modes", {})
                if orchestration_modes:
                    best_response_time = min(
                        mode_data.get("average_response_time", float('inf'))
                        for mode_data in orchestration_modes.values()
                    )
                    if best_response_time < self.targets["max_response_time"]:
                        opt_score += 25

                scores.append(("optimization", opt_score, 0.1))

            # Calculate weighted average
            if scores:
                weighted_sum = sum(score * weight for _, score, weight in scores)
                total_weight = sum(weight for _, _, weight in scores)
                overall_score = weighted_sum / total_weight if total_weight > 0 else 0
            else:
                overall_score = 0

            return min(100, max(0, overall_score))

        except Exception as e:
            logger.error(f"Failed to calculate overall score: {e}")
            return 0.0

    def _check_targets(self, validation_results: Dict[str, Any]) -> Dict[str, bool]:
        """Check if performance targets are met"""
        targets_met = {
            "response_time": False,
            "success_rate": False,
            "accuracy": False,
            "concurrent_users": False,
            "throughput": False
        }

        try:
            # Check response time target
            load_tests = validation_results.get("load_tests", {})
            if load_tests:
                response_times = []
                for test_result in load_tests.values():
                    if "metrics" in test_result:
                        response_times.append(test_result["metrics"].p95_response_time)

                if response_times:
                    avg_response_time = statistics.mean(response_times)
                    targets_met["response_time"] = avg_response_time <= self.targets["max_response_time"]

            # Check success rate target
            perf_tests = validation_results.get("performance_tests", {})
            if perf_tests:
                success_rates = []
                for test_result in perf_tests.values():
                    if "metrics" in test_result:
                        success_rates.append(test_result["metrics"].success_rate)

                if success_rates:
                    avg_success_rate = statistics.mean(success_rates)
                    targets_met["success_rate"] = avg_success_rate >= self.targets["min_success_rate"]

            # Check accuracy target
            backtests = validation_results.get("backtests", {})
            if backtests and "overall" in backtests:
                overall_accuracy = backtests["overall"].get("accuracy", 0)
                targets_met["accuracy"] = overall_accuracy >= self.targets["min_accuracy"]

            # Check concurrent users target
            if load_tests:
                max_users_tested = 0
                for test_name, test_result in load_tests.items():
                    if "config" in test_result:
                        users = test_result["config"].get("concurrent_users", 0)
                        if users > max_users_tested and test_result.get("passed_success_rate", False):
                            max_users_tested = users

                targets_met["concurrent_users"] = max_users_tested >= self.targets["max_concurrent_users"]

            # Check throughput target
            if load_tests:
                throughputs = []
                for test_result in load_tests.values():
                    if "metrics" in test_result:
                        throughputs.append(test_result["metrics"].requests_per_second)

                if throughputs:
                    max_throughput = max(throughputs)
                    targets_met["throughput"] = max_throughput >= self.targets["min_requests_per_second"]

        except Exception as e:
            logger.error(f"Failed to check targets: {e}")

        return targets_met

    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations based on results"""
        recommendations = []

        try:
            # Response time recommendations
            load_tests = validation_results.get("load_tests", {})
            if load_tests:
                slow_tests = []
                for test_name, test_result in load_tests.items():
                    if "metrics" in test_result:
                        if test_result["metrics"].p95_response_time > self.targets["max_response_time"]:
                            slow_tests.append(test_name)

                if slow_tests:
                    recommendations.append(
                        f"Response time optimization needed for: {', '.join(slow_tests)}. "
                        "Consider increasing worker processes or optimizing agent algorithms."
                    )

            # Success rate recommendations
            perf_tests = validation_results.get("performance_tests", {})
            if perf_tests:
                failed_tests = []
                for test_name, test_result in perf_tests.items():
                    if "metrics" in test_result:
                        if test_result["metrics"].success_rate < self.targets["min_success_rate"]:
                            failed_tests.append(test_name)

                if failed_tests:
                    recommendations.append(
                        f"Success rate improvement needed for: {', '.join(failed_tests)}. "
                        "Review error handling and timeout configurations."
                    )

            # Accuracy recommendations
            backtests = validation_results.get("backtests", {})
            if backtests and "overall" in backtests:
                overall_accuracy = backtests["overall"].get("accuracy", 0)
                if overall_accuracy < self.targets["min_accuracy"]:
                    recommendations.append(
                        f"Signal accuracy is {overall_accuracy:.1%}, below target of {self.targets['min_accuracy']:.1%}. "
                        "Consider fine-tuning agent algorithms and improving data quality."
                    )

            # Orchestration recommendations
            optimization = validation_results.get("optimization_results", {})
            if optimization and "recommendations" in optimization:
                recommendations.extend(optimization["recommendations"])

            # Scaling recommendations
            targets_met = validation_results.get("passed_targets", {})
            if not targets_met.get("concurrent_users", True):
                recommendations.append(
                    "Concurrent user capacity below target. Consider horizontal scaling "
                    "or implementing connection pooling."
                )

            if not targets_met.get("throughput", True):
                recommendations.append(
                    "Throughput below target. Consider optimizing database queries, "
                    "implementing caching, or increasing worker processes."
                )

            # General recommendations
            overall_score = validation_results.get("overall_score", 0)
            if overall_score < 80:
                recommendations.append(
                    "Overall performance score is below 80. Prioritize response time "
                    "optimization and error rate reduction."
                )
            elif overall_score < 90:
                recommendations.append(
                    "Good performance overall. Focus on fine-tuning for production workloads."
                )
            else:
                recommendations.append(
                    "Excellent performance! System is ready for production deployment."
                )

        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
            recommendations.append("Unable to generate specific recommendations due to analysis error.")

        return recommendations if recommendations else ["No specific recommendations at this time."]

# ============================================================================
# PERFORMANCE TEST RUNNER
# ============================================================================

async def run_performance_validation():
    """Main function to run performance validation"""
    logger.info("🚀 Starting A.T.L.A.S. Performance Validation Suite")

    try:
        # Initialize orchestrator
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()

        # Create validator
        validator = PerformanceValidator(orchestrator)

        # Run comprehensive validation
        results = await validator.run_comprehensive_validation()

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"performance_validation_results_{timestamp}.json"

        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        # Print summary
        print("\n" + "="*80)
        print("🎯 PERFORMANCE VALIDATION SUMMARY")
        print("="*80)
        print(f"Overall Score: {results['overall_score']:.1f}/100")
        print(f"Timestamp: {results['timestamp']}")

        print("\n📊 Target Achievement:")
        targets = results.get('passed_targets', {})
        for target, passed in targets.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {target.replace('_', ' ').title()}: {status}")

        print("\n💡 Recommendations:")
        for i, rec in enumerate(results.get('recommendations', []), 1):
            print(f"  {i}. {rec}")

        print(f"\n📄 Detailed results saved to: {results_file}")
        print("="*80)

        return results

    except Exception as e:
        logger.error(f"Performance validation failed: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    asyncio.run(run_performance_validation())
