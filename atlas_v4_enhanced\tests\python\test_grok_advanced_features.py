"""
Comprehensive Test Suite for A.T.L.A.S. Advanced Grok Integration
Tests all new advanced capabilities including live search, function calling, 
structured outputs, enhanced reasoning, and image analysis.
"""

import pytest
import asyncio
import json
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any
import base64

# Import the enhanced Grok integration
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine,
    GrokAPIClient,
    GrokRequest, GrokResponse, GrokAnalysisResult,
    GrokTaskType, GrokCapability,
    TradingSignal, MarketAnalysis, NewsAnalysis,
    ATLAS_TRADING_TOOLS, MARKET_SEARCH_CONFIGS,
    PYDANTIC_AVAILABLE, HTTPX_AVAILABLE
)

logger = logging.getLogger(__name__)

class TestGrokAdvancedFeatures:
    """Comprehensive test suite for advanced Grok features"""
    
    @pytest.fixture
    async def grok_engine(self):
        """Create Grok engine for testing"""
        engine = AtlasGrokIntegrationEngine()
        await engine.initialize()
        return engine
    
    @pytest.fixture
    async def grok_client(self):
        """Create Grok API client for testing"""
        client = GrokAPIClient()
        await client.initialize()
        return client
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock response data for testing"""
        return {
            "id": "test-request-123",
            "choices": [{
                "message": {
                    "content": "Test response content",
                    "tool_calls": [{
                        "id": "call_123",
                        "function": {
                            "name": "get_market_data",
                            "arguments": '{"symbol": "AAPL", "data_type": "price"}'
                        }
                    }],
                    "reasoning_content": "Step 1: Analyze market conditions..."
                }
            }],
            "usage": {
                "total_tokens": 150,
                "num_sources_used": 5
            },
            "citations": [
                "https://bloomberg.com/news/test-article",
                "https://reuters.com/markets/test-story"
            ]
        }

    # ============================================================================
    # LIVE SEARCH INTEGRATION TESTS
    # ============================================================================
    
    @pytest.mark.asyncio
    async def test_live_search_request(self, grok_client, mock_response_data):
        """Test live search functionality"""
        with patch.object(grok_client, 'client') as mock_client:
            # Mock successful response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            # Test live search request
            result = await grok_client.make_live_search_request(
                query="Latest news for AAPL",
                search_config="real_time_news",
                symbol="AAPL"
            )
            
            assert result.success
            assert result.search_sources_used == 5
            assert len(result.citations) == 2
            assert "bloomberg.com" in result.citations[0]
    
    @pytest.mark.asyncio
    async def test_market_search_configs(self):
        """Test market search configuration validity"""
        # Verify all search configs are properly structured
        for config_name, config in MARKET_SEARCH_CONFIGS.items():
            assert "search_parameters" in config
            assert "mode" in config["search_parameters"]
            assert "sources" in config["search_parameters"]
            
            # Verify source structure
            for source in config["search_parameters"]["sources"]:
                assert "type" in source
                assert source["type"] in ["web", "news", "x", "rss"]
    
    @pytest.mark.asyncio
    async def test_social_sentiment_search(self, grok_client, mock_response_data):
        """Test social media sentiment search"""
        with patch.object(grok_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            result = await grok_client.make_live_search_request(
                query="Social sentiment for TSLA",
                search_config="social_sentiment",
                symbol="TSLA"
            )
            
            assert result.success
            assert result.citations is not None

    # ============================================================================
    # STRUCTURED OUTPUTS TESTS
    # ============================================================================
    
    @pytest.mark.skipif(not PYDANTIC_AVAILABLE, reason="Pydantic not available")
    @pytest.mark.asyncio
    async def test_structured_trading_signal(self, grok_client):
        """Test structured trading signal generation"""
        mock_structured_output = {
            "symbol": "AAPL",
            "action": "BUY",
            "confidence": 0.85,
            "target_price": 180.0,
            "stop_loss": 165.0,
            "reasoning": "Strong technical breakout with volume confirmation",
            "risk_level": "MEDIUM",
            "time_horizon": "SHORT_TERM",
            "entry_price": 175.0,
            "position_size": 0.05
        }
        
        with patch.object(grok_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{
                    "message": {
                        "content": json.dumps(mock_structured_output)
                    }
                }],
                "usage": {"total_tokens": 200}
            }
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            result = await grok_client.make_structured_analysis_request(
                symbol="AAPL",
                analysis_type="trading_signal"
            )
            
            assert result.success
            assert result.structured_output is not None
            assert result.structured_output["action"] == "BUY"
            assert result.structured_output["confidence"] == 0.85
    
    @pytest.mark.skipif(not PYDANTIC_AVAILABLE, reason="Pydantic not available")
    def test_pydantic_models_validation(self):
        """Test Pydantic model validation"""
        # Test TradingSignal validation
        valid_signal = {
            "symbol": "AAPL",
            "action": "BUY",
            "confidence": 0.85,
            "reasoning": "Test reasoning",
            "risk_level": "MEDIUM",
            "time_horizon": "SHORT_TERM"
        }
        
        signal = TradingSignal(**valid_signal)
        assert signal.symbol == "AAPL"
        assert signal.action == "BUY"
        assert signal.confidence == 0.85
        
        # Test invalid confidence (should raise validation error)
        with pytest.raises(Exception):
            TradingSignal(**{**valid_signal, "confidence": 1.5})  # Invalid confidence > 1.0

    # ============================================================================
    # FUNCTION CALLING TESTS
    # ============================================================================
    
    @pytest.mark.asyncio
    async def test_function_calling_request(self, grok_client, mock_response_data):
        """Test function calling capabilities"""
        with patch.object(grok_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            result = await grok_client.make_function_calling_request(
                query="Get market data for AAPL and analyze portfolio impact",
                available_tools=["get_market_data", "analyze_portfolio"]
            )
            
            assert result.success
            assert result.tool_calls is not None
            assert len(result.tool_calls) >= 1
            assert result.tool_calls[0]["function"]["name"] == "get_market_data"
    
    def test_trading_tools_structure(self):
        """Test trading tools are properly structured"""
        for tool in ATLAS_TRADING_TOOLS:
            assert tool["type"] == "function"
            assert "function" in tool
            assert "name" in tool["function"]
            assert "description" in tool["function"]
            assert "parameters" in tool["function"]
            
            # Verify parameter structure
            params = tool["function"]["parameters"]
            assert params["type"] == "object"
            assert "properties" in params
            assert "required" in params
    
    @pytest.mark.asyncio
    async def test_parallel_function_calling(self, grok_client):
        """Test parallel function calling capability"""
        mock_multiple_calls = {
            "choices": [{
                "message": {
                    "content": "Analysis complete",
                    "tool_calls": [
                        {
                            "id": "call_1",
                            "function": {
                                "name": "get_market_data",
                                "arguments": '{"symbol": "AAPL", "data_type": "price"}'
                            }
                        },
                        {
                            "id": "call_2", 
                            "function": {
                                "name": "analyze_portfolio",
                                "arguments": '{"analysis_type": "risk"}'
                            }
                        }
                    ]
                }
            }],
            "usage": {"total_tokens": 300}
        }
        
        with patch.object(grok_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_multiple_calls
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            result = await grok_client.make_function_calling_request(
                query="Analyze AAPL and check portfolio risk",
                available_tools=["get_market_data", "analyze_portfolio"]
            )
            
            assert result.success
            assert len(result.tool_calls) == 2
            assert result.tool_calls[0]["function"]["name"] == "get_market_data"
            assert result.tool_calls[1]["function"]["name"] == "analyze_portfolio"

    # ============================================================================
    # ENHANCED REASONING TESTS
    # ============================================================================
    
    @pytest.mark.asyncio
    async def test_enhanced_reasoning_request(self, grok_client, mock_response_data):
        """Test enhanced reasoning capabilities"""
        with patch.object(grok_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.headers = {}
            mock_client.post.return_value = mock_response
            
            result = await grok_client.make_enhanced_reasoning_request(
                query="Analyze the impact of Fed rate changes on tech stocks",
                effort="high"
            )
            
            assert result.success
            assert result.reasoning_content is not None
            assert "Step 1:" in result.reasoning_content
    
    @pytest.mark.asyncio
    async def test_market_reasoning_analysis(self, grok_engine, mock_response_data):
        """Test market reasoning analysis"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            mock_response = GrokResponse(
                success=True,
                content="Detailed market analysis...",
                confidence=0.9,
                task_type=GrokTaskType.LOGICAL_REASONING,
                capability=GrokCapability.REASONING,
                processing_time=2.5,
                reasoning_content="Step 1: Analyze market conditions..."
            )
            mock_request.return_value = mock_response
            
            result = await grok_engine.enhanced_market_reasoning(
                market_scenario="Fed rate decision impact on markets",
                symbol="SPY",
                reasoning_effort="high"
            )
            
            assert isinstance(result, GrokAnalysisResult)
            assert result.grok_enhancement.success
            assert result.combined_confidence > 0.8
            assert len(result.reasoning_chain) > 0
    
    @pytest.mark.asyncio
    async def test_what_if_scenario_analysis(self, grok_engine):
        """Test what-if scenario analysis"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            mock_response = GrokResponse(
                success=True,
                content="Scenario analysis complete...",
                confidence=0.85,
                task_type=GrokTaskType.CAUSAL_ANALYSIS,
                capability=GrokCapability.LOGICAL_INFERENCE,
                processing_time=3.0,
                reasoning_content="Analyzing multiple scenarios..."
            )
            mock_request.return_value = mock_response
            
            interventions = [
                "Fed raises rates by 0.75%",
                "Fed holds rates steady",
                "Market volatility increases"
            ]
            
            result = await grok_engine.what_if_scenario_analysis(
                base_scenario="Current market conditions",
                interventions=interventions,
                symbol="QQQ"
            )
            
            assert isinstance(result, GrokAnalysisResult)
            assert result.original_result["interventions_count"] == 3
            assert result.improvement_metrics["scenario_coverage"] > 0.0

    # ============================================================================
    # IMAGE ANALYSIS TESTS
    # ============================================================================

    @pytest.fixture
    def sample_chart_image(self):
        """Create sample chart image data for testing"""
        # Create a simple base64 encoded test image
        test_image_data = b"fake_chart_image_data_for_testing"
        return test_image_data

    @pytest.mark.asyncio
    async def test_single_chart_analysis(self, grok_engine, sample_chart_image):
        """Test single chart analysis"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            mock_response = GrokResponse(
                success=True,
                content="Technical analysis: Strong uptrend with support at $150...",
                confidence=0.88,
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                processing_time=1.8
            )
            mock_request.return_value = mock_response

            result = await grok_engine.analyze_trading_chart(
                chart_image=sample_chart_image,
                symbol="AAPL",
                analysis_type="technical"
            )

            assert isinstance(result, GrokAnalysisResult)
            assert result.grok_enhancement.success
            assert result.original_result["symbol"] == "AAPL"
            assert result.improvement_metrics["vision_accuracy"] > 0.8

    @pytest.mark.asyncio
    async def test_multiple_chart_analysis(self, grok_engine, sample_chart_image):
        """Test multi-timeframe chart analysis"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            mock_response = GrokResponse(
                success=True,
                content="Multi-timeframe analysis shows confluence at key levels...",
                confidence=0.92,
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                processing_time=2.5
            )
            mock_request.return_value = mock_response

            chart_images = [sample_chart_image, sample_chart_image, sample_chart_image]
            timeframes = ["1D", "4H", "1H"]

            result = await grok_engine.analyze_multiple_charts(
                chart_images=chart_images,
                symbol="TSLA",
                timeframes=timeframes
            )

            assert isinstance(result, GrokAnalysisResult)
            assert result.original_result["chart_count"] == 3
            assert result.original_result["timeframes"] == timeframes
            assert result.improvement_metrics["multi_timeframe_synthesis"] > 0.8

    @pytest.mark.asyncio
    async def test_earnings_document_analysis(self, grok_engine, sample_chart_image):
        """Test earnings document analysis"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            mock_response = GrokResponse(
                success=True,
                content="Earnings analysis: Revenue growth of 15%, strong margins...",
                confidence=0.87,
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                processing_time=3.2
            )
            mock_request.return_value = mock_response

            document_images = [sample_chart_image, sample_chart_image]

            result = await grok_engine.analyze_earnings_documents(
                document_images=document_images,
                symbol="NVDA"
            )

            assert isinstance(result, GrokAnalysisResult)
            assert result.original_result["symbol"] == "NVDA"
            assert result.original_result["document_count"] == 2
            assert result.improvement_metrics["fundamental_insight"] > 0.8

    # ============================================================================
    # INTEGRATION AND PERFORMANCE TESTS
    # ============================================================================

    @pytest.mark.asyncio
    async def test_grok_engine_initialization(self):
        """Test Grok engine initialization"""
        engine = AtlasGrokIntegrationEngine()
        success = await engine.initialize()

        # Should succeed even if API is not available (fallback mode)
        assert isinstance(success, bool)
        assert engine.status is not None
        assert engine.grok_client is not None

    @pytest.mark.asyncio
    async def test_error_handling_and_fallbacks(self, grok_client):
        """Test error handling and fallback mechanisms"""
        with patch.object(grok_client, 'client') as mock_client:
            # Mock API error
            mock_response = Mock()
            mock_response.status_code = 500
            mock_response.json.return_value = {"error": {"message": "Internal server error"}}
            mock_client.post.return_value = mock_response

            result = await grok_client.make_live_search_request(
                query="Test query",
                search_config="real_time_news"
            )

            # Should handle error gracefully
            assert not result.success
            assert result.error_message is not None
            assert "500" in result.error_message or "Internal server error" in result.error_message

    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, grok_client):
        """Test rate limit handling"""
        with patch.object(grok_client, 'client') as mock_client:
            # Mock rate limit response
            mock_response = Mock()
            mock_response.status_code = 429
            mock_response.json.return_value = {"error": {"message": "Rate limit exceeded"}}
            mock_response.headers = {
                'x-ratelimit-remaining': '0',
                'x-ratelimit-reset': '1640995200'
            }
            mock_client.post.return_value = mock_response

            result = await grok_client.make_enhanced_reasoning_request(
                query="Test rate limit",
                effort="low"
            )

            assert not result.success
            assert "rate limit" in result.error_message.lower()

    def test_model_selection_logic(self, grok_client):
        """Test optimal model selection logic"""
        # Test reasoning model selection
        reasoning_request = GrokRequest(
            task_type=GrokTaskType.LOGICAL_REASONING,
            capability=GrokCapability.REASONING,
            prompt="Test reasoning",
            reasoning_effort="high"
        )

        model = grok_client._select_optimal_model(reasoning_request)
        assert "reasoning" in model.lower() or "mini" in model.lower()

        # Test vision model selection
        vision_request = GrokRequest(
            task_type=GrokTaskType.IMAGE_ANALYSIS,
            capability=GrokCapability.VISION,
            prompt="Analyze chart",
            image_data=b"test_image"
        )

        model = grok_client._select_optimal_model(vision_request)
        assert "vision" in model.lower()

    @pytest.mark.asyncio
    async def test_comprehensive_workflow(self, grok_engine):
        """Test comprehensive workflow combining multiple features"""
        with patch.object(grok_engine.grok_client, 'make_request') as mock_request:
            # Mock different responses for different request types
            def mock_request_side_effect(request):
                if request.capability == GrokCapability.REAL_TIME_SEARCH:
                    return GrokResponse(
                        success=True,
                        content="Latest news analysis...",
                        confidence=0.85,
                        task_type=request.task_type,
                        capability=request.capability,
                        processing_time=1.5,
                        citations=["https://example.com/news1"],
                        search_sources_used=3
                    )
                elif request.capability == GrokCapability.REASONING:
                    return GrokResponse(
                        success=True,
                        content="Enhanced reasoning analysis...",
                        confidence=0.9,
                        task_type=request.task_type,
                        capability=request.capability,
                        processing_time=2.0,
                        reasoning_content="Step 1: Market analysis..."
                    )
                else:
                    return GrokResponse(
                        success=True,
                        content="General analysis...",
                        confidence=0.8,
                        task_type=request.task_type,
                        capability=request.capability,
                        processing_time=1.0
                    )

            mock_request.side_effect = mock_request_side_effect

            # Test workflow: Live search -> Reasoning -> Analysis
            symbol = "AAPL"

            # Step 1: Live search
            search_result = await grok_engine.grok_client.make_live_search_request(
                query=f"Latest news for {symbol}",
                search_config="real_time_news",
                symbol=symbol
            )

            # Step 2: Enhanced reasoning
            reasoning_result = await grok_engine.enhanced_market_reasoning(
                market_scenario=f"Market conditions for {symbol}",
                symbol=symbol,
                reasoning_effort="high"
            )

            # Verify workflow results
            assert search_result.success
            assert search_result.citations is not None
            assert reasoning_result.grok_enhancement.success
            assert reasoning_result.grok_enhancement.reasoning_content is not None

    def test_configuration_validation(self):
        """Test configuration validation"""
        # Test market search configs
        for config in MARKET_SEARCH_CONFIGS.values():
            assert isinstance(config, dict)
            assert "search_parameters" in config

            search_params = config["search_parameters"]
            assert "mode" in search_params
            assert search_params["mode"] in ["auto", "on", "off"]

            if "sources" in search_params:
                for source in search_params["sources"]:
                    assert "type" in source
                    assert source["type"] in ["web", "news", "x", "rss"]

        # Test trading tools
        for tool in ATLAS_TRADING_TOOLS:
            assert "type" in tool
            assert tool["type"] == "function"
            assert "function" in tool

            func = tool["function"]
            assert "name" in func
            assert "description" in func
            assert "parameters" in func

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
