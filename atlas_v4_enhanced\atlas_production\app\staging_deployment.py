"""
A.T.L.A.S. Staging Deployment Script
Automated staging environment setup and deployment
"""

import os
import sys
import asyncio
import logging
import subprocess
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StagingDeployment:
    """Handles A.T.L.A.S. staging environment deployment"""
    
    def __init__(self, staging_dir: str = "atlas_staging"):
        self.staging_dir = Path(staging_dir)
        self.source_dir = Path(".")
        self.deployment_log = []
        self.start_time = datetime.now()
        
    def log_step(self, step: str, status: str = "INFO", details: str = ""):
        """Log deployment step"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            "timestamp": timestamp,
            "step": step,
            "status": status,
            "details": details
        }
        self.deployment_log.append(log_entry)
        
        if status == "ERROR":
            logger.error(f"{step}: {details}")
        elif status == "WARNING":
            logger.warning(f"{step}: {details}")
        else:
            logger.info(f"{step}: {details}")
    
    def create_staging_directory(self) -> bool:
        """Create and prepare staging directory"""
        try:
            self.log_step("Creating staging directory", "INFO", f"Path: {self.staging_dir}")
            
            # Remove existing staging directory if it exists
            if self.staging_dir.exists():
                shutil.rmtree(self.staging_dir)
                self.log_step("Removed existing staging directory", "INFO")
            
            # Create new staging directory
            self.staging_dir.mkdir(parents=True, exist_ok=True)
            self.log_step("Staging directory created successfully", "INFO")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to create staging directory", "ERROR", str(e))
            return False
    
    def copy_source_files(self) -> bool:
        """Copy source files to staging directory"""
        try:
            self.log_step("Copying source files to staging", "INFO")
            
            # Files to copy
            python_files = list(self.source_dir.glob("*.py"))
            config_files = [".env.example", "requirements.txt"]
            
            # Copy Python files
            for file_path in python_files:
                if file_path.name not in ["staging_deployment.py"]:  # Skip deployment script
                    dest_path = self.staging_dir / file_path.name
                    shutil.copy2(file_path, dest_path)
            
            # Copy configuration files
            for config_file in config_files:
                if Path(config_file).exists():
                    shutil.copy2(config_file, self.staging_dir / config_file)
            
            # Copy helper directories if they exist
            helper_dirs = ["4_helper_tools", "static", "templates"]
            for helper_dir in helper_dirs:
                if Path(helper_dir).exists():
                    shutil.copytree(helper_dir, self.staging_dir / helper_dir, dirs_exist_ok=True)
            
            files_copied = len(list(self.staging_dir.glob("*.py")))
            self.log_step("Source files copied successfully", "INFO", f"{files_copied} Python files copied")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to copy source files", "ERROR", str(e))
            return False
    
    def create_staging_config(self) -> bool:
        """Create staging-specific configuration"""
        try:
            self.log_step("Creating staging configuration", "INFO")
            
            # Create staging .env file
            staging_env_content = """# A.T.L.A.S. Staging Environment Configuration
# STAGING ENVIRONMENT - DO NOT USE IN PRODUCTION

# System Configuration
HOST=localhost
PORT=8002
DEBUG=true
ENVIRONMENT=staging

# CRITICAL: Trading Mode - ALWAYS PAPER TRADING IN STAGING
ATLAS_TRADING_MODE=PAPER
PAPER_TRADING=true

# Database Configuration (Staging)
DATABASE_URL=sqlite:///atlas_staging.db

# API Keys (Use test/demo keys only)
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=your_staging_alpaca_api_key_here
ALPACA_SECRET_KEY=your_staging_alpaca_secret_key_here

# Financial Modeling Prep API (Demo/Test Key)
FMP_API_KEY=your_staging_fmp_api_key_here

# OpenAI API (Test Key)
OPENAI_API_KEY=your_staging_openai_api_key_here

# Grok API (Test Key)
GROK_API_KEY=your_staging_grok_api_key_here

# Email Configuration (Test SMTP)
SMTP_SERVER=smtp.mailtrap.io
SMTP_PORT=587
SMTP_USERNAME=your_mailtrap_username
SMTP_PASSWORD=your_mailtrap_password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FILE=atlas_staging.log

# Performance Settings (Reduced for staging)
MAX_CONCURRENT_REQUESTS=10
CACHE_TTL=300
REQUEST_TIMEOUT=30

# Security Settings
SECRET_KEY=staging_secret_key_change_in_production
JWT_SECRET=staging_jwt_secret_change_in_production

# Monitoring (Staging)
ENABLE_METRICS=true
METRICS_PORT=8003
"""
            
            with open(self.staging_dir / ".env", "w") as f:
                f.write(staging_env_content)
            
            self.log_step("Staging .env file created", "INFO")
            
            # Create staging-specific startup script
            startup_script = """#!/bin/bash
# A.T.L.A.S. Staging Environment Startup Script

echo "Starting A.T.L.A.S. Staging Environment..."
echo "Environment: STAGING"
echo "Trading Mode: PAPER TRADING ONLY"
echo "Port: 8002"
echo "Debug: Enabled"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "Virtual environment activated"
fi

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run database migrations/setup if needed
echo "Setting up staging database..."
python -c "
import asyncio
from atlas_database import AtlasDatabase
async def setup_db():
    db = AtlasDatabase()
    await db.initialize()
    print('Staging database initialized')
asyncio.run(setup_db())
"

# Start the A.T.L.A.S. server
echo "Starting A.T.L.A.S. server in staging mode..."
python atlas_server.py
"""
            
            with open(self.staging_dir / "start_staging.sh", "w") as f:
                f.write(startup_script)
            
            # Make startup script executable
            os.chmod(self.staging_dir / "start_staging.sh", 0o755)
            
            self.log_step("Staging startup script created", "INFO")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to create staging configuration", "ERROR", str(e))
            return False
    
    def validate_staging_environment(self) -> bool:
        """Validate staging environment setup"""
        try:
            self.log_step("Validating staging environment", "INFO")
            
            validation_results = {
                "python_files": 0,
                "config_files": 0,
                "required_modules": [],
                "missing_files": []
            }
            
            # Check Python files
            python_files = list(self.staging_dir.glob("*.py"))
            validation_results["python_files"] = len(python_files)
            
            # Check required files
            required_files = [
                "atlas_server.py",
                "atlas_orchestrator.py", 
                "config.py",
                "requirements.txt",
                ".env",
                "start_staging.sh"
            ]
            
            for required_file in required_files:
                if (self.staging_dir / required_file).exists():
                    validation_results["config_files"] += 1
                else:
                    validation_results["missing_files"].append(required_file)
            
            # Check for critical modules
            critical_modules = [
                "atlas_trading_core.py",
                "atlas_ai_core.py",
                "atlas_market_core.py",
                "atlas_risk_core.py",
                "atlas_lee_method.py"
            ]
            
            for module in critical_modules:
                if (self.staging_dir / module).exists():
                    validation_results["required_modules"].append(module)
            
            # Validation summary
            if validation_results["missing_files"]:
                self.log_step("Staging validation warnings", "WARNING", 
                            f"Missing files: {validation_results['missing_files']}")
            
            success = (
                validation_results["python_files"] >= 50 and  # At least 50 Python files
                validation_results["config_files"] >= 4 and   # At least 4 config files
                len(validation_results["required_modules"]) >= 4  # At least 4 critical modules
            )
            
            if success:
                self.log_step("Staging environment validation passed", "INFO", 
                            f"Files: {validation_results['python_files']} Python, "
                            f"{validation_results['config_files']} config")
            else:
                self.log_step("Staging environment validation failed", "ERROR", 
                            f"Insufficient files or missing critical components")
            
            return success
            
        except Exception as e:
            self.log_step("Staging validation error", "ERROR", str(e))
            return False
    
    def create_deployment_report(self) -> bool:
        """Create deployment report"""
        try:
            deployment_duration = (datetime.now() - self.start_time).total_seconds()
            
            report = {
                "deployment_info": {
                    "timestamp": self.start_time.isoformat(),
                    "duration_seconds": deployment_duration,
                    "staging_directory": str(self.staging_dir),
                    "source_directory": str(self.source_dir)
                },
                "deployment_log": self.deployment_log,
                "summary": {
                    "total_steps": len(self.deployment_log),
                    "successful_steps": len([log for log in self.deployment_log if log["status"] != "ERROR"]),
                    "errors": len([log for log in self.deployment_log if log["status"] == "ERROR"]),
                    "warnings": len([log for log in self.deployment_log if log["status"] == "WARNING"])
                },
                "next_steps": [
                    "1. Navigate to staging directory: cd " + str(self.staging_dir),
                    "2. Configure API keys in .env file",
                    "3. Run staging environment: ./start_staging.sh",
                    "4. Access staging server at: http://localhost:8002",
                    "5. Run comprehensive tests",
                    "6. Perform user acceptance testing"
                ]
            }
            
            with open(self.staging_dir / "deployment_report.json", "w") as f:
                json.dump(report, f, indent=2)
            
            self.log_step("Deployment report created", "INFO", "deployment_report.json")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to create deployment report", "ERROR", str(e))
            return False
    
    async def deploy_to_staging(self) -> bool:
        """Execute complete staging deployment"""
        try:
            logger.info("="*80)
            logger.info("A.T.L.A.S. STAGING DEPLOYMENT STARTED")
            logger.info("="*80)
            
            # Step 1: Create staging directory
            if not self.create_staging_directory():
                return False
            
            # Step 2: Copy source files
            if not self.copy_source_files():
                return False
            
            # Step 3: Create staging configuration
            if not self.create_staging_config():
                return False
            
            # Step 4: Validate staging environment
            if not self.validate_staging_environment():
                return False
            
            # Step 5: Create deployment report
            if not self.create_deployment_report():
                return False
            
            # Success summary
            deployment_duration = (datetime.now() - self.start_time).total_seconds()
            
            logger.info("="*80)
            logger.info("A.T.L.A.S. STAGING DEPLOYMENT COMPLETED SUCCESSFULLY")
            logger.info(f"Duration: {deployment_duration:.2f} seconds")
            logger.info(f"Staging Directory: {self.staging_dir}")
            logger.info("="*80)
            
            print("\n🎉 STAGING DEPLOYMENT SUCCESSFUL!")
            print(f"📁 Staging Directory: {self.staging_dir}")
            print(f"⏱️  Deployment Time: {deployment_duration:.2f} seconds")
            print("\n📋 NEXT STEPS:")
            print("1. cd " + str(self.staging_dir))
            print("2. Configure API keys in .env file")
            print("3. ./start_staging.sh")
            print("4. Access: http://localhost:8002")
            print("5. Run comprehensive tests")
            
            return True
            
        except Exception as e:
            self.log_step("Staging deployment failed", "ERROR", str(e))
            logger.error(f"STAGING DEPLOYMENT FAILED: {e}")
            return False


async def main():
    """Main deployment function"""
    deployment = StagingDeployment()
    success = await deployment.deploy_to_staging()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
