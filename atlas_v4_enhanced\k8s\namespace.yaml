apiVersion: v1
kind: Namespace
metadata:
  name: atlas-trading
  labels:
    name: atlas-trading
    environment: production
    app: atlas-multi-agent-system
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: atlas-resource-quota
  namespace: atlas-trading
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: atlas-limit-range
  namespace: atlas-trading
spec:
  limits:
  - default:
      cpu: "2"
      memory: "4Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
