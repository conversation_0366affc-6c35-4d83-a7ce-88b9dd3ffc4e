"""
A.T.L.A.S. Enhanced Intelligence Engine
Comprehensive web search and news capabilities leveraging Grok AI
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field

# A.T.L.A.S. imports
from models import EngineStatus
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine, GrokRequest, GrokResponse,
    GrokTaskType, GrokCapability, MARKET_SEARCH_CONFIGS
)
# from atlas_ml_analytics import AtlasMLAnalytics  # Temporarily disabled due to import issues

logger = logging.getLogger(__name__)

@dataclass
class IntelligenceReport:
    """Comprehensive intelligence report structure"""
    symbol: str
    company_name: str
    report_timestamp: datetime
    report_type: str  # 'ceo_communications', 'social_sentiment', 'news_analysis', 'comprehensive'
    
    # Core intelligence data
    ceo_communications: List[Dict[str, Any]] = field(default_factory=list)
    social_sentiment: Dict[str, Any] = field(default_factory=dict)
    news_analysis: Dict[str, Any] = field(default_factory=dict)
    web_search_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # Analysis results
    overall_sentiment: str = "neutral"
    sentiment_score: float = 0.0
    confidence: float = 0.0
    key_themes: List[str] = field(default_factory=list)
    market_impact_score: float = 0.0
    urgency_level: str = "normal"  # 'low', 'normal', 'high', 'critical'
    
    # Recommendations
    trading_recommendation: str = ""
    risk_assessment: str = ""
    next_catalysts: List[str] = field(default_factory=list)

# Enhanced search configurations for intelligence gathering
ENHANCED_SEARCH_CONFIGS = {
    "ceo_communications": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 20,
            "return_citations": True,
            "sources": [
                {
                    "type": "news",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.gov", "bloomberg.com", "reuters.com",
                        "cnbc.com", "wsj.com", "marketwatch.com", "yahoo.com"
                    ],
                    "keywords": ["CEO", "earnings call", "executive", "announcement", "guidance"]
                },
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.apple.com", "ir.tesla.com", 
                        "microsoft.com/investor", "abc.xyz/investor"
                    ]
                }
            ]
        }
    },
    "executive_social": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 25,
            "return_citations": True,
            "sources": [
                {
                    "type": "x",
                    "included_x_handles": [
                        "tim_cook", "elonmusk", "satyanadella", "sundarpichai",
                        "ajassy", "jeffbezos", "warrenbuffett", "chamath"
                    ],
                    "post_favorite_count": 10,
                    "post_view_count": 100
                }
            ]
        }
    },
    "comprehensive_news": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 30,
            "return_citations": True,
            "sources": [
                {
                    "type": "news",
                    "country": "US",
                    "allowed_websites": [
                        "bloomberg.com", "reuters.com", "marketwatch.com",
                        "wsj.com", "cnbc.com", "yahoo.com", "barrons.com",
                        "fool.com", "seekingalpha.com", "benzinga.com"
                    ]
                },
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.gov", "federalreserve.gov"
                    ]
                }
            ]
        }
    },
    "internet_search": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 15,
            "return_citations": True,
            "sources": [
                {
                    "type": "web",
                    "country": "US",
                    "safe_search": False
                },
                {
                    "type": "news",
                    "country": "US",
                    "safe_search": False
                }
            ]
        }
    }
}

class AtlasEnhancedIntelligenceEngine:
    """Enhanced intelligence engine leveraging Grok AI for comprehensive market intelligence"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.grok_engine = None
        self.ml_analytics = None
        
        # Executive tracking data
        self.executive_profiles = {
            "AAPL": {"ceo": "Tim Cook", "twitter": "@tim_cook", "company": "Apple Inc."},
            "TSLA": {"ceo": "Elon Musk", "twitter": "@elonmusk", "company": "Tesla Inc."},
            "MSFT": {"ceo": "Satya Nadella", "twitter": "@satyanadella", "company": "Microsoft Corporation"},
            "GOOGL": {"ceo": "Sundar Pichai", "twitter": "@sundarpichai", "company": "Alphabet Inc."},
            "AMZN": {"ceo": "Andy Jassy", "twitter": "@ajassy", "company": "Amazon.com Inc."},
            "META": {"ceo": "Mark Zuckerberg", "twitter": "@zuck", "company": "Meta Platforms Inc."},
            "NVDA": {"ceo": "Jensen Huang", "twitter": "@jensenhuang", "company": "NVIDIA Corporation"},
            "BRK.B": {"ceo": "Warren Buffett", "twitter": "@warrenbuffett", "company": "Berkshire Hathaway"}
        }
        
    async def initialize(self) -> bool:
        """Initialize the enhanced intelligence engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize Grok integration engine
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if not success:
                logger.error("❌ Failed to initialize Grok integration")
                self.status = EngineStatus.ERROR
                return False
            
            # Initialize ML analytics for sentiment analysis (temporarily disabled)
            self.ml_analytics = None
            # self.ml_analytics = AtlasMLAnalytics()
            # await self.ml_analytics.initialize()
            
            # Merge enhanced search configs with existing ones
            MARKET_SEARCH_CONFIGS.update(ENHANCED_SEARCH_CONFIGS)
            
            self.status = "RUNNING"  # EngineStatus.RUNNING
            logger.info("✅ Enhanced Intelligence Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Enhanced Intelligence Engine: {e}")
            self.status = "ERROR"  # EngineStatus.ERROR
            return False
    
    async def get_ceo_communications(self, symbol: str, days_back: int = 7) -> Dict[str, Any]:
        """Get CEO communications and executive announcements"""
        try:
            if symbol not in self.executive_profiles:
                return {"error": f"No executive profile found for {symbol}"}
            
            profile = self.executive_profiles[symbol]
            ceo_name = profile["ceo"]
            company_name = profile["company"]
            
            # Search for CEO communications
            query = f"Recent announcements, interviews, and communications from {ceo_name} CEO of {company_name} {symbol} in the last {days_back} days"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="ceo_communications",
                symbol=symbol
            )
            
            if result.success:
                # Analyze sentiment of CEO communications
                sentiment_analysis = await self._analyze_content_sentiment(result.content)
                
                return {
                    "symbol": symbol,
                    "ceo_name": ceo_name,
                    "company_name": company_name,
                    "communications": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Failed to retrieve CEO communications: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error getting CEO communications for {symbol}: {e}")
            return {"error": str(e)}
    
    async def monitor_executive_social_media(self, symbol: str) -> Dict[str, Any]:
        """Monitor social media activity from executives and financial influencers"""
        try:
            if symbol not in self.executive_profiles:
                return {"error": f"No executive profile found for {symbol}"}
            
            profile = self.executive_profiles[symbol]
            ceo_name = profile["ceo"]
            twitter_handle = profile.get("twitter", "")
            
            # Search for executive social media activity
            query = f"Recent tweets and social media posts from {ceo_name} {twitter_handle} about {symbol} and company updates"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="executive_social",
                symbol=symbol
            )
            
            if result.success:
                # Analyze social sentiment
                sentiment_analysis = await self._analyze_content_sentiment(result.content)
                
                return {
                    "symbol": symbol,
                    "executive": ceo_name,
                    "social_activity": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Failed to retrieve social media data: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error monitoring social media for {symbol}: {e}")
            return {"error": str(e)}
    
    async def search_internet(self, query: str, symbol: str = None) -> Dict[str, Any]:
        """Search the internet for trading-relevant information"""
        try:
            # Enhance query for trading relevance
            enhanced_query = f"Financial and trading information: {query}"
            if symbol:
                enhanced_query += f" related to {symbol} stock"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=enhanced_query,
                search_config="internet_search",
                symbol=symbol
            )
            
            if result.success:
                return {
                    "query": query,
                    "symbol": symbol,
                    "results": result.content,
                    "sources": result.citations,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Internet search failed: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error in internet search: {e}")
            return {"error": str(e)}
    
    async def _analyze_content_sentiment(self, content: str) -> Dict[str, Any]:
        """Analyze sentiment of content using ML analytics"""
        try:
            if self.ml_analytics:
                return await self.ml_analytics.analyze_sentiment(content)
            else:
                # Fallback basic sentiment analysis
                return {
                    "sentiment": "neutral",
                    "sentiment_score": 0.0,
                    "confidence": 0.5,
                    "model": "fallback"
                }
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {
                "sentiment": "neutral",
                "sentiment_score": 0.0,
                "confidence": 0.0,
                "model": "error",
                "error": str(e)
            }
