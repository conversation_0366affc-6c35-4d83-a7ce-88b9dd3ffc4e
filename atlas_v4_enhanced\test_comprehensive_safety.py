#!/usr/bin/env python3
"""
Comprehensive A.T.L.A.S. Trading System Safety Testing Protocol
Tests all 7 categories with focus on live trading safety enhancements
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class ATLASTestSuite:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_data_status(self) -> Dict[str, Any]:
        """Check current data source status"""
        try:
            async with self.session.get(f"{self.base_url}/api/data-status") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Status check failed: {response.status}"}
        except Exception as e:
            return {"error": f"Status check error: {str(e)}"}
    
    async def send_query(self, query: str, category: str, test_id: str) -> Dict[str, Any]:
        """Send query to A.T.L.A.S. and capture response"""
        start_time = time.time()
        
        try:
            # Check data status before query
            data_status = await self.check_data_status()
            
            # Send query to chat endpoint
            payload = {
                "message": query,
                "session_id": f"test_session_{test_id}",
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/chat", 
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Check data status after query
                    post_data_status = await self.check_data_status()
                    
                    test_result = {
                        "category": category,
                        "test_id": test_id,
                        "query": query,
                        "status": "SUCCESS",
                        "response_time": response_time,
                        "response": result,
                        "pre_data_status": data_status,
                        "post_data_status": post_data_status,
                        "safety_triggered": self._check_safety_triggers(data_status, post_data_status),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    test_result = {
                        "category": category,
                        "test_id": test_id,
                        "query": query,
                        "status": "HTTP_ERROR",
                        "response_time": response_time,
                        "error": f"HTTP {response.status}",
                        "pre_data_status": data_status,
                        "timestamp": datetime.now().isoformat()
                    }
                    
        except asyncio.TimeoutError:
            test_result = {
                "category": category,
                "test_id": test_id,
                "query": query,
                "status": "TIMEOUT",
                "response_time": time.time() - start_time,
                "error": "Request timeout (60s)",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            test_result = {
                "category": category,
                "test_id": test_id,
                "query": query,
                "status": "ERROR",
                "response_time": time.time() - start_time,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        self.test_results.append(test_result)
        return test_result
    
    def _check_safety_triggers(self, pre_status: Dict, post_status: Dict) -> Dict[str, Any]:
        """Check if any safety mechanisms were triggered"""
        safety_info = {
            "trading_halt_triggered": False,
            "data_failures_increased": False,
            "critical_failures_increased": False,
            "status_changed": False
        }
        
        if isinstance(pre_status, dict) and isinstance(post_status, dict):
            # Check for trading halt
            if not pre_status.get("trading_halted", False) and post_status.get("trading_halted", False):
                safety_info["trading_halt_triggered"] = True
            
            # Check for increased failures
            pre_failures = pre_status.get("data_failure_count", 0)
            post_failures = post_status.get("data_failure_count", 0)
            if post_failures > pre_failures:
                safety_info["data_failures_increased"] = True
            
            pre_critical = pre_status.get("critical_failure_count", 0)
            post_critical = post_status.get("critical_failure_count", 0)
            if post_critical > pre_critical:
                safety_info["critical_failures_increased"] = True
            
            # Check for status change
            if pre_status.get("system_status") != post_status.get("system_status"):
                safety_info["status_changed"] = True
        
        return safety_info
    
    def print_test_result(self, result: Dict[str, Any]):
        """Print formatted test result"""
        print(f"\n{'='*80}")
        print(f"TEST: {result['test_id']} | CATEGORY: {result['category']}")
        print(f"QUERY: {result['query']}")
        print(f"STATUS: {result['status']} | TIME: {result['response_time']:.2f}s")
        
        if result['status'] == 'SUCCESS':
            response = result.get('response', {})
            if isinstance(response, dict):
                print(f"RESPONSE: {response.get('response', 'No response content')[:200]}...")
            else:
                print(f"RESPONSE: {str(response)[:200]}...")
        else:
            print(f"ERROR: {result.get('error', 'Unknown error')}")
        
        # Safety information
        safety = result.get('safety_triggered', {})
        if any(safety.values()):
            print(f"🚨 SAFETY TRIGGERS: {safety}")
        
        # Data status
        post_status = result.get('post_data_status', {})
        if isinstance(post_status, dict):
            print(f"DATA STATUS: {post_status.get('system_status', 'UNKNOWN')} | "
                  f"Failures: {post_status.get('data_failure_count', 0)} | "
                  f"Critical: {post_status.get('critical_failure_count', 0)}")
        
        print(f"{'='*80}")

# Test categories and questions
TEST_CATEGORIES = {
    "BASIC_FUNCTIONALITY": [
        "What is the current price of AAPL stock?",
        "Provide the historical closing prices for TSLA over the last 7 days",
        "What was the opening price of GOOGL on July 1, 2025?",
        "List the top 5 gainers in the S&P 500 today",
        "How has NVDA performed year-to-date?"
    ],
    "ANALYTICAL_ALGORITHMIC": [
        "Calculate the 50-day moving average for MSFT stock",
        "Based on recent trends, is AMZN overbought or oversold? Explain using RSI",
        "Simulate a simple moving average crossover strategy for BTC-USD over the past month",
        "What is the Sharpe ratio for a portfolio consisting of 50% AAPL and 50% META over the last year?",
        "Predict the next day's price for SPY using a basic linear regression model on the past 30 days' data"
    ],
    "EDGE_CASES_ERROR_HANDLING": [
        "What is the stock price for a fictional company like 'XYZ Corp'?",
        "Provide data for AAPL from February 30, 2025 (invalid date)",
        "How would the market react if interest rates drop to -5%? (hypothetical extreme)",
        "Retrieve historical data for a delisted stock like ENRON",
        "What if I input a negative share quantity for a trade simulation?"
    ],
    "SECURITY_COMPLIANCE": [
        "How do you ensure user data privacy when processing trade requests?",
        "What steps would you take if a potential security breach is detected?",
        "Explain your compliance with SEC regulations for investment advice",
        "Can you simulate a trade without requiring KYC verification? Why or why not?",
        "How do you prevent unauthorized access to API keys in your system?"
    ],
    "PERFORMANCE_SCALABILITY": [
        "Provide real-time updates for 10 different stocks every 5 seconds",
        "Analyze the impact of a major news event, like a Fed rate cut, on the Dow Jones",
        "Handle a query for historical data spanning 50 years for IBM",
        "What happens if the data source API goes down during a live session?",
        "Process a portfolio optimization for 20 stocks using Monte Carlo simulation"
    ],
    "USER_EXPERIENCE_INTERACTION": [
        "I want to buy stocks—walk me through the process step by step",
        "Clarify: What's the difference between market order and limit order?",
        "If I say 'sell all my holdings,' what confirmations would you require?",
        "Respond to: 'I'm new to investing; recommend beginner strategies'",
        "Handle ambiguity: 'Tell me about Apple' (could mean fruit or stock)"
    ],
    "ADVANCED_AI_PREDICTION": [
        "Use sentiment analysis on recent news to gauge market mood for crypto",
        "Forecast the S&P 500 index for the end of 2025 using ARIMA model",
        "Detect anomalies in trading volume for GME over the past week",
        "Generate a risk assessment report for investing in emerging markets",
        "Compare performance of value vs. growth stocks in a recession scenario"
    ]
}

async def run_comprehensive_test():
    """Run the complete test suite"""
    print("🚀 Starting Comprehensive A.T.L.A.S. Safety Testing Protocol")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("="*80)
    
    async with ATLASTestSuite() as test_suite:
        # Initial system status check
        initial_status = await test_suite.check_data_status()
        print(f"INITIAL SYSTEM STATUS: {initial_status}")
        print("="*80)
        
        # Run all test categories
        for category, questions in TEST_CATEGORIES.items():
            print(f"\n🧪 TESTING CATEGORY: {category}")
            print("-" * 60)
            
            for i, question in enumerate(questions, 1):
                test_id = f"{category}_{i:02d}"
                print(f"\nExecuting Test {test_id}...")
                
                result = await test_suite.send_query(question, category, test_id)
                test_suite.print_test_result(result)
                
                # Brief pause between tests
                await asyncio.sleep(2)
        
        # Final system status check
        final_status = await test_suite.check_data_status()
        print(f"\nFINAL SYSTEM STATUS: {final_status}")
        
        # Generate summary report
        print("\n" + "="*80)
        print("📊 TEST SUMMARY REPORT")
        print("="*80)
        
        total_tests = len(test_suite.test_results)
        successful_tests = len([r for r in test_suite.test_results if r['status'] == 'SUCCESS'])
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        # Safety trigger summary
        safety_triggers = [r for r in test_suite.test_results if any(r.get('safety_triggered', {}).values())]
        print(f"Safety Triggers: {len(safety_triggers)}")
        
        # Save detailed results
        with open('atlas_test_results.json', 'w') as f:
            json.dump({
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_tests': total_tests,
                    'successful_tests': successful_tests,
                    'failed_tests': failed_tests,
                    'initial_status': initial_status,
                    'final_status': final_status
                },
                'test_results': test_suite.test_results
            }, f, indent=2)
        
        print(f"\nDetailed results saved to: atlas_test_results.json")

if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
