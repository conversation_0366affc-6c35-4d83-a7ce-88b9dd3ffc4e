#!/usr/bin/env python3
"""
A.T.L.A.S. Grok Capabilities Test Suite
Test all advanced Grok features: web search, reasoning, tool calling, vision, etc.
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from atlas_grok_integration import (
    GrokAPIClient, AtlasGrokIntegrationEngine, 
    GrokCapability, GrokTaskType, GrokRequest,
    ATLAS_TRADING_TOOLS
)
from config import get_api_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GrokCapabilitiesTest:
    """Comprehensive test suite for Grok capabilities"""
    
    def __init__(self):
        self.grok_config = get_api_config("grok")
        self.client = None
        self.engine = None
        self.test_results = {}
        
    async def initialize(self):
        """Initialize Grok client and engine"""
        try:
            # Initialize Grok API client
            self.client = GrokAPIClient()
            await self.client.initialize()

            # Initialize Grok integration engine
            self.engine = AtlasGrokIntegrationEngine()
            await self.engine.initialize()
            
            logger.info("✅ Grok client and engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Grok: {e}")
            return False
    
    async def test_basic_reasoning(self):
        """Test basic reasoning capabilities"""
        logger.info("🧠 Testing Grok reasoning capabilities...")
        
        try:
            request = GrokRequest(
                task_type=GrokTaskType.LOGICAL_REASONING,
                capability=GrokCapability.REASONING,
                prompt="Analyze AAPL stock: If Apple's revenue increased 15% but margins decreased 2%, what does this suggest about their business strategy?",
                temperature=0.2,
                max_tokens=200
            )
            
            response = await self.client.make_request(request)
            
            if response.success and response.content:
                logger.info("✅ Reasoning test passed")
                self.test_results["reasoning"] = {
                    "status": "PASS",
                    "confidence": response.confidence,
                    "response_length": len(response.content)
                }
                return True
            else:
                logger.error("❌ Reasoning test failed")
                self.test_results["reasoning"] = {"status": "FAIL", "error": "No valid response"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Reasoning test error: {e}")
            self.test_results["reasoning"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def test_web_search(self):
        """Test real-time web search capabilities"""
        logger.info("🌐 Testing Grok web search capabilities...")
        
        try:
            # Test live search request
            search_result = await self.client.make_live_search_request(
                query="NVDA stock latest earnings news today",
                search_config="real_time_news",
                symbol="NVDA"
            )
            
            if search_result and search_result.get("success"):
                logger.info("✅ Web search test passed")
                self.test_results["web_search"] = {
                    "status": "PASS",
                    "sources_found": search_result.get("sources_count", 0),
                    "search_quality": search_result.get("search_quality", 0)
                }
                return True
            else:
                logger.error("❌ Web search test failed")
                self.test_results["web_search"] = {"status": "FAIL", "error": "No search results"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Web search test error: {e}")
            self.test_results["web_search"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def test_function_calling(self):
        """Test function calling capabilities"""
        logger.info("🔧 Testing Grok function calling capabilities...")
        
        try:
            # Test function calling with trading tools
            response = await self.client.make_function_calling_request(
                query="What trading tools are available for analyzing TSLA?",
                available_tools=["get_market_data", "analyze_technical_indicators", "get_news_sentiment"]
            )
            
            if response.success and response.content:
                logger.info("✅ Function calling test passed")
                self.test_results["function_calling"] = {
                    "status": "PASS",
                    "tools_available": len(ATLAS_TRADING_TOOLS),
                    "response_quality": response.confidence
                }
                return True
            else:
                logger.error("❌ Function calling test failed")
                self.test_results["function_calling"] = {"status": "FAIL", "error": "No function response"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Function calling test error: {e}")
            self.test_results["function_calling"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def test_sentiment_analysis(self):
        """Test sentiment analysis capabilities"""
        logger.info("💭 Testing Grok sentiment analysis capabilities...")
        
        try:
            request = GrokRequest(
                task_type=GrokTaskType.REAL_TIME_SENTIMENT,
                capability=GrokCapability.SENTIMENT_ANALYSIS,
                prompt="Analyze the market sentiment from this text: 'AAPL beats earnings expectations, revenue up 12%, iPhone sales strong, guidance raised for next quarter'",
                temperature=0.1,
                max_tokens=150
            )
            
            response = await self.client.make_request(request)
            
            if response.success and response.content:
                logger.info("✅ Sentiment analysis test passed")
                self.test_results["sentiment_analysis"] = {
                    "status": "PASS",
                    "confidence": response.confidence,
                    "analysis_quality": "high" if "positive" in response.content.lower() else "medium"
                }
                return True
            else:
                logger.error("❌ Sentiment analysis test failed")
                self.test_results["sentiment_analysis"] = {"status": "FAIL", "error": "No sentiment response"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Sentiment analysis test error: {e}")
            self.test_results["sentiment_analysis"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def test_pattern_recognition(self):
        """Test pattern recognition capabilities"""
        logger.info("🔍 Testing Grok pattern recognition capabilities...")
        
        try:
            request = GrokRequest(
                task_type=GrokTaskType.PATTERN_DETECTION,
                capability=GrokCapability.PATTERN_RECOGNITION,
                prompt="Identify trading patterns in this scenario: Stock price broke above 20-day moving average with increasing volume, RSI at 65, MACD showing bullish crossover",
                temperature=0.2,
                max_tokens=200
            )
            
            response = await self.client.make_request(request)
            
            if response.success and response.content:
                logger.info("✅ Pattern recognition test passed")
                self.test_results["pattern_recognition"] = {
                    "status": "PASS",
                    "confidence": response.confidence,
                    "patterns_identified": "bullish" in response.content.lower()
                }
                return True
            else:
                logger.error("❌ Pattern recognition test failed")
                self.test_results["pattern_recognition"] = {"status": "FAIL", "error": "No pattern response"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Pattern recognition test error: {e}")
            self.test_results["pattern_recognition"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def test_intent_detection(self):
        """Test AI-powered intent detection"""
        logger.info("🎯 Testing Grok intent detection capabilities...")
        
        try:
            intent_prompt = """Analyze this user message for trading intent: "What's AAPL trading at right now?"
            
            Respond with JSON: {"type": "stock_analysis", "symbols": ["AAPL"], "confidence": 0.95, "reasoning": "User asking for current stock price"}"""
            
            request = GrokRequest(
                task_type=GrokTaskType.LOGICAL_REASONING,
                capability=GrokCapability.REASONING,
                prompt=intent_prompt,
                temperature=0.1,
                max_tokens=100
            )
            
            response = await self.client.make_request(request)
            
            if response.success and response.content:
                # Try to parse JSON response
                try:
                    intent_data = json.loads(response.content.strip())
                    if isinstance(intent_data, dict) and "type" in intent_data:
                        logger.info("✅ Intent detection test passed")
                        self.test_results["intent_detection"] = {
                            "status": "PASS",
                            "detected_intent": intent_data.get("type"),
                            "confidence": intent_data.get("confidence", 0),
                            "symbols_extracted": intent_data.get("symbols", [])
                        }
                        return True
                except json.JSONDecodeError:
                    pass
                    
                logger.error("❌ Intent detection test failed - invalid JSON")
                self.test_results["intent_detection"] = {"status": "FAIL", "error": "Invalid JSON response"}
                return False
            else:
                logger.error("❌ Intent detection test failed")
                self.test_results["intent_detection"] = {"status": "FAIL", "error": "No intent response"}
                return False
                
        except Exception as e:
            logger.error(f"❌ Intent detection test error: {e}")
            self.test_results["intent_detection"] = {"status": "ERROR", "error": str(e)}
            return False
    
    async def run_all_tests(self):
        """Run all Grok capability tests"""
        logger.info("🚀 Starting comprehensive Grok capabilities test...")
        
        if not await self.initialize():
            logger.error("❌ Failed to initialize Grok - aborting tests")
            return False
        
        tests = [
            ("Basic Reasoning", self.test_basic_reasoning),
            ("Web Search", self.test_web_search),
            ("Function Calling", self.test_function_calling),
            ("Sentiment Analysis", self.test_sentiment_analysis),
            ("Pattern Recognition", self.test_pattern_recognition),
            ("Intent Detection", self.test_intent_detection)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running {test_name} Test ---")
            try:
                if await test_func():
                    passed += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        # Generate summary report
        success_rate = (passed / total) * 100
        logger.info(f"\n🎯 GROK CAPABILITIES TEST SUMMARY")
        logger.info(f"Tests Passed: {passed}/{total} ({success_rate:.1f}%)")
        logger.info(f"Grok API Status: {'✅ FULLY OPERATIONAL' if passed >= 4 else '⚠️ PARTIAL' if passed >= 2 else '❌ CRITICAL ISSUES'}")
        
        # Save detailed results
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total,
                "passed": passed,
                "success_rate": success_rate,
                "status": "OPERATIONAL" if passed >= 4 else "PARTIAL" if passed >= 2 else "CRITICAL"
            },
            "detailed_results": self.test_results,
            "grok_config": {
                "model": self.grok_config.get("model"),
                "base_url": self.grok_config.get("base_url"),
                "available": self.grok_config.get("available")
            }
        }
        
        with open("grok_capabilities_test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📊 Detailed report saved to: grok_capabilities_test_report.json")
        
        return passed >= 4  # Consider successful if at least 4/6 tests pass

async def main():
    """Main test execution"""
    test_suite = GrokCapabilitiesTest()
    success = await test_suite.run_all_tests()
    
    if success:
        logger.info("🎉 Grok integration is ready for production use!")
        return 0
    else:
        logger.error("⚠️ Grok integration has issues that need attention")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
