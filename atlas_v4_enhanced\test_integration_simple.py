"""
Simple Integration Test for A.T.L.A.S. Multi-Agent System
Quick test to verify the multi-agent system is working correctly
"""

import asyncio
import logging
import time
from datetime import datetime

# Import the multi-agent components
from atlas_multi_agent_orchestrator import (
    AtlasMultiAgentOrchestrator, OrchestrationRequest, 
    IntentType, OrchestrationMode, TaskPriority
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_basic_functionality():
    """Test basic multi-agent functionality"""
    logger.info("🚀 Starting A.T.L.A.S. Multi-Agent System Integration Test")
    
    try:
        # Initialize the orchestrator
        logger.info("📋 Initializing Multi-Agent Orchestrator...")
        orchestrator = AtlasMultiAgentOrchestrator()
        
        success = await orchestrator.initialize()
        if not success:
            logger.error("❌ Failed to initialize orchestrator")
            return False
        
        logger.info("✅ Orchestrator initialized successfully")
        
        # Get system status
        status = orchestrator.get_orchestrator_status()
        logger.info(f"📊 System Status: {status['status']}")
        logger.info(f"🤖 Total Agents: {status['total_agents']}")
        logger.info(f"🟢 Active Agents: {status['active_agents']}")
        
        # Test 1: Data Analysis Request
        logger.info("\n🔍 Test 1: Data Analysis Request")
        request1 = OrchestrationRequest(
            request_id="test_data_analysis",
            intent=IntentType.DATA_ANALYSIS,
            symbol="AAPL",
            input_data={"analysis_type": "basic"},
            orchestration_mode=OrchestrationMode.PARALLEL,
            priority=TaskPriority.HIGH,
            timeout_seconds=60
        )
        
        start_time = time.time()
        result1 = await orchestrator.process_request(request1)
        end_time = time.time()
        
        logger.info(f"✅ Data Analysis completed in {end_time - start_time:.2f} seconds")
        logger.info(f"📈 Confidence Score: {result1.confidence_score:.3f}")
        logger.info(f"🎯 Success: {result1.success}")
        
        # Test 2: Pattern Detection Request
        logger.info("\n📊 Test 2: Pattern Detection Request")
        request2 = OrchestrationRequest(
            request_id="test_pattern_detection",
            intent=IntentType.PATTERN_DETECTION,
            symbol="TSLA",
            input_data={"timeframe": "1Day"},
            orchestration_mode=OrchestrationMode.PARALLEL,
            priority=TaskPriority.MEDIUM,
            timeout_seconds=60
        )
        
        start_time = time.time()
        result2 = await orchestrator.process_request(request2)
        end_time = time.time()
        
        logger.info(f"✅ Pattern Detection completed in {end_time - start_time:.2f} seconds")
        logger.info(f"📈 Confidence Score: {result2.confidence_score:.3f}")
        logger.info(f"🎯 Success: {result2.success}")
        
        # Test 3: Comprehensive Analysis (Hybrid Mode)
        logger.info("\n🔬 Test 3: Comprehensive Analysis (Hybrid Mode)")
        request3 = OrchestrationRequest(
            request_id="test_comprehensive",
            intent=IntentType.COMPREHENSIVE_ANALYSIS,
            symbol="MSFT",
            input_data={
                "current_price": 350.25,
                "analysis_depth": "full"
            },
            orchestration_mode=OrchestrationMode.HYBRID,
            priority=TaskPriority.HIGH,
            timeout_seconds=120,
            require_validation=True
        )
        
        start_time = time.time()
        result3 = await orchestrator.process_request(request3)
        end_time = time.time()
        
        logger.info(f"✅ Comprehensive Analysis completed in {end_time - start_time:.2f} seconds")
        logger.info(f"📈 Confidence Score: {result3.confidence_score:.3f}")
        logger.info(f"🎯 Success: {result3.success}")
        logger.info(f"🔍 Validation Passed: {result3.validation_result is not None}")
        
        if result3.final_recommendation:
            logger.info("💡 Final Recommendation Generated:")
            logger.info(f"   Symbol: {result3.final_recommendation.get('symbol', 'N/A')}")
            logger.info(f"   Data Quality: {result3.final_recommendation.get('data_quality', {}).get('confidence', 'N/A')}")
            logger.info(f"   Technical Analysis: {result3.final_recommendation.get('technical_analysis', {}).get('signal_strength', 'N/A')}")
        
        # Test 4: Agent Status Check
        logger.info("\n🤖 Test 4: Individual Agent Status Check")
        for role, agent in orchestrator.agents.items():
            agent_status = agent.get_status()
            logger.info(f"   {role.value}: {agent_status['status']} (Tasks: {agent_status['metrics']['tasks_completed']})")
        
        # Performance Summary
        logger.info("\n📊 Performance Summary:")
        final_status = orchestrator.get_orchestrator_status()
        perf_metrics = final_status.get('performance_metrics', {})
        logger.info(f"   Total Requests Processed: {perf_metrics.get('total_requests_processed', 0)}")
        logger.info(f"   Successful Requests: {perf_metrics.get('successful_requests', 0)}")
        logger.info(f"   Success Rate: {perf_metrics.get('success_rate', 0):.1f}%")
        logger.info(f"   Average Processing Time: {perf_metrics.get('average_processing_time', 0):.2f}s")
        
        logger.info("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concurrent_requests():
    """Test concurrent request handling"""
    logger.info("\n🔄 Testing Concurrent Request Handling...")
    
    try:
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create multiple concurrent requests
        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]
        requests = []
        
        for i, symbol in enumerate(symbols):
            request = OrchestrationRequest(
                request_id=f"concurrent_test_{i}",
                intent=IntentType.PATTERN_DETECTION,
                symbol=symbol,
                input_data={"test_id": i},
                orchestration_mode=OrchestrationMode.PARALLEL,
                priority=TaskPriority.MEDIUM,
                timeout_seconds=45
            )
            requests.append(request)
        
        # Execute all requests concurrently
        logger.info(f"🚀 Executing {len(requests)} concurrent requests...")
        start_time = time.time()
        
        results = await asyncio.gather(*[
            orchestrator.process_request(req) for req in requests
        ], return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        failed_results = [r for r in results if isinstance(r, Exception)]
        
        logger.info(f"✅ Concurrent test completed in {total_time:.2f} seconds")
        logger.info(f"📊 Successful requests: {len(successful_results)}/{len(requests)}")
        logger.info(f"❌ Failed requests: {len(failed_results)}")
        
        if successful_results:
            avg_confidence = sum(r.confidence_score for r in successful_results) / len(successful_results)
            logger.info(f"📈 Average confidence score: {avg_confidence:.3f}")
        
        if failed_results:
            logger.warning("⚠️ Some requests failed:")
            for i, error in enumerate(failed_results):
                logger.warning(f"   Request {i}: {error}")
        
        return len(successful_results) >= len(requests) * 0.8  # 80% success rate
        
    except Exception as e:
        logger.error(f"❌ Concurrent test failed: {e}")
        return False

async def test_error_handling():
    """Test error handling capabilities"""
    logger.info("\n🛡️ Testing Error Handling...")
    
    try:
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Test 1: Invalid symbol
        logger.info("🔍 Test 1: Invalid Symbol Handling")
        request1 = OrchestrationRequest(
            request_id="error_test_invalid_symbol",
            intent=IntentType.DATA_ANALYSIS,
            symbol="INVALID_SYMBOL_XYZ",
            input_data={},
            timeout_seconds=30
        )
        
        result1 = await orchestrator.process_request(request1)
        logger.info(f"✅ Invalid symbol handled gracefully: {result1 is not None}")
        
        # Test 2: Very short timeout
        logger.info("🔍 Test 2: Timeout Handling")
        request2 = OrchestrationRequest(
            request_id="error_test_timeout",
            intent=IntentType.COMPREHENSIVE_ANALYSIS,
            symbol="AAPL",
            input_data={},
            timeout_seconds=1  # Very short timeout
        )
        
        start_time = time.time()
        result2 = await orchestrator.process_request(request2)
        end_time = time.time()
        
        logger.info(f"✅ Timeout handled gracefully in {end_time - start_time:.2f}s: {result2 is not None}")
        
        # Test 3: Empty input data
        logger.info("🔍 Test 3: Empty Input Data Handling")
        request3 = OrchestrationRequest(
            request_id="error_test_empty_data",
            intent=IntentType.SENTIMENT_ANALYSIS,
            symbol="AAPL",
            input_data={},  # Empty data
            timeout_seconds=30
        )
        
        result3 = await orchestrator.process_request(request3)
        logger.info(f"✅ Empty data handled gracefully: {result3 is not None}")
        
        logger.info("🛡️ Error handling tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {e}")
        return False

async def main():
    """Main test runner"""
    logger.info("=" * 80)
    logger.info("🚀 A.T.L.A.S. Multi-Agent System Integration Test Suite")
    logger.info("=" * 80)
    
    test_results = []
    
    # Run basic functionality test
    logger.info("\n" + "=" * 50)
    logger.info("📋 BASIC FUNCTIONALITY TEST")
    logger.info("=" * 50)
    basic_test_result = await test_basic_functionality()
    test_results.append(("Basic Functionality", basic_test_result))
    
    # Run concurrent requests test
    logger.info("\n" + "=" * 50)
    logger.info("🔄 CONCURRENT REQUESTS TEST")
    logger.info("=" * 50)
    concurrent_test_result = await test_concurrent_requests()
    test_results.append(("Concurrent Requests", concurrent_test_result))
    
    # Run error handling test
    logger.info("\n" + "=" * 50)
    logger.info("🛡️ ERROR HANDLING TEST")
    logger.info("=" * 50)
    error_test_result = await test_error_handling()
    test_results.append(("Error Handling", error_test_result))
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 FINAL TEST RESULTS")
    logger.info("=" * 80)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / len(test_results)) * 100
    logger.info(f"\nOverall Success Rate: {success_rate:.1f}% ({passed_tests}/{len(test_results)} tests passed)")
    
    if success_rate >= 80:
        logger.info("🎉 Multi-Agent System is functioning correctly!")
        return True
    else:
        logger.error("❌ Multi-Agent System has issues that need to be addressed.")
        return False

if __name__ == "__main__":
    # Run the integration test
    success = asyncio.run(main())
    exit(0 if success else 1)
