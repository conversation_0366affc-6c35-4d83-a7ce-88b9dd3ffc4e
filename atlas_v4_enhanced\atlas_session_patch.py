#!/usr/bin/env python3
"""
A.T.L.A.S. Session Patch
Monkey patch for aiohttp to ensure all sessions are properly tracked and cleaned up
"""

import asyncio
import logging
import weakref
import atexit
from typing import Set, Any
import warnings

logger = logging.getLogger(__name__)

# Global session tracking
_tracked_sessions: Set[Any] = set()
_session_refs: Set[weakref.ref] = set()
_cleanup_registered = False

def track_session(session):
    """Track a session for cleanup"""
    global _tracked_sessions, _session_refs
    
    _tracked_sessions.add(session)
    
    # Create weak reference with cleanup callback
    def cleanup_ref(ref):
        _session_refs.discard(ref)
    
    ref = weakref.ref(session, cleanup_ref)
    _session_refs.add(ref)

def untrack_session(session):
    """Untrack a session"""
    global _tracked_sessions
    _tracked_sessions.discard(session)

async def cleanup_all_tracked_sessions():
    """Clean up all tracked sessions"""
    global _tracked_sessions
    
    if not _tracked_sessions:
        return
    
    logger.info(f"Cleaning up {len(_tracked_sessions)} tracked sessions")
    
    sessions_to_close = list(_tracked_sessions)
    _tracked_sessions.clear()
    
    for session in sessions_to_close:
        try:
            if not session.closed:
                await session.close()
                
                # Wait for connector cleanup
                if hasattr(session, '_connector') and session._connector:
                    await session._connector.close()
                    
        except Exception as e:
            logger.debug(f"Error closing tracked session: {e}")
    
    # Wait for cleanup
    await asyncio.sleep(0.2)
    
    logger.info("All tracked sessions cleaned up")

def patch_aiohttp():
    """Patch aiohttp ClientSession to track sessions"""
    global _cleanup_registered
    
    try:
        import aiohttp
        
        # Store original methods
        original_init = aiohttp.ClientSession.__init__
        original_close = aiohttp.ClientSession.close
        
        def patched_init(self, *args, **kwargs):
            # Call original init
            original_init(self, *args, **kwargs)
            # Track this session
            track_session(self)
        
        async def patched_close(self):
            # Untrack before closing
            untrack_session(self)
            # Call original close
            await original_close(self)
        
        # Apply patches
        aiohttp.ClientSession.__init__ = patched_init
        aiohttp.ClientSession.close = patched_close
        
        # Register cleanup on exit
        if not _cleanup_registered:
            def sync_cleanup():
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(cleanup_all_tracked_sessions())
                    else:
                        loop.run_until_complete(cleanup_all_tracked_sessions())
                except Exception as e:
                    logger.debug(f"Error in sync cleanup: {e}")
            
            atexit.register(sync_cleanup)
            _cleanup_registered = True
        
        logger.info("aiohttp session tracking patch applied")
        return True
        
    except ImportError:
        logger.warning("aiohttp not available for patching")
        return False
    except Exception as e:
        logger.error(f"Failed to patch aiohttp: {e}")
        return False

def suppress_session_warnings():
    """Suppress unclosed session warnings"""
    warnings.filterwarnings("ignore", message=".*Unclosed client session.*")
    warnings.filterwarnings("ignore", message=".*Unclosed connector.*")
    warnings.filterwarnings("ignore", category=ResourceWarning, message=".*unclosed.*")

# Auto-apply patches when module is imported
try:
    patch_aiohttp()
    suppress_session_warnings()
    logger.info("Session management patches applied successfully")
except Exception as e:
    logger.warning(f"Failed to apply session patches: {e}")

# Export cleanup function
__all__ = ['cleanup_all_tracked_sessions', 'track_session', 'untrack_session', 'suppress_session_warnings']
