"""
A.T.L.A.S. Multi-Agent System Test Suite
Comprehensive testing for the multi-agent architecture
"""

import asyncio
import pytest
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

# Test imports
from atlas_multi_agent_orchestrator import (
    AtlasMultiAgentOrchestrator, OrchestrationRequest, OrchestrationResult,
    IntentType, OrchestrationMode, TaskPriority
)
from atlas_multi_agent_core import MultiAgentTask, AgentRole
from atlas_data_validation_agent import AtlasDataValidationAgent
from atlas_pattern_detection_agent import AtlasPatternDetectionAgent
from atlas_analysis_agent import AtlasAnalysisAgent
from atlas_risk_management_agent import AtlasRiskManagementAgent
from atlas_trade_execution_agent import AtlasTradeExecutionAgent
from atlas_validation_agent import AtlasValidationAgent

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# TEST FIXTURES AND UTILITIES
# ============================================================================

@pytest.fixture
async def orchestrator():
    """Create and initialize orchestrator for testing"""
    orchestrator = AtlasMultiAgentOrchestrator()
    await orchestrator.initialize()
    return orchestrator

@pytest.fixture
def sample_market_data():
    """Sample market data for testing"""
    return {
        "symbol": "AAPL",
        "current_price": 150.25,
        "volume": 1000000,
        "market_cap": 2500000000000,
        "pe_ratio": 25.5,
        "volatility": 0.25,
        "news_sentiment": 0.3,
        "technical_indicators": {
            "rsi": 65.2,
            "macd": 1.2,
            "sma_20": 148.5,
            "sma_50": 145.0
        }
    }

@pytest.fixture
def sample_orchestration_request():
    """Sample orchestration request for testing"""
    return OrchestrationRequest(
        request_id="test_001",
        intent=IntentType.COMPREHENSIVE_ANALYSIS,
        symbol="AAPL",
        input_data={
            "current_price": 150.25,
            "analysis_type": "comprehensive"
        },
        orchestration_mode=OrchestrationMode.HYBRID,
        priority=TaskPriority.HIGH,
        timeout_seconds=120,
        require_validation=True,
        confidence_threshold=0.8
    )

# ============================================================================
# UNIT TESTS FOR INDIVIDUAL AGENTS
# ============================================================================

class TestDataValidationAgent:
    """Test suite for Data Validation Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test data validation agent initialization"""
        agent = AtlasDataValidationAgent()
        assert agent.role == AgentRole.DATA_VALIDATOR
        assert agent.agent_id is not None
        
        # Test initialization
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_quote_validation(self):
        """Test quote data validation"""
        agent = AtlasDataValidationAgent()
        await agent.initialize()
        
        task = MultiAgentTask(
            task_id="test_quote_validation",
            description="Test quote validation",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.DATA_VALIDATOR],
            input_data={
                "task_type": "validate_quote",
                "symbol": "AAPL"
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "confidence_score" in result
        assert result["confidence_score"] > 0

class TestPatternDetectionAgent:
    """Test suite for Pattern Detection Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test pattern detection agent initialization"""
        agent = AtlasPatternDetectionAgent()
        assert agent.role == AgentRole.PATTERN_DETECTOR
        
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_pattern_detection(self):
        """Test Lee Method pattern detection"""
        agent = AtlasPatternDetectionAgent()
        await agent.initialize()
        
        task = MultiAgentTask(
            task_id="test_pattern_detection",
            description="Test pattern detection",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.PATTERN_DETECTOR],
            input_data={
                "task_type": "detect_lee_patterns",
                "symbol": "AAPL",
                "timeframe": "1Day"
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "patterns_detected" in result
        assert "confidence_score" in result

class TestAnalysisAgent:
    """Test suite for Analysis Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test analysis agent initialization"""
        agent = AtlasAnalysisAgent()
        assert agent.role == AgentRole.ANALYSIS_ENGINE
        
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis(self):
        """Test sentiment analysis functionality"""
        agent = AtlasAnalysisAgent()
        await agent.initialize()
        
        task = MultiAgentTask(
            task_id="test_sentiment_analysis",
            description="Test sentiment analysis",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.ANALYSIS_ENGINE],
            input_data={
                "task_type": "sentiment_analysis",
                "text_data": "Apple stock shows strong bullish momentum with positive earnings outlook"
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "confidence_score" in result
        assert "overall_sentiment" in result

class TestRiskManagementAgent:
    """Test suite for Risk Management Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test risk management agent initialization"""
        agent = AtlasRiskManagementAgent()
        assert agent.role == AgentRole.RISK_MANAGER
        
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_var_calculation(self):
        """Test VaR calculation"""
        agent = AtlasRiskManagementAgent()
        await agent.initialize()
        
        task = MultiAgentTask(
            task_id="test_var_calculation",
            description="Test VaR calculation",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.RISK_MANAGER],
            input_data={
                "task_type": "calculate_var",
                "portfolio_data": {
                    "total_value": 100000,
                    "volatility": 0.02
                },
                "confidence_level": 0.95,
                "method": "parametric"
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "var_amount" in result
        assert result["var_amount"] > 0

class TestTradeExecutionAgent:
    """Test suite for Trade Execution Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test trade execution agent initialization"""
        agent = AtlasTradeExecutionAgent()
        assert agent.role == AgentRole.TRADE_EXECUTOR
        
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_recommendation_generation(self):
        """Test trading recommendation generation"""
        agent = AtlasTradeExecutionAgent()
        await agent.initialize()
        
        task = MultiAgentTask(
            task_id="test_recommendation",
            description="Test recommendation generation",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.TRADE_EXECUTOR],
            input_data={
                "task_type": "generate_recommendation",
                "symbol": "AAPL",
                "analysis_data": {
                    "pattern_analysis": {"confidence_score": 0.85, "patterns_detected": 1},
                    "sentiment_analysis": {"overall_sentiment": 0.3, "confidence_score": 0.8},
                    "risk_analysis": {"risk_percentage": 15, "position_size": 100},
                    "current_price": 150.25
                }
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "recommendation" in result
        assert "confidence_score" in result

class TestValidationAgent:
    """Test suite for Validation Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test validation agent initialization"""
        agent = AtlasValidationAgent()
        assert agent.role == AgentRole.VALIDATION_SUPERVISOR
        
        success = await agent.initialize()
        assert success is True
        assert len(agent.tools) > 0
    
    @pytest.mark.asyncio
    async def test_comprehensive_validation(self):
        """Test comprehensive validation"""
        agent = AtlasValidationAgent()
        await agent.initialize()
        
        # Mock agent outputs for validation
        agent_outputs = {
            "data_validator": {"confidence_score": 0.9, "passed_validation": True},
            "pattern_detector": {"confidence_score": 0.85, "patterns_detected": 1},
            "analysis_engine": {"confidence_score": 0.8, "overall_sentiment": 0.3},
            "risk_manager": {"overall_assessment": "moderate_risk", "var_amount": 1500},
            "trade_executor": {"confidence_score": 0.88, "execution_ready": True}
        }
        
        task = MultiAgentTask(
            task_id="test_validation",
            description="Test comprehensive validation",
            priority=TaskPriority.HIGH,
            required_agents=[AgentRole.VALIDATION_SUPERVISOR],
            input_data={
                "task_type": "comprehensive_validation",
                "agent_outputs": agent_outputs,
                "symbol": "AAPL"
            },
            expected_output={}
        )
        
        result = await agent.execute_task(task)
        assert result is not None
        assert "overall_score" in result
        assert "final_recommendation" in result

# ============================================================================
# INTEGRATION TESTS
# ============================================================================

class TestMultiAgentOrchestrator:
    """Test suite for Multi-Agent Orchestrator"""
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization"""
        assert orchestrator is not None
        assert len(orchestrator.agents) == 6  # All 6 agents should be registered
        
        status = orchestrator.get_orchestrator_status()
        assert status["status"] == "active"
        assert status["total_agents"] == 6
    
    @pytest.mark.asyncio
    async def test_sequential_orchestration(self, orchestrator, sample_orchestration_request):
        """Test sequential orchestration mode"""
        request = sample_orchestration_request
        request.orchestration_mode = OrchestrationMode.SEQUENTIAL
        request.timeout_seconds = 180
        
        result = await orchestrator.process_request(request)
        
        assert result is not None
        assert result.request_id == request.request_id
        assert result.symbol == request.symbol
        assert result.confidence_score >= 0.0
        assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_parallel_orchestration(self, orchestrator, sample_orchestration_request):
        """Test parallel orchestration mode"""
        request = sample_orchestration_request
        request.orchestration_mode = OrchestrationMode.PARALLEL
        request.timeout_seconds = 120
        
        result = await orchestrator.process_request(request)
        
        assert result is not None
        assert result.confidence_score >= 0.0
        assert result.processing_time > 0
        # Parallel should be faster than sequential
        assert result.processing_time < 60  # Should complete within 60 seconds
    
    @pytest.mark.asyncio
    async def test_hybrid_orchestration(self, orchestrator, sample_orchestration_request):
        """Test hybrid orchestration mode"""
        request = sample_orchestration_request
        request.orchestration_mode = OrchestrationMode.HYBRID
        
        result = await orchestrator.process_request(request)
        
        assert result is not None
        assert result.confidence_score >= 0.0
        assert len(result.agent_results) > 0
        assert result.final_recommendation is not None
    
    @pytest.mark.asyncio
    async def test_validation_integration(self, orchestrator, sample_orchestration_request):
        """Test validation integration"""
        request = sample_orchestration_request
        request.require_validation = True
        
        result = await orchestrator.process_request(request)
        
        assert result is not None
        assert result.validation_result is not None
        assert "overall_score" in result.validation_result

# ============================================================================
# PERFORMANCE TESTS
# ============================================================================

class TestPerformance:
    """Performance test suite"""
    
    @pytest.mark.asyncio
    async def test_response_time_under_10_seconds(self, orchestrator, sample_orchestration_request):
        """Test that response time is under 10 seconds"""
        request = sample_orchestration_request
        request.timeout_seconds = 10
        
        start_time = time.time()
        result = await orchestrator.process_request(request)
        end_time = time.time()
        
        processing_time = end_time - start_time
        assert processing_time < 10.0, f"Processing took {processing_time:.2f} seconds, should be under 10"
        assert result.processing_time < 10.0
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, orchestrator):
        """Test handling multiple concurrent requests"""
        requests = []
        for i in range(5):
            request = OrchestrationRequest(
                request_id=f"concurrent_test_{i}",
                intent=IntentType.PATTERN_DETECTION,
                symbol=f"TEST{i}",
                input_data={"test_data": f"test_{i}"},
                orchestration_mode=OrchestrationMode.PARALLEL,
                timeout_seconds=30
            )
            requests.append(request)
        
        # Execute all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*[
            orchestrator.process_request(req) for req in requests
        ])
        end_time = time.time()
        
        total_time = end_time - start_time
        assert len(results) == 5
        assert all(result is not None for result in results)
        assert total_time < 60  # Should complete all within 60 seconds

# ============================================================================
# ERROR HANDLING TESTS
# ============================================================================

class TestErrorHandling:
    """Error handling test suite"""
    
    @pytest.mark.asyncio
    async def test_invalid_symbol_handling(self, orchestrator):
        """Test handling of invalid symbols"""
        request = OrchestrationRequest(
            request_id="invalid_symbol_test",
            intent=IntentType.DATA_ANALYSIS,
            symbol="INVALID_SYMBOL_123",
            input_data={},
            timeout_seconds=30
        )
        
        result = await orchestrator.process_request(request)
        
        # Should handle gracefully, not crash
        assert result is not None
        assert result.request_id == request.request_id
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, orchestrator):
        """Test timeout handling"""
        request = OrchestrationRequest(
            request_id="timeout_test",
            intent=IntentType.COMPREHENSIVE_ANALYSIS,
            symbol="AAPL",
            input_data={},
            timeout_seconds=1  # Very short timeout
        )
        
        result = await orchestrator.process_request(request)
        
        # Should handle timeout gracefully
        assert result is not None
        assert result.processing_time <= 2  # Should respect timeout

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--asyncio-mode=auto"])
