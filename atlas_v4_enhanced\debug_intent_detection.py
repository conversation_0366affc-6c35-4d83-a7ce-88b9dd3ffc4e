#!/usr/bin/env python3
"""
Debug Intent Detection System
Test the intent detection to see why stock analysis queries are not being routed correctly.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from atlas_ai_core import AtlasAIEngine

async def test_intent_detection():
    """Test intent detection with various queries"""

    print("🔍 TESTING A.T.L.A.S. INTENT DETECTION SYSTEM")
    print("=" * 60)

    # Initialize AI engine
    ai_engine = AtlasAIEngine()
    await ai_engine.initialize()

    # Test queries
    test_queries = [
        "Should I buy AAPL now?",
        "What are some of the insider trades that are close to their current price?",
        "Analyze AAPL stock",
        "How do I trade Bitcoin?",
        "Scan for TTM Squeeze patterns",
        "Hello A.T.L.A.S.",
        "What is a stock?",
        "Find me profitable trades"
    ]

    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        print("-" * 40)

        try:
            # Test the actual process_message method to see what response we get
            response = await ai_engine.process_message(
                message=query,
                session_id="test_session",
                user_id="test_user"
            )

            print(f"🎯 Response Type: {response.type}")
            print(f"🎯 Confidence: {response.confidence}")
            print(f"📝 Response Preview: {response.response[:200]}...")

        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()

    print("\n" + "=" * 60)
    print("✅ Intent detection test completed!")

if __name__ == "__main__":
    asyncio.run(test_intent_detection())
