"""
A.T.L.A.S. Trading Accuracy Monitor
Automated monitoring for 35%+ performance standards, AI analysis accuracy, and pattern detection validation
"""

import asyncio
import logging
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class PerformanceStatus(Enum):
    """Trading performance status levels"""
    EXCELLENT = "EXCELLENT"  # >50% returns
    GOOD = "GOOD"           # 35-50% returns
    ACCEPTABLE = "ACCEPTABLE"  # 25-35% returns
    BELOW_TARGET = "BELOW_TARGET"  # 15-25% returns
    CRITICAL = "CRITICAL"   # <15% returns

class AccuracyStatus(Enum):
    """Analysis accuracy status levels"""
    HIGH = "HIGH"           # >90% accuracy
    GOOD = "GOOD"          # 80-90% accuracy
    ACCEPTABLE = "ACCEPTABLE"  # 70-80% accuracy
    LOW = "LOW"            # 60-70% accuracy
    CRITICAL = "CRITICAL"  # <60% accuracy

@dataclass
class TradingSignal:
    """Individual trading signal record"""
    signal_id: str
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    timestamp: datetime
    source: str  # AI, Lee Method, 6-Point Analysis, etc.
    
@dataclass
class SignalOutcome:
    """Trading signal outcome tracking"""
    signal_id: str
    outcome: str  # WIN, LOSS, PENDING
    actual_return: Optional[float]
    max_gain: Optional[float]
    max_loss: Optional[float]
    duration_hours: Optional[float]
    exit_timestamp: Optional[datetime]

@dataclass
class PerformanceMetrics:
    """Performance tracking metrics"""
    total_signals: int
    winning_signals: int
    losing_signals: int
    pending_signals: int
    win_rate: float
    average_return: float
    total_return: float
    sharpe_ratio: Optional[float]
    max_drawdown: float
    last_updated: datetime

class AtlasTradingAccuracyMonitor:
    """Comprehensive trading accuracy and performance monitoring system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Performance tracking
        self.signals: Dict[str, TradingSignal] = {}
        self.outcomes: Dict[str, SignalOutcome] = {}
        self.performance_history: deque = deque(maxlen=1000)
        
        # Performance thresholds
        self.performance_thresholds = {
            'target_return': 0.35,  # 35% minimum
            'min_win_rate': 0.60,   # 60% minimum win rate
            'max_drawdown': 0.15,   # 15% maximum drawdown
            'min_sharpe_ratio': 1.0, # 1.0 minimum Sharpe ratio
            'signal_confidence_threshold': 0.70  # 70% minimum confidence
        }
        
        # AI Analysis accuracy tracking
        self.ai_analysis_accuracy: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.pattern_detection_accuracy: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Lee Method specific tracking
        self.lee_method_signals: deque = deque(maxlen=500)
        self.lee_method_accuracy = 0.0
        
        # 6-Point Analysis tracking
        self.six_point_analysis_accuracy: deque = deque(maxlen=200)
        
        # Alert system
        self.accuracy_alerts: List[Dict[str, Any]] = []
        self.performance_alerts: List[Dict[str, Any]] = []
        
        # Monitoring intervals
        self.last_performance_check = datetime.now()
        self.performance_check_interval = 3600  # 1 hour
        
        self.logger.info("[TRADING_ACCURACY] Trading Accuracy Monitor initialized")
    
    async def record_trading_signal(self, signal: TradingSignal) -> str:
        """Record a new trading signal for tracking"""
        try:
            signal_id = f"{signal.symbol}_{signal.signal_type}_{int(signal.timestamp.timestamp())}"
            signal.signal_id = signal_id
            
            self.signals[signal_id] = signal
            
            # Initialize outcome tracking
            self.outcomes[signal_id] = SignalOutcome(
                signal_id=signal_id,
                outcome="PENDING",
                actual_return=None,
                max_gain=None,
                max_loss=None,
                duration_hours=None,
                exit_timestamp=None
            )
            
            # Validate signal quality
            await self._validate_signal_quality(signal)
            
            self.logger.info(f"[SIGNAL_RECORDED] {signal.symbol} {signal.signal_type} "
                           f"confidence: {signal.confidence:.1%} source: {signal.source}")
            
            return signal_id
            
        except Exception as e:
            self.logger.error(f"Error recording trading signal: {e}")
            return ""
    
    async def update_signal_outcome(self, signal_id: str, current_price: float, 
                                  force_exit: bool = False) -> bool:
        """Update trading signal outcome based on current market conditions"""
        try:
            if signal_id not in self.signals or signal_id not in self.outcomes:
                return False
            
            signal = self.signals[signal_id]
            outcome = self.outcomes[signal_id]
            
            if outcome.outcome != "PENDING":
                return True  # Already resolved
            
            # Calculate current return
            if signal.signal_type == "BUY":
                current_return = (current_price - signal.entry_price) / signal.entry_price
            elif signal.signal_type == "SELL":
                current_return = (signal.entry_price - current_price) / signal.entry_price
            else:
                return False  # HOLD signals not tracked for returns
            
            # Update max gain/loss
            if outcome.max_gain is None or current_return > outcome.max_gain:
                outcome.max_gain = current_return
            if outcome.max_loss is None or current_return < outcome.max_loss:
                outcome.max_loss = current_return
            
            # Check exit conditions
            should_exit = force_exit
            
            # Stop loss hit
            if signal.stop_loss and ((signal.signal_type == "BUY" and current_price <= signal.stop_loss) or
                                   (signal.signal_type == "SELL" and current_price >= signal.stop_loss)):
                should_exit = True
                outcome.outcome = "LOSS"
            
            # Target hit
            elif signal.target_price and ((signal.signal_type == "BUY" and current_price >= signal.target_price) or
                                        (signal.signal_type == "SELL" and current_price <= signal.target_price)):
                should_exit = True
                outcome.outcome = "WIN"
            
            # Time-based exit (24 hours for short-term signals)
            elif (datetime.now() - signal.timestamp).total_seconds() > 86400:
                should_exit = True
                outcome.outcome = "WIN" if current_return > 0 else "LOSS"
            
            if should_exit:
                outcome.actual_return = current_return
                outcome.duration_hours = (datetime.now() - signal.timestamp).total_seconds() / 3600
                outcome.exit_timestamp = datetime.now()
                
                # Record for accuracy tracking
                await self._record_signal_accuracy(signal, outcome)
                
                self.logger.info(f"[SIGNAL_CLOSED] {signal.symbol} {signal.signal_type} "
                               f"return: {current_return:.1%} outcome: {outcome.outcome}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating signal outcome: {e}")
            return False
    
    async def _validate_signal_quality(self, signal: TradingSignal):
        """Validate signal quality and generate alerts if needed"""
        try:
            issues = []
            
            # Check confidence threshold
            if signal.confidence < self.performance_thresholds['signal_confidence_threshold']:
                issues.append(f"Low confidence signal: {signal.confidence:.1%}")
            
            # Check for reasonable price targets
            if signal.target_price and signal.entry_price:
                if signal.signal_type == "BUY":
                    expected_return = (signal.target_price - signal.entry_price) / signal.entry_price
                elif signal.signal_type == "SELL":
                    expected_return = (signal.entry_price - signal.target_price) / signal.entry_price
                else:
                    expected_return = 0
                
                if expected_return > 0.50:  # >50% expected return
                    issues.append(f"Unrealistic target: {expected_return:.1%} expected return")
                elif expected_return < 0.02:  # <2% expected return
                    issues.append(f"Low target: {expected_return:.1%} expected return")
            
            # Check stop loss reasonableness
            if signal.stop_loss and signal.entry_price:
                if signal.signal_type == "BUY":
                    risk = (signal.entry_price - signal.stop_loss) / signal.entry_price
                elif signal.signal_type == "SELL":
                    risk = (signal.stop_loss - signal.entry_price) / signal.entry_price
                else:
                    risk = 0
                
                if risk > 0.10:  # >10% risk
                    issues.append(f"High risk signal: {risk:.1%} potential loss")
            
            if issues:
                await self._generate_signal_quality_alert(signal, issues)
                
        except Exception as e:
            self.logger.error(f"Signal quality validation error: {e}")
    
    async def _record_signal_accuracy(self, signal: TradingSignal, outcome: SignalOutcome):
        """Record signal accuracy for different analysis methods"""
        try:
            is_accurate = outcome.outcome == "WIN"
            
            # Record by source
            self.ai_analysis_accuracy[signal.source].append(is_accurate)
            
            # Lee Method specific tracking
            if "lee" in signal.source.lower():
                self.lee_method_signals.append({
                    'signal': signal,
                    'outcome': outcome,
                    'accurate': is_accurate,
                    'timestamp': datetime.now()
                })
                
                # Update Lee Method accuracy
                recent_lee_signals = list(self.lee_method_signals)[-50:]  # Last 50 signals
                if recent_lee_signals:
                    self.lee_method_accuracy = sum(s['accurate'] for s in recent_lee_signals) / len(recent_lee_signals)
            
            # 6-Point Analysis tracking
            if "6-point" in signal.source.lower() or "six-point" in signal.source.lower():
                self.six_point_analysis_accuracy.append(is_accurate)
            
        except Exception as e:
            self.logger.error(f"Error recording signal accuracy: {e}")
    
    async def calculate_performance_metrics(self) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        try:
            completed_outcomes = [o for o in self.outcomes.values() if o.outcome != "PENDING"]
            
            if not completed_outcomes:
                return PerformanceMetrics(
                    total_signals=0, winning_signals=0, losing_signals=0, pending_signals=0,
                    win_rate=0.0, average_return=0.0, total_return=0.0, sharpe_ratio=None,
                    max_drawdown=0.0, last_updated=datetime.now()
                )
            
            # Basic metrics
            total_signals = len(completed_outcomes)
            winning_signals = sum(1 for o in completed_outcomes if o.outcome == "WIN")
            losing_signals = sum(1 for o in completed_outcomes if o.outcome == "LOSS")
            pending_signals = len([o for o in self.outcomes.values() if o.outcome == "PENDING"])
            
            win_rate = winning_signals / total_signals if total_signals > 0 else 0.0
            
            # Return calculations
            returns = [o.actual_return for o in completed_outcomes if o.actual_return is not None]
            average_return = statistics.mean(returns) if returns else 0.0
            total_return = sum(returns) if returns else 0.0
            
            # Sharpe ratio (simplified)
            sharpe_ratio = None
            if returns and len(returns) > 1:
                return_std = statistics.stdev(returns)
                if return_std > 0:
                    sharpe_ratio = average_return / return_std
            
            # Max drawdown calculation
            max_drawdown = 0.0
            if returns:
                cumulative_returns = np.cumsum(returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / (running_max + 1)  # Avoid division by zero
                max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0.0
            
            metrics = PerformanceMetrics(
                total_signals=total_signals,
                winning_signals=winning_signals,
                losing_signals=losing_signals,
                pending_signals=pending_signals,
                win_rate=win_rate,
                average_return=average_return,
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                last_updated=datetime.now()
            )
            
            # Store in history
            self.performance_history.append(metrics)
            
            # Check performance against thresholds
            await self._check_performance_thresholds(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {e}")
            return PerformanceMetrics(
                total_signals=0, winning_signals=0, losing_signals=0, pending_signals=0,
                win_rate=0.0, average_return=0.0, total_return=0.0, sharpe_ratio=None,
                max_drawdown=0.0, last_updated=datetime.now()
            )
    
    async def _check_performance_thresholds(self, metrics: PerformanceMetrics):
        """Check performance against thresholds and generate alerts"""
        try:
            alerts = []
            
            # Check return performance
            if metrics.total_signals >= 10:  # Need minimum signals for meaningful analysis
                if metrics.average_return < self.performance_thresholds['target_return']:
                    alerts.append({
                        'type': 'PERFORMANCE_BELOW_TARGET',
                        'severity': 'WARNING',
                        'message': f"Average return {metrics.average_return:.1%} below target {self.performance_thresholds['target_return']:.1%}",
                        'current_value': metrics.average_return,
                        'threshold': self.performance_thresholds['target_return']
                    })
            
            # Check win rate
            if metrics.win_rate < self.performance_thresholds['min_win_rate']:
                alerts.append({
                    'type': 'WIN_RATE_LOW',
                    'severity': 'WARNING',
                    'message': f"Win rate {metrics.win_rate:.1%} below minimum {self.performance_thresholds['min_win_rate']:.1%}",
                    'current_value': metrics.win_rate,
                    'threshold': self.performance_thresholds['min_win_rate']
                })
            
            # Check drawdown
            if metrics.max_drawdown > self.performance_thresholds['max_drawdown']:
                alerts.append({
                    'type': 'HIGH_DRAWDOWN',
                    'severity': 'CRITICAL',
                    'message': f"Max drawdown {metrics.max_drawdown:.1%} exceeds limit {self.performance_thresholds['max_drawdown']:.1%}",
                    'current_value': metrics.max_drawdown,
                    'threshold': self.performance_thresholds['max_drawdown']
                })
            
            # Store alerts
            for alert in alerts:
                alert['timestamp'] = datetime.now().isoformat()
                self.performance_alerts.append(alert)
                self.logger.warning(f"[PERFORMANCE_ALERT] {alert['message']}")
            
        except Exception as e:
            self.logger.error(f"Error checking performance thresholds: {e}")
    
    async def _generate_signal_quality_alert(self, signal: TradingSignal, issues: List[str]):
        """Generate signal quality alert"""
        alert = {
            'type': 'SIGNAL_QUALITY_ISSUE',
            'severity': 'WARNING',
            'symbol': signal.symbol,
            'signal_type': signal.signal_type,
            'source': signal.source,
            'confidence': signal.confidence,
            'issues': issues,
            'timestamp': datetime.now().isoformat(),
            'message': f"Signal quality issues for {signal.symbol}: {', '.join(issues)}"
        }
        
        self.accuracy_alerts.append(alert)
        self.logger.warning(f"[SIGNAL_QUALITY_ALERT] {alert['message']}")
    
    def get_accuracy_report(self) -> Dict[str, Any]:
        """Generate comprehensive accuracy report"""
        try:
            current_time = datetime.now()
            
            # AI Analysis accuracy by source
            ai_accuracy = {}
            for source, accuracy_data in self.ai_analysis_accuracy.items():
                if accuracy_data:
                    recent_data = list(accuracy_data)[-20:]  # Last 20 analyses
                    ai_accuracy[source] = {
                        'accuracy': sum(recent_data) / len(recent_data),
                        'total_analyses': len(accuracy_data),
                        'recent_analyses': len(recent_data)
                    }
            
            # Pattern detection accuracy
            pattern_accuracy = {}
            for pattern_type, accuracy_data in self.pattern_detection_accuracy.items():
                if accuracy_data:
                    recent_data = list(accuracy_data)[-20:]
                    pattern_accuracy[pattern_type] = {
                        'accuracy': sum(recent_data) / len(recent_data),
                        'total_detections': len(accuracy_data),
                        'recent_detections': len(recent_data)
                    }
            
            # Lee Method specific accuracy
            lee_method_stats = {
                'accuracy': self.lee_method_accuracy,
                'total_signals': len(self.lee_method_signals),
                'recent_signals': len([s for s in self.lee_method_signals 
                                     if (current_time - s['timestamp']).total_seconds() < 86400])
            }
            
            # 6-Point Analysis accuracy
            six_point_accuracy = 0.0
            if self.six_point_analysis_accuracy:
                recent_six_point = list(self.six_point_analysis_accuracy)[-20:]
                six_point_accuracy = sum(recent_six_point) / len(recent_six_point)
            
            # Recent alerts
            recent_alerts = [
                alert for alert in (self.accuracy_alerts + self.performance_alerts)
                if (current_time - datetime.fromisoformat(alert['timestamp'])).total_seconds() < 3600
            ]
            
            return {
                'timestamp': current_time.isoformat(),
                'ai_analysis_accuracy': ai_accuracy,
                'pattern_detection_accuracy': pattern_accuracy,
                'lee_method_accuracy': lee_method_stats,
                'six_point_analysis_accuracy': six_point_accuracy,
                'recent_alerts': recent_alerts,
                'alert_count': len(recent_alerts),
                'monitoring_active': True
            }
            
        except Exception as e:
            self.logger.error(f"Error generating accuracy report: {e}")
            return {'error': str(e)}

# Global instance
trading_accuracy_monitor = AtlasTradingAccuracyMonitor()
