"""
A.T.L.A.S. Performance Monitor
Comprehensive performance monitoring and status tracking for ultra-responsive TTM Squeeze detection
Maintains 35%+ returns standard with real-time metrics and functional progress indicators
"""

import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    """Performance metric types"""
    SCAN_LATENCY = "scan_latency"
    PATTERN_DETECTION_ACCURACY = "pattern_detection_accuracy"
    ALERT_DELIVERY_TIME = "alert_delivery_time"
    API_RESPONSE_TIME = "api_response_time"
    CACHE_HIT_RATIO = "cache_hit_ratio"
    SYMBOLS_PER_SECOND = "symbols_per_second"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"

@dataclass
class PerformanceData:
    """Performance data point"""
    metric_type: PerformanceMetric
    value: float
    timestamp: datetime
    symbol: Optional[str] = None
    additional_data: Dict[str, Any] = None

@dataclass
class ScanningProgress:
    """Real-time scanning progress indicator"""
    total_symbols: int
    scanned_symbols: int
    patterns_detected: int
    alerts_generated: int
    current_symbol: str
    scan_start_time: datetime
    estimated_completion: datetime
    progress_percentage: float
    scanning_rate: float  # symbols per second

class AtlasPerformanceMonitor:
    """
    Comprehensive performance monitoring system for A.T.L.A.S. ultra-responsive scanner
    Tracks all critical metrics to maintain 35%+ returns performance standard
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Performance data storage
        self.performance_data: Dict[PerformanceMetric, List[PerformanceData]] = {
            metric: [] for metric in PerformanceMetric
        }
        
        # Real-time metrics
        self.current_metrics: Dict[str, float] = {}
        self.performance_thresholds: Dict[PerformanceMetric, float] = {
            PerformanceMetric.SCAN_LATENCY: 2.0,  # seconds - max acceptable scan time
            PerformanceMetric.PATTERN_DETECTION_ACCURACY: 0.85,  # 85% minimum accuracy
            PerformanceMetric.ALERT_DELIVERY_TIME: 1.0,  # seconds - max alert delivery time
            PerformanceMetric.API_RESPONSE_TIME: 0.5,  # seconds - max API response time
            PerformanceMetric.CACHE_HIT_RATIO: 0.7,  # 70% minimum cache hit ratio
            PerformanceMetric.SYMBOLS_PER_SECOND: 5.0,  # minimum scanning rate
        }
        
        # Progress tracking
        self.current_progress: Optional[ScanningProgress] = None
        self.progress_history: List[ScanningProgress] = []
        
        # Performance alerts
        self.performance_alerts: List[Dict[str, Any]] = []
        self.alert_callbacks: List = []
        
        # Threading for background monitoring
        self.monitoring_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        self.metrics_queue = queue.Queue()
        
        # Returns tracking for 35%+ standard
        self.returns_tracking = {
            'total_signals': 0,
            'successful_signals': 0,
            'total_return': 0.0,
            'average_return': 0.0,
            'success_rate': 0.0,
            'last_updated': datetime.now()
        }
        
        # Enhanced auto-scaling for 250+ symbols
        self.auto_scaling_enabled = True
        self.scale_up_threshold = 0.8  # Scale up when 80% utilization
        self.scale_down_threshold = 0.3  # Scale down when 30% utilization
        self.scaling_cooldown = 300  # 5 minutes between scaling actions
        self.last_scaling_action = 0

        # API capacity tracking for intelligent scaling
        self.api_capacity_tracker = {
            'total_requests_per_minute': 3000,  # FMP paid tier
            'current_usage': 0,
            'available_capacity': 3000,
            'provider_limits': {
                'fmp': 3000,
                'alpaca': 1000,
                'polygon': 1000,
                'alpha_vantage': 75
            }
        }

        # Component references for auto-scaling
        self.scanner_ref = None
        self.worker_pool_ref = None
        self.rate_limiter_ref = None

        self.logger.info("[PERFORMANCE] Enhanced Performance monitor initialized for 250+ symbol scanning")

    def start_monitoring(self):
        """Start background performance monitoring"""
        try:
            if self.is_monitoring:
                return
            
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.logger.info("[PERFORMANCE] Background monitoring started")
            
        except Exception as e:
            self.logger.error(f"Error starting performance monitoring: {e}")

    def stop_monitoring(self):
        """Stop background performance monitoring"""
        try:
            self.is_monitoring = False
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5)
            
            self.logger.info("[PERFORMANCE] Background monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping performance monitoring: {e}")

    def record_metric(self, metric_type: PerformanceMetric, value: float, 
                     symbol: str = None, additional_data: Dict[str, Any] = None):
        """Record a performance metric"""
        try:
            data_point = PerformanceData(
                metric_type=metric_type,
                value=value,
                timestamp=datetime.now(),
                symbol=symbol,
                additional_data=additional_data or {}
            )
            
            # Store in performance data
            self.performance_data[metric_type].append(data_point)
            
            # Keep only last 1000 data points per metric
            if len(self.performance_data[metric_type]) > 1000:
                self.performance_data[metric_type] = self.performance_data[metric_type][-1000:]
            
            # Update current metrics
            self.current_metrics[metric_type.value] = value
            
            # Check performance thresholds
            self._check_performance_threshold(metric_type, value)
            
            # Queue for background processing
            self.metrics_queue.put(data_point)
            
        except Exception as e:
            self.logger.error(f"Error recording metric {metric_type}: {e}")

    def start_scan_progress(self, total_symbols: int):
        """Start tracking scanning progress"""
        try:
            self.current_progress = ScanningProgress(
                total_symbols=total_symbols,
                scanned_symbols=0,
                patterns_detected=0,
                alerts_generated=0,
                current_symbol="",
                scan_start_time=datetime.now(),
                estimated_completion=datetime.now() + timedelta(seconds=total_symbols * 0.5),
                progress_percentage=0.0,
                scanning_rate=0.0
            )
            
            self.logger.info(f"[PROGRESS] Started scan progress tracking for {total_symbols} symbols")
            
        except Exception as e:
            self.logger.error(f"Error starting scan progress: {e}")

    def update_scan_progress(self, scanned_symbols: int, current_symbol: str, 
                           patterns_detected: int = None, alerts_generated: int = None):
        """Update scanning progress"""
        try:
            if not self.current_progress:
                return
            
            self.current_progress.scanned_symbols = scanned_symbols
            self.current_progress.current_symbol = current_symbol
            
            if patterns_detected is not None:
                self.current_progress.patterns_detected = patterns_detected
            
            if alerts_generated is not None:
                self.current_progress.alerts_generated = alerts_generated
            
            # Calculate progress percentage
            self.current_progress.progress_percentage = (
                scanned_symbols / self.current_progress.total_symbols * 100
            )
            
            # Calculate scanning rate
            elapsed_time = (datetime.now() - self.current_progress.scan_start_time).total_seconds()
            if elapsed_time > 0:
                self.current_progress.scanning_rate = scanned_symbols / elapsed_time
                
                # Update estimated completion
                remaining_symbols = self.current_progress.total_symbols - scanned_symbols
                if self.current_progress.scanning_rate > 0:
                    remaining_time = remaining_symbols / self.current_progress.scanning_rate
                    self.current_progress.estimated_completion = datetime.now() + timedelta(seconds=remaining_time)
            
        except Exception as e:
            self.logger.error(f"Error updating scan progress: {e}")

    def complete_scan_progress(self):
        """Complete scanning progress tracking"""
        try:
            if not self.current_progress:
                return
            
            # Finalize progress
            self.current_progress.progress_percentage = 100.0
            self.current_progress.estimated_completion = datetime.now()
            
            # Store in history
            self.progress_history.append(self.current_progress)
            
            # Keep only last 100 progress records
            if len(self.progress_history) > 100:
                self.progress_history = self.progress_history[-100:]
            
            # Record performance metrics
            scan_duration = (datetime.now() - self.current_progress.scan_start_time).total_seconds()
            self.record_metric(PerformanceMetric.SCAN_LATENCY, scan_duration)
            self.record_metric(PerformanceMetric.SYMBOLS_PER_SECOND, self.current_progress.scanning_rate)
            
            self.logger.info(f"[PROGRESS] Completed scan: {self.current_progress.scanned_symbols} symbols, "
                           f"{self.current_progress.patterns_detected} patterns, "
                           f"{self.current_progress.scanning_rate:.1f} symbols/sec")
            
            self.current_progress = None
            
        except Exception as e:
            self.logger.error(f"Error completing scan progress: {e}")

    def record_signal_performance(self, symbol: str, signal_confidence: float, 
                                actual_return: float = None, success: bool = None):
        """Record signal performance for returns tracking"""
        try:
            self.returns_tracking['total_signals'] += 1
            
            if success is not None and actual_return is not None:
                if success:
                    self.returns_tracking['successful_signals'] += 1
                    self.returns_tracking['total_return'] += actual_return
                
                # Calculate averages
                if self.returns_tracking['total_signals'] > 0:
                    self.returns_tracking['success_rate'] = (
                        self.returns_tracking['successful_signals'] / self.returns_tracking['total_signals']
                    )
                
                if self.returns_tracking['successful_signals'] > 0:
                    self.returns_tracking['average_return'] = (
                        self.returns_tracking['total_return'] / self.returns_tracking['successful_signals']
                    )
            
            self.returns_tracking['last_updated'] = datetime.now()
            
            # Check if we're maintaining 35%+ returns
            if self.returns_tracking['average_return'] < 0.35 and self.returns_tracking['total_signals'] > 10:
                self._generate_performance_alert(
                    "RETURNS_BELOW_THRESHOLD",
                    f"Average returns ({self.returns_tracking['average_return']:.1%}) below 35% threshold"
                )
            
        except Exception as e:
            self.logger.error(f"Error recording signal performance: {e}")

    def get_performance_status(self) -> Dict[str, Any]:
        """Get comprehensive performance status"""
        try:
            current_time = datetime.now()
            
            # Calculate recent performance metrics
            recent_metrics = {}
            for metric_type in PerformanceMetric:
                recent_data = [
                    dp for dp in self.performance_data[metric_type]
                    if (current_time - dp.timestamp).total_seconds() < 300  # Last 5 minutes
                ]
                
                if recent_data:
                    recent_metrics[metric_type.value] = {
                        'current': recent_data[-1].value,
                        'average': sum(dp.value for dp in recent_data) / len(recent_data),
                        'min': min(dp.value for dp in recent_data),
                        'max': max(dp.value for dp in recent_data),
                        'count': len(recent_data)
                    }
            
            return {
                'monitoring_active': self.is_monitoring,
                'current_metrics': self.current_metrics,
                'recent_performance': recent_metrics,
                'performance_thresholds': {k.value: v for k, v in self.performance_thresholds.items()},
                'current_progress': asdict(self.current_progress) if self.current_progress else None,
                'returns_tracking': self.returns_tracking,
                'performance_alerts': self.performance_alerts[-10:],  # Last 10 alerts
                'system_health': self._assess_system_health(),
                'timestamp': current_time.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance status: {e}")
            return {'error': str(e)}

    def _check_performance_threshold(self, metric_type: PerformanceMetric, value: float):
        """Check if performance metric exceeds threshold"""
        try:
            threshold = self.performance_thresholds.get(metric_type)
            if threshold is None:
                return
            
            # Different threshold logic for different metrics
            if metric_type in [PerformanceMetric.SCAN_LATENCY, PerformanceMetric.ALERT_DELIVERY_TIME, 
                              PerformanceMetric.API_RESPONSE_TIME]:
                # Lower is better - alert if value exceeds threshold
                if value > threshold:
                    self._generate_performance_alert(
                        f"{metric_type.value.upper()}_THRESHOLD_EXCEEDED",
                        f"{metric_type.value} ({value:.2f}) exceeded threshold ({threshold:.2f})"
                    )
            
            elif metric_type in [PerformanceMetric.PATTERN_DETECTION_ACCURACY, PerformanceMetric.CACHE_HIT_RATIO,
                                PerformanceMetric.SYMBOLS_PER_SECOND]:
                # Higher is better - alert if value below threshold
                if value < threshold:
                    self._generate_performance_alert(
                        f"{metric_type.value.upper()}_BELOW_THRESHOLD",
                        f"{metric_type.value} ({value:.2f}) below threshold ({threshold:.2f})"
                    )
            
        except Exception as e:
            self.logger.error(f"Error checking performance threshold: {e}")

    def _generate_performance_alert(self, alert_type: str, message: str):
        """Generate performance alert"""
        try:
            alert = {
                'type': alert_type,
                'message': message,
                'timestamp': datetime.now().isoformat(),
                'severity': 'WARNING'
            }
            
            self.performance_alerts.append(alert)
            
            # Keep only last 100 alerts
            if len(self.performance_alerts) > 100:
                self.performance_alerts = self.performance_alerts[-100:]
            
            # Notify callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in performance alert callback: {e}")
            
            self.logger.warning(f"[PERFORMANCE ALERT] {alert_type}: {message}")
            
        except Exception as e:
            self.logger.error(f"Error generating performance alert: {e}")

    def _assess_system_health(self) -> str:
        """Assess overall system health"""
        try:
            health_score = 100
            
            # Check recent performance metrics
            current_time = datetime.now()
            
            for metric_type, threshold in self.performance_thresholds.items():
                recent_data = [
                    dp for dp in self.performance_data[metric_type]
                    if (current_time - dp.timestamp).total_seconds() < 300
                ]
                
                if recent_data:
                    avg_value = sum(dp.value for dp in recent_data) / len(recent_data)
                    
                    # Deduct points based on threshold violations
                    if metric_type in [PerformanceMetric.SCAN_LATENCY, PerformanceMetric.ALERT_DELIVERY_TIME]:
                        if avg_value > threshold:
                            health_score -= 15
                    elif metric_type in [PerformanceMetric.PATTERN_DETECTION_ACCURACY, PerformanceMetric.SYMBOLS_PER_SECOND]:
                        if avg_value < threshold:
                            health_score -= 15
            
            # Check returns performance
            if self.returns_tracking['average_return'] < 0.35 and self.returns_tracking['total_signals'] > 10:
                health_score -= 20
            
            # Determine health status
            if health_score >= 90:
                return "EXCELLENT"
            elif health_score >= 75:
                return "GOOD"
            elif health_score >= 60:
                return "FAIR"
            elif health_score >= 40:
                return "POOR"
            else:
                return "CRITICAL"
                
        except Exception as e:
            self.logger.error(f"Error assessing system health: {e}")
            return "UNKNOWN"

    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                # Process queued metrics
                while not self.metrics_queue.empty():
                    try:
                        data_point = self.metrics_queue.get_nowait()
                        # Additional processing could be done here
                    except queue.Empty:
                        break
                
                # Periodic cleanup
                self._cleanup_old_data()
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(5)

    def _cleanup_old_data(self):
        """Clean up old performance data"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=24)  # Keep 24 hours of data
            
            for metric_type in PerformanceMetric:
                self.performance_data[metric_type] = [
                    dp for dp in self.performance_data[metric_type]
                    if dp.timestamp > cutoff_time
                ]
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")

    def add_alert_callback(self, callback):
        """Add callback for performance alerts"""
        self.alert_callbacks.append(callback)

    def register_components(self, scanner=None, worker_pool=None, rate_limiter=None):
        """Register system components for monitoring and auto-scaling"""
        self.scanner_ref = scanner
        self.worker_pool_ref = worker_pool
        self.rate_limiter_ref = rate_limiter

        self.logger.info("System components registered for auto-scaling")

    def check_auto_scaling(self):
        """Check for auto-scaling opportunities"""
        try:
            if not self.auto_scaling_enabled:
                return

            current_time = time.time()

            # Check cooldown period
            if current_time - self.last_scaling_action < self.scaling_cooldown:
                return

            # Get current metrics
            current_metrics = self.get_current_metrics()

            # Calculate system utilization
            api_utilization = (self.api_capacity_tracker['current_usage'] /
                             self.api_capacity_tracker['total_requests_per_minute'])

            # Scale up conditions
            if api_utilization > self.scale_up_threshold:
                self._trigger_scale_up()
                self.last_scaling_action = current_time

            # Scale down conditions
            elif api_utilization < self.scale_down_threshold:
                self._trigger_scale_down()
                self.last_scaling_action = current_time

        except Exception as e:
            self.logger.error(f"Error checking auto-scaling: {e}")

    def _trigger_scale_up(self):
        """Trigger scale-up actions"""
        try:
            self.logger.info("🚀 Auto-scaling: Scaling up system resources")

            # Record scaling action
            self.record_metric(PerformanceMetric.SCALING_ACTION, {
                'action': 'scale_up',
                'timestamp': datetime.now(),
                'reason': 'high_utilization'
            })

        except Exception as e:
            self.logger.error(f"Error triggering scale-up: {e}")

    def _trigger_scale_down(self):
        """Trigger scale-down actions"""
        try:
            self.logger.info("📉 Auto-scaling: Scaling down system resources")

            # Record scaling action
            self.record_metric(PerformanceMetric.SCALING_ACTION, {
                'action': 'scale_down',
                'timestamp': datetime.now(),
                'reason': 'low_utilization'
            })

        except Exception as e:
            self.logger.error(f"Error triggering scale-down: {e}")

# Global performance monitor instance
performance_monitor = AtlasPerformanceMonitor()
