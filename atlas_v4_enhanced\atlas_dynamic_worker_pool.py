"""
A.T.L.A.S. Dynamic Worker Pool Manager
Intelligent worker scaling based on API capacity and workload
"""

import asyncio
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

logger = logging.getLogger(__name__)


class WorkerTier(Enum):
    """Worker tier priorities"""
    ULTRA_PRIORITY = 1
    HIGH_PRIORITY = 2
    MEDIUM_PRIORITY = 3
    LOW_PRIORITY = 4


@dataclass
class WorkerPoolConfig:
    """Configuration for dynamic worker pool"""
    min_workers: int = 2
    max_workers: int = 50
    target_queue_size: int = 10
    scale_up_threshold: float = 0.8  # Scale up when 80% busy
    scale_down_threshold: float = 0.3  # Scale down when 30% busy
    scale_check_interval: int = 30  # Check every 30 seconds
    worker_timeout: int = 60  # Worker timeout in seconds


@dataclass
class WorkerMetrics:
    """Metrics for worker performance tracking"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    avg_task_time: float = 0.0
    last_activity: Optional[datetime] = None
    utilization: float = 0.0


class DynamicWorkerPool:
    """Dynamic worker pool that scales based on workload and API capacity"""
    
    def __init__(self, config: WorkerPoolConfig = None):
        self.config = config or WorkerPoolConfig()
        
        # Worker pools by tier
        self.worker_pools: Dict[WorkerTier, ThreadPoolExecutor] = {}
        self.worker_metrics: Dict[WorkerTier, WorkerMetrics] = {}
        
        # Task queues by tier
        self.task_queues: Dict[WorkerTier, queue.Queue] = {
            tier: queue.Queue() for tier in WorkerTier
        }
        
        # Scaling control
        self.scaling_enabled = True
        self.last_scale_check = time.time()
        self.scaling_lock = threading.Lock()
        
        # Performance tracking
        self.api_capacity_tracker = {
            'total_capacity': 100,  # requests per second
            'current_usage': 0,
            'available_capacity': 100
        }
        
        # Initialize worker pools
        self._initialize_worker_pools()
        
        # Start scaling monitor
        self.scaling_thread = threading.Thread(target=self._scaling_monitor, daemon=True)
        self.scaling_thread.start()
        
        logger.info("Dynamic Worker Pool initialized")
    
    def _initialize_worker_pools(self):
        """Initialize worker pools for each tier"""
        try:
            # Initial worker allocation based on tier priority
            initial_workers = {
                WorkerTier.ULTRA_PRIORITY: min(8, self.config.max_workers // 4),
                WorkerTier.HIGH_PRIORITY: min(6, self.config.max_workers // 4),
                WorkerTier.MEDIUM_PRIORITY: min(4, self.config.max_workers // 4),
                WorkerTier.LOW_PRIORITY: max(2, self.config.min_workers)
            }
            
            for tier, worker_count in initial_workers.items():
                self.worker_pools[tier] = ThreadPoolExecutor(
                    max_workers=worker_count,
                    thread_name_prefix=f"atlas-{tier.name.lower()}"
                )
                self.worker_metrics[tier] = WorkerMetrics()
                
                logger.info(f"Initialized {tier.name} pool with {worker_count} workers")
                
        except Exception as e:
            logger.error(f"Error initializing worker pools: {e}")
    
    def submit_task(self, tier: WorkerTier, task_func: Callable, *args, **kwargs) -> Optional[Any]:
        """Submit task to appropriate tier with load balancing"""
        try:
            # Check if we should scale up before submitting
            self._check_scaling_needed(tier)
            
            # Get worker pool for tier
            pool = self.worker_pools.get(tier)
            if not pool:
                logger.error(f"No worker pool available for tier {tier.name}")
                return None
            
            # Submit task
            future = pool.submit(self._execute_task_with_metrics, tier, task_func, *args, **kwargs)
            
            # Update metrics
            metrics = self.worker_metrics[tier]
            metrics.total_tasks += 1
            metrics.last_activity = datetime.now()
            
            return future
            
        except Exception as e:
            logger.error(f"Error submitting task to tier {tier.name}: {e}")
            return None
    
    def _execute_task_with_metrics(self, tier: WorkerTier, task_func: Callable, *args, **kwargs) -> Any:
        """Execute task with performance tracking"""
        start_time = time.time()
        metrics = self.worker_metrics[tier]
        
        try:
            # Execute the actual task
            result = task_func(*args, **kwargs)
            
            # Update success metrics
            execution_time = time.time() - start_time
            metrics.completed_tasks += 1
            
            # Update average task time (exponential moving average)
            if metrics.avg_task_time == 0:
                metrics.avg_task_time = execution_time
            else:
                metrics.avg_task_time = 0.8 * metrics.avg_task_time + 0.2 * execution_time
            
            return result
            
        except Exception as e:
            # Update failure metrics
            metrics.failed_tasks += 1
            logger.error(f"Task execution failed in tier {tier.name}: {e}")
            raise
    
    def _scaling_monitor(self):
        """Monitor worker pools and scale as needed"""
        while self.scaling_enabled:
            try:
                current_time = time.time()
                
                # Check if it's time to evaluate scaling
                if current_time - self.last_scale_check >= self.config.scale_check_interval:
                    self._evaluate_scaling()
                    self.last_scale_check = current_time
                
                # Brief sleep to prevent CPU overload
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"Error in scaling monitor: {e}")
                time.sleep(10)  # Longer sleep on error
    
    def _evaluate_scaling(self):
        """Evaluate and perform scaling for all tiers"""
        try:
            with self.scaling_lock:
                for tier in WorkerTier:
                    self._evaluate_tier_scaling(tier)
                    
        except Exception as e:
            logger.error(f"Error evaluating scaling: {e}")
    
    def _evaluate_tier_scaling(self, tier: WorkerTier):
        """Evaluate scaling for a specific tier"""
        try:
            pool = self.worker_pools[tier]
            metrics = self.worker_metrics[tier]
            
            # Calculate current utilization
            if hasattr(pool, '_threads'):
                active_threads = len([t for t in pool._threads if t.is_alive()])
                max_workers = pool._max_workers
                utilization = active_threads / max_workers if max_workers > 0 else 0
            else:
                utilization = 0.5  # Default assumption
            
            metrics.utilization = utilization
            
            # Scaling decisions
            if utilization > self.config.scale_up_threshold:
                self._scale_up_tier(tier)
            elif utilization < self.config.scale_down_threshold:
                self._scale_down_tier(tier)
                
        except Exception as e:
            logger.error(f"Error evaluating scaling for tier {tier.name}: {e}")
    
    def _scale_up_tier(self, tier: WorkerTier):
        """Scale up workers for a tier"""
        try:
            current_pool = self.worker_pools[tier]
            current_workers = current_pool._max_workers
            
            # Calculate new worker count (increase by 25% or minimum 2)
            new_workers = min(
                current_workers + max(2, int(current_workers * 0.25)),
                self.config.max_workers
            )
            
            if new_workers > current_workers:
                # Create new pool with more workers
                old_pool = current_pool
                self.worker_pools[tier] = ThreadPoolExecutor(
                    max_workers=new_workers,
                    thread_name_prefix=f"atlas-{tier.name.lower()}"
                )
                
                # Gracefully shutdown old pool
                old_pool.shutdown(wait=False)
                
                logger.info(f"Scaled up {tier.name} from {current_workers} to {new_workers} workers")
                
        except Exception as e:
            logger.error(f"Error scaling up tier {tier.name}: {e}")
    
    def _scale_down_tier(self, tier: WorkerTier):
        """Scale down workers for a tier"""
        try:
            current_pool = self.worker_pools[tier]
            current_workers = current_pool._max_workers
            
            # Calculate new worker count (decrease by 20% but maintain minimum)
            min_workers_for_tier = {
                WorkerTier.ULTRA_PRIORITY: 4,
                WorkerTier.HIGH_PRIORITY: 3,
                WorkerTier.MEDIUM_PRIORITY: 2,
                WorkerTier.LOW_PRIORITY: 1
            }
            
            min_workers = min_workers_for_tier.get(tier, self.config.min_workers)
            new_workers = max(
                min_workers,
                current_workers - max(1, int(current_workers * 0.2))
            )
            
            if new_workers < current_workers:
                # Create new pool with fewer workers
                old_pool = current_pool
                self.worker_pools[tier] = ThreadPoolExecutor(
                    max_workers=new_workers,
                    thread_name_prefix=f"atlas-{tier.name.lower()}"
                )
                
                # Gracefully shutdown old pool
                old_pool.shutdown(wait=False)
                
                logger.info(f"Scaled down {tier.name} from {current_workers} to {new_workers} workers")
                
        except Exception as e:
            logger.error(f"Error scaling down tier {tier.name}: {e}")
    
    def _check_scaling_needed(self, tier: WorkerTier):
        """Check if immediate scaling is needed for a tier"""
        try:
            # Quick check for immediate scaling needs
            pool = self.worker_pools[tier]
            queue_size = self.task_queues[tier].qsize()
            
            # If queue is backing up significantly, trigger immediate scale-up
            if queue_size > self.config.target_queue_size * 2:
                self._scale_up_tier(tier)
                
        except Exception as e:
            logger.error(f"Error checking scaling for tier {tier.name}: {e}")
    
    def get_pool_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics for all worker pools"""
        try:
            stats = {
                'timestamp': datetime.now().isoformat(),
                'total_pools': len(self.worker_pools),
                'scaling_enabled': self.scaling_enabled,
                'tiers': {}
            }
            
            total_workers = 0
            total_tasks = 0
            total_completed = 0
            total_failed = 0
            
            for tier, pool in self.worker_pools.items():
                metrics = self.worker_metrics[tier]
                
                tier_stats = {
                    'current_workers': pool._max_workers,
                    'utilization': metrics.utilization,
                    'total_tasks': metrics.total_tasks,
                    'completed_tasks': metrics.completed_tasks,
                    'failed_tasks': metrics.failed_tasks,
                    'success_rate': (metrics.completed_tasks / metrics.total_tasks) if metrics.total_tasks > 0 else 0,
                    'avg_task_time': metrics.avg_task_time,
                    'last_activity': metrics.last_activity.isoformat() if metrics.last_activity else None,
                    'queue_size': self.task_queues[tier].qsize()
                }
                
                stats['tiers'][tier.name] = tier_stats
                
                total_workers += pool._max_workers
                total_tasks += metrics.total_tasks
                total_completed += metrics.completed_tasks
                total_failed += metrics.failed_tasks
            
            stats['summary'] = {
                'total_workers': total_workers,
                'total_tasks': total_tasks,
                'total_completed': total_completed,
                'total_failed': total_failed,
                'overall_success_rate': (total_completed / total_tasks) if total_tasks > 0 else 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting pool statistics: {e}")
            return {'error': str(e)}
    
    def shutdown(self):
        """Gracefully shutdown all worker pools"""
        try:
            self.scaling_enabled = False
            
            # Shutdown all pools
            for tier, pool in self.worker_pools.items():
                logger.info(f"Shutting down {tier.name} worker pool")
                pool.shutdown(wait=True)
            
            logger.info("Dynamic Worker Pool shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during worker pool shutdown: {e}")


# Global instance for easy access
dynamic_worker_pool = DynamicWorkerPool()
