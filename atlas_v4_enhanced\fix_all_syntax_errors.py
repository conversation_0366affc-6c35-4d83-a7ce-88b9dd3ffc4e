#!/usr/bin/env python3
"""
Fix All Syntax Errors in A.T.L.A.S. Components
Comprehensive syntax error repair
"""

import re
from pathlib import Path

def fix_all_syntax_errors():
    """Fix all syntax errors in component files"""
    try:
        print('🔧 FIXING ALL SYNTAX ERRORS IN A.T.L.A.S. COMPONENTS')
        print('=' * 60)
        
        # Files to fix
        files_to_fix = [
            'atlas_alternative_data.py',
            'atlas_explainable_ai.py',
            'atlas_quantum_optimizer.py',
            'atlas_global_markets.py',
            'atlas_news_insights_engine.py'
        ]
        
        fixes_applied = 0
        
        for filename in files_to_fix:
            file_path = Path(filename)
            if not file_path.exists():
                print(f'   ⚠️  {filename} not found - skipping')
                continue
            
            print(f'\n🔧 Fixing {filename}...')
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix 1: Fix indentation after except ImportError:
            pattern1 = r'except ImportError:\s*#[^\n]*\ntry:'
            replacement1 = 'except ImportError:\n    # Check for newly installed libraries\n    try:'
            content = re.sub(pattern1, replacement1, content)
            
            # Fix 2: Fix logger calls before logger is defined
            # Remove logger calls from import sections
            lines = content.split('\n')
            in_import_section = True
            fixed_lines = []
            
            for line in lines:
                # Check if we're still in import section
                if line.strip().startswith('class ') or line.strip().startswith('def ') or line.strip().startswith('async def '):
                    in_import_section = False
                
                # Remove logger calls from import section
                if in_import_section and 'logger.info' in line and 'libraries now available' in line:
                    continue  # Skip this line
                elif in_import_section and 'logger.warning' in line and 'libraries still missing' in line:
                    continue  # Skip this line
                else:
                    fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            # Fix 3: Fix duplicate logger messages in initialization methods
            # Pattern to find and fix duplicate logger messages
            duplicate_patterns = [
                (r'(\s+)else:\s*\n\s+logger\.info\("\[.*?\].*?libraries now available.*?"\)\s*\n\s*\n\s+# Initialize Grok integration\s*\n\s+if self\.grok_integration_available:\s*\n\s+try:\s*\n\s+self\.grok_engine = AtlasGrokIntegrationEngine\(\)\s*\n\s+grok_success = await self\.grok_engine\.initialize\(\)\s*\n\s+if grok_success:\s*\n\s+logger\.info\("\[OK\].*?"\)\s*\n\s+else:\s*\n\s+logger\.info\("\[.*?\].*?libraries now available.*?"\)', 
                 r'\1else:\n\1    logger.info("[INFO] Libraries available in fallback mode")\n\n\1# Initialize Grok integration\n\1if self.grok_integration_available:\n\1    try:\n\1        self.grok_engine = AtlasGrokIntegrationEngine()\n\1        grok_success = await self.grok_engine.initialize()\n\1        if grok_success:\n\1            logger.info("[OK] Grok integration initialized")\n\1        else:\n\1            logger.warning("[WARN] Grok integration failed")'),
                
                (r'(\s+)logger\.info\("\[.*?\].*?libraries now available.*?"\)\s*\n\s+except Exception as e:\s*\n\s+logger\.error\(f"Grok integration initialization failed: \{e\}"\)\s*\n\s+self\.grok_engine = None\s*\n\s+else:\s*\n\s+logger\.info\("\[.*?\].*?libraries now available.*?"\)',
                 r'\1    except Exception as e:\n\1        logger.error(f"Grok integration initialization failed: {e}")\n\1        self.grok_engine = None\n\1else:\n\1    logger.info("[INFO] Using simplified processing without Grok")')
            ]
            
            for pattern, replacement in duplicate_patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
            
            # Fix 4: Ensure proper indentation for all blocks
            lines = content.split('\n')
            fixed_lines = []
            indent_level = 0
            
            for i, line in enumerate(lines):
                stripped = line.strip()
                
                # Skip empty lines
                if not stripped:
                    fixed_lines.append(line)
                    continue
                
                # Handle specific patterns that need fixing
                if stripped.startswith('logger.info') and i > 0:
                    prev_line = lines[i-1].strip()
                    if prev_line.endswith(':') and not prev_line.startswith('#'):
                        # This logger call should be indented relative to the previous line
                        prev_indent = len(lines[i-1]) - len(lines[i-1].lstrip())
                        line = ' ' * (prev_indent + 4) + stripped
                
                fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            # Check if content was actually changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f'   ✅ {filename} fixed')
                fixes_applied += 1
            else:
                print(f'   ℹ️  {filename} no changes needed')
        
        # Summary
        print(f'\n📊 SYNTAX FIX SUMMARY')
        print('-' * 30)
        print(f'   Files processed: {len(files_to_fix)}')
        print(f'   Files fixed: {fixes_applied}')
        print(f'   Success rate: {(fixes_applied/len(files_to_fix)*100):.1f}%')
        
        if fixes_applied > 0:
            print('\n✅ SYNTAX ERRORS FIXED!')
            print('   A.T.L.A.S. components should now load without syntax errors')
        else:
            print('\n✅ NO SYNTAX ERRORS FOUND!')
            print('   All components appear to be syntactically correct')
        
        return True
        
    except Exception as e:
        print(f'❌ SYNTAX FIX FAILED: {e}')
        return False

if __name__ == "__main__":
    success = fix_all_syntax_errors()
    exit(0 if success else 1)
