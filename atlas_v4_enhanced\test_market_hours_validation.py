#!/usr/bin/env python3
"""
Comprehensive Market Hours and Central Time Configuration Validation
Tests proper Central Time (8:30 AM - 3:00 PM CT) market hours detection for Houston, Texas
"""

import asyncio
import traceback
from datetime import datetime, time
import pytz

async def test_market_hours_configuration():
    """Comprehensive market hours and Central Time validation"""
    try:
        print('🕐 TESTING MARKET HOURS & CENTRAL TIME CONFIGURATION...')
        print('=' * 60)
        
        # Test imports
        print('📦 Testing imports...')
        from atlas_realtime_scanner import AtlasRealtimeScanner
        from atlas_market_core import AtlasMarketEngine
        print('✅ All imports successful')
        
        # Initialize components
        print('🚀 Initializing components...')
        scanner = AtlasRealtimeScanner()
        market_engine = AtlasMarketEngine()
        await market_engine.initialize()
        print('✅ Components initialized')
        
        # Test Central Time configuration
        print('🌎 Testing Central Time Configuration (Houston, TX)...')
        ct_tz = pytz.timezone('US/Central')
        et_tz = pytz.timezone('US/Eastern')
        
        current_ct = datetime.now(ct_tz)
        current_et = datetime.now(et_tz)
        
        print(f'   Current Houston Time (CT): {current_ct.strftime("%H:%M:%S %Z")}')
        print(f'   Current Eastern Time (ET): {current_et.strftime("%H:%M:%S %Z")}')
        print(f'   Date: {current_ct.strftime("%A, %B %d, %Y")}')
        
        # Test market hours definition
        print('📊 Testing Market Hours Definition...')
        market_open_ct = time(8, 30)  # 8:30 AM CT
        market_close_ct = time(15, 0)  # 3:00 PM CT
        market_open_et = time(9, 30)  # 9:30 AM ET
        market_close_et = time(16, 0)  # 4:00 PM ET
        
        print(f'   Market Hours (Central): {market_open_ct} - {market_close_ct}')
        print(f'   Market Hours (Eastern): {market_open_et} - {market_close_et}')
        
        # Test current market status
        print('🔍 Testing Current Market Status...')
        current_time_ct = current_ct.time()
        current_time_et = current_et.time()
        
        # Check if it's a weekday
        is_weekday = current_ct.weekday() < 5  # Monday=0, Friday=4
        print(f'   Is Weekday: {is_weekday} (Day: {current_ct.strftime("%A")})')
        
        # Central Time market hours check
        ct_market_open = market_open_ct <= current_time_ct <= market_close_ct and is_weekday
        print(f'   CT Market Hours Active: {ct_market_open}')
        
        # Eastern Time market hours check
        et_market_open = market_open_et <= current_time_et <= market_close_et and is_weekday
        print(f'   ET Market Hours Active: {et_market_open}')
        
        # Verify consistency
        if ct_market_open == et_market_open:
            print('✅ Timezone consistency verified')
        else:
            print('⚠️  Timezone inconsistency detected')
        
        # Test scanner market hours detection
        print('🎯 Testing Scanner Market Hours Detection...')
        should_scan = scanner._should_scan()
        scanner_market_hours = getattr(scanner, 'is_market_hours', None)
        
        print(f'   Scanner Should Scan: {should_scan}')
        print(f'   Scanner Market Hours: {scanner_market_hours}')
        print(f'   Scanner Config Market Hours Only: {scanner.config.market_hours_only}')
        
        # Test market engine market hours detection
        print('🏢 Testing Market Engine Market Hours Detection...')
        engine_market_hours = market_engine._is_market_hours()
        print(f'   Market Engine Market Hours: {engine_market_hours}')
        
        # Validate configuration consistency
        print('🔧 Validating Configuration Consistency...')
        
        expected_market_status = ct_market_open  # Use Central Time as reference
        
        consistency_checks = {
            'Scanner vs Expected': should_scan == expected_market_status if scanner.config.market_hours_only else True,
            'Market Engine vs Expected': engine_market_hours == expected_market_status,
            'CT vs ET Consistency': ct_market_open == et_market_open
        }
        
        all_consistent = True
        for check_name, is_consistent in consistency_checks.items():
            status = '✅' if is_consistent else '❌'
            print(f'   {status} {check_name}: {is_consistent}')
            if not is_consistent:
                all_consistent = False
        
        # Test specific time scenarios
        print('⏰ Testing Specific Time Scenarios...')
        
        test_times_ct = [
            (time(8, 29), False, "Before market open"),
            (time(8, 30), True, "Market open"),
            (time(12, 0), True, "Market midday"),
            (time(15, 0), True, "Market close"),
            (time(15, 1), False, "After market close"),
            (time(7, 0), False, "Early morning"),
            (time(18, 0), False, "Evening")
        ]
        
        for test_time, expected_open, description in test_times_ct:
            # Create test datetime with current date but specific time
            test_datetime_ct = current_ct.replace(
                hour=test_time.hour, 
                minute=test_time.minute, 
                second=0, 
                microsecond=0
            )
            
            # Only test if it's a weekday
            if is_weekday:
                test_market_open = market_open_ct <= test_time <= market_close_ct
                status = '✅' if test_market_open == expected_open else '❌'
                print(f'   {status} {test_time} CT - {description}: Expected {expected_open}, Got {test_market_open}')
        
        # Test weekend handling
        print('📅 Testing Weekend Handling...')
        if not is_weekday:
            print('   Currently weekend - market should be closed')
            if not should_scan and scanner.config.market_hours_only:
                print('   ✅ Scanner correctly disabled on weekend')
            else:
                print('   ⚠️  Scanner weekend handling may need review')
        else:
            print('   Currently weekday - weekend test skipped')
        
        # Performance and timing tests
        print('⚡ Testing Performance and Timing...')
        
        import time as time_module
        
        # Test market hours check performance
        start_time = time_module.time()
        for _ in range(100):
            scanner._should_scan()
        end_time = time_module.time()
        
        avg_time_ms = ((end_time - start_time) / 100) * 1000
        print(f'   Market hours check performance: {avg_time_ms:.2f}ms average')
        
        if avg_time_ms < 10:  # Should be very fast
            print('   ✅ Performance target met (<10ms)')
        else:
            print('   ⚠️  Performance may need optimization')
        
        # Summary
        print('\n📋 MARKET HOURS CONFIGURATION SUMMARY:')
        print(f'   Location: Houston, Texas (US/Central)')
        print(f'   Market Hours: 8:30 AM - 3:00 PM CT')
        print(f'   Equivalent: 9:30 AM - 4:00 PM ET')
        print(f'   Current Status: {"OPEN" if expected_market_status else "CLOSED"}')
        print(f'   Scanner Active: {"YES" if should_scan else "NO"}')
        print(f'   Configuration Valid: {"YES" if all_consistent else "NO"}')
        
        print('\n🔧 MARKET HOURS VALIDATION COMPLETE')
        
        if all_consistent:
            print('\n✅ ALL TESTS PASSED - Market hours configuration is correct')
            print('🎯 Central Time (Houston, TX) configuration validated')
            print('⚡ Ultra-responsive scanning during market hours confirmed')
        else:
            print('\n⚠️  SOME TESTS FAILED - Configuration needs review')
        
        return all_consistent
        
    except Exception as e:
        print(f'❌ MARKET HOURS VALIDATION FAILED: {e}')
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_market_hours_configuration())
    exit(0 if success else 1)
