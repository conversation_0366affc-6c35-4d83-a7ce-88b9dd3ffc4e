"""
A.T.L.A.S. Multi-Agent Core Architecture
Advanced multi-agent system for enhanced trading accuracy and reduced hallucinations
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import uuid

# CrewAI imports
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool

# Core A.T.L.A.S. imports
from models import EngineStatus
from config import get_api_config

logger = logging.getLogger(__name__)

# ============================================================================
# MULTI-AGENT ENUMS AND MODELS
# ============================================================================

class AgentRole(Enum):
    """Specialized agent roles in the multi-agent system"""
    DATA_VALIDATOR = "data_validator"
    PATTERN_DETECTOR = "pattern_detector"
    ANALYSIS_ENGINE = "analysis_engine"
    RISK_MANAGER = "risk_manager"
    TRADE_EXECUTOR = "trade_executor"
    VALIDATION_SUPERVISOR = "validation_supervisor"

class TaskPriority(Enum):
    """Task priority levels for agent coordination"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class AgentStatus(Enum):
    """Agent operational status"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"

@dataclass
class AgentMetrics:
    """Performance metrics for individual agents"""
    agent_id: str
    role: AgentRole
    tasks_completed: int = 0
    tasks_failed: int = 0
    average_response_time: float = 0.0
    accuracy_score: float = 0.0
    confidence_score: float = 0.0
    last_activity: Optional[datetime] = None

@dataclass
class MultiAgentTask:
    """Enhanced task structure for multi-agent coordination"""
    task_id: str
    description: str
    priority: TaskPriority
    required_agents: List[AgentRole]
    input_data: Dict[str, Any]
    expected_output: Dict[str, Any]
    timeout_seconds: int = 300
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"
    results: Dict[str, Any] = field(default_factory=dict)
    confidence_scores: Dict[str, float] = field(default_factory=dict)

@dataclass
class AgentCommunication:
    """Inter-agent communication protocol"""
    sender_id: str
    receiver_id: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    requires_response: bool = False
    correlation_id: Optional[str] = None

# ============================================================================
# BASE AGENT ARCHITECTURE
# ============================================================================

class AtlasBaseAgent(ABC):
    """Base class for all A.T.L.A.S. specialized agents"""
    
    def __init__(self, agent_id: str, role: AgentRole, config: Dict[str, Any] = None):
        self.agent_id = agent_id
        self.role = role
        self.config = config or {}
        self.status = AgentStatus.INITIALIZING
        self.metrics = AgentMetrics(agent_id=agent_id, role=role)
        self.message_queue = asyncio.Queue()
        self.active_tasks = {}
        self.tools = []
        self.crew_agent = None
        
        # Initialize logging
        self.logger = logging.getLogger(f"atlas.agent.{role.value}")
        
    async def initialize(self) -> bool:
        """Initialize the agent and its capabilities"""
        try:
            self.logger.info(f"Initializing {self.role.value} agent {self.agent_id}")
            
            # Initialize agent-specific tools
            await self._initialize_tools()
            
            # Create CrewAI agent instance
            self.crew_agent = Agent(
                role=self.role.value.replace('_', ' ').title(),
                goal=self._get_agent_goal(),
                backstory=self._get_agent_backstory(),
                tools=self.tools,
                verbose=True,
                allow_delegation=False,
                max_iter=5,
                memory=True
            )
            
            self.status = AgentStatus.ACTIVE
            self.metrics.last_activity = datetime.now()
            
            self.logger.info(f"Agent {self.agent_id} initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent {self.agent_id}: {e}")
            self.status = AgentStatus.ERROR
            return False
    
    @abstractmethod
    async def _initialize_tools(self):
        """Initialize agent-specific tools"""
        pass
    
    @abstractmethod
    def _get_agent_goal(self) -> str:
        """Get the agent's primary goal"""
        pass
    
    @abstractmethod
    def _get_agent_backstory(self) -> str:
        """Get the agent's backstory for context"""
        pass
    
    @abstractmethod
    async def process_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Process a specific task assigned to this agent"""
        pass
    
    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a task with error handling and metrics tracking"""
        start_time = time.time()
        self.status = AgentStatus.BUSY
        
        try:
            self.logger.info(f"Agent {self.agent_id} executing task {task.task_id}")
            
            # Add task to active tasks
            self.active_tasks[task.task_id] = task
            task.started_at = datetime.now()
            task.status = "running"
            
            # Process the task
            result = await self.process_task(task)
            
            # Update metrics
            execution_time = time.time() - start_time
            self.metrics.tasks_completed += 1
            self.metrics.average_response_time = (
                (self.metrics.average_response_time * (self.metrics.tasks_completed - 1) + execution_time) 
                / self.metrics.tasks_completed
            )
            
            # Update task status
            task.completed_at = datetime.now()
            task.status = "completed"
            task.results = result
            
            # Remove from active tasks
            del self.active_tasks[task.task_id]
            
            self.status = AgentStatus.ACTIVE
            self.metrics.last_activity = datetime.now()
            
            self.logger.info(f"Agent {self.agent_id} completed task {task.task_id} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Agent {self.agent_id} failed to execute task {task.task_id}: {e}")
            
            # Update error metrics
            self.metrics.tasks_failed += 1
            task.status = "failed"
            task.results = {"error": str(e), "timestamp": datetime.now().isoformat()}
            
            # Remove from active tasks
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            self.status = AgentStatus.ACTIVE
            raise
    
    async def send_message(self, receiver_id: str, message_type: str, payload: Dict[str, Any], 
                          requires_response: bool = False) -> Optional[str]:
        """Send a message to another agent"""
        correlation_id = str(uuid.uuid4()) if requires_response else None
        
        message = AgentCommunication(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            payload=payload,
            requires_response=requires_response,
            correlation_id=correlation_id
        )
        
        # In a real implementation, this would use a message broker
        # For now, we'll use a simple queue-based approach
        self.logger.debug(f"Agent {self.agent_id} sending message to {receiver_id}: {message_type}")
        
        return correlation_id
    
    async def receive_message(self) -> Optional[AgentCommunication]:
        """Receive a message from the message queue"""
        try:
            message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
            return message
        except asyncio.TimeoutError:
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics"""
        return {
            "agent_id": self.agent_id,
            "role": self.role.value,
            "status": self.status.value,
            "active_tasks": len(self.active_tasks),
            "metrics": {
                "tasks_completed": self.metrics.tasks_completed,
                "tasks_failed": self.metrics.tasks_failed,
                "average_response_time": self.metrics.average_response_time,
                "accuracy_score": self.metrics.accuracy_score,
                "confidence_score": self.metrics.confidence_score,
                "last_activity": self.metrics.last_activity.isoformat() if self.metrics.last_activity else None
            }
        }

# ============================================================================
# MULTI-AGENT COORDINATOR
# ============================================================================

class MultiAgentCoordinator:
    """Central coordinator for managing multiple specialized agents"""
    
    def __init__(self):
        self.agents: Dict[str, AtlasBaseAgent] = {}
        self.task_queue = asyncio.Queue()
        self.active_tasks: Dict[str, MultiAgentTask] = {}
        self.task_history: List[MultiAgentTask] = []
        self.status = EngineStatus.INITIALIZING
        
        # Performance metrics
        self.total_tasks_processed = 0
        self.total_tasks_failed = 0
        self.average_task_completion_time = 0.0
        self.system_accuracy_score = 0.0
        
        self.logger = logging.getLogger("atlas.multi_agent.coordinator")
    
    async def initialize(self) -> bool:
        """Initialize the multi-agent coordinator"""
        try:
            self.logger.info("Initializing Multi-Agent Coordinator")
            self.status = EngineStatus.ACTIVE
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Multi-Agent Coordinator: {e}")
            self.status = EngineStatus.FAILED
            return False
    
    async def register_agent(self, agent: AtlasBaseAgent) -> bool:
        """Register a new agent with the coordinator"""
        try:
            if await agent.initialize():
                self.agents[agent.agent_id] = agent
                self.logger.info(f"Registered agent {agent.agent_id} with role {agent.role.value}")
                return True
            else:
                self.logger.error(f"Failed to initialize agent {agent.agent_id}")
                return False
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent.agent_id}: {e}")
            return False
    
    async def submit_task(self, task: MultiAgentTask) -> str:
        """Submit a task for processing by the multi-agent system"""
        task.task_id = str(uuid.uuid4())
        self.active_tasks[task.task_id] = task
        await self.task_queue.put(task)
        
        self.logger.info(f"Submitted task {task.task_id} with priority {task.priority.value}")
        return task.task_id
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status and metrics"""
        agent_statuses = {agent_id: agent.get_status() for agent_id, agent in self.agents.items()}
        
        return {
            "coordinator_status": self.status.value,
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.status == AgentStatus.ACTIVE]),
            "active_tasks": len(self.active_tasks),
            "queued_tasks": self.task_queue.qsize(),
            "system_metrics": {
                "total_tasks_processed": self.total_tasks_processed,
                "total_tasks_failed": self.total_tasks_failed,
                "average_completion_time": self.average_task_completion_time,
                "system_accuracy_score": self.system_accuracy_score
            },
            "agents": agent_statuses
        }
