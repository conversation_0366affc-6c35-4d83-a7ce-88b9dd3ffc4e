#!/usr/bin/env python3
"""
Import test for A.T.L.A.S. Multi-Agent System
Tests that all core modules can be imported successfully
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module"""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} - {e}")
        return False
    except Exception as e:
        print(f"⚠️ {description}: {module_name} - {e}")
        return False

def main():
    """Main test runner"""
    print("=" * 60)
    print("🚀 A.T.L.A.S. Multi-Agent System Import Tests")
    print("=" * 60)
    
    tests = [
        ("models", "Core Models"),
        ("atlas_multi_agent_core", "Multi-Agent Core"),
        ("atlas_data_validation_agent", "Data Validation Agent"),
        ("atlas_pattern_detection_agent", "Pattern Detection Agent"),
        ("atlas_analysis_agent", "Analysis Agent"),
        ("atlas_risk_management_agent", "Risk Management Agent"),
        ("atlas_trade_execution_agent", "Trade Execution Agent"),
        ("atlas_validation_agent", "Validation Agent"),
        ("atlas_multi_agent_orchestrator", "Multi-Agent Orchestrator"),
        ("atlas_security_compliance", "Security & Compliance"),
        ("atlas_monitoring_metrics", "Monitoring & Metrics"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_name, description in tests:
        if test_import(module_name, description):
            passed += 1
    
    print("\n" + "=" * 60)
    print("📊 IMPORT TEST SUMMARY")
    print("=" * 60)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ ALL IMPORTS SUCCESSFUL")
        return True
    else:
        print("❌ SOME IMPORTS FAILED")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
