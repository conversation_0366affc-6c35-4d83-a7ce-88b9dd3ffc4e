"""
A.T.L.A.S. Grok Resilience Manager
Advanced error handling, circuit breakers, retry logic, and fallback mechanisms
for production-grade reliability and fault tolerance.
"""

import asyncio
import time
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import json

# Core imports
from atlas_grok_integration import GrokRequest, GrokResponse, GrokTaskType, GrokCapability

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5          # Failures before opening
    recovery_timeout: int = 60          # Seconds before trying half-open
    success_threshold: int = 3          # Successes needed to close from half-open
    timeout_seconds: float = 30.0       # Request timeout
    
@dataclass
class RetryConfig:
    """Retry configuration"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True

class CircuitBreaker:
    """Circuit breaker for API resilience"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.next_attempt_time = None
        
    def can_execute(self) -> bool:
        """Check if request can be executed"""
        now = datetime.now()
        
        if self.state == CircuitState.CLOSED:
            return True
        elif self.state == CircuitState.OPEN:
            if self.next_attempt_time and now >= self.next_attempt_time:
                self.state = CircuitState.HALF_OPEN
                self.success_count = 0
                logger.info(f"Circuit breaker {self.name} transitioning to HALF_OPEN")
                return True
            return False
        elif self.state == CircuitState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self):
        """Record successful execution"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                logger.info(f"Circuit breaker {self.name} closed after recovery")
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """Record failed execution"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self.state = CircuitState.OPEN
                self.next_attempt_time = datetime.now() + timedelta(seconds=self.config.recovery_timeout)
                logger.warning(f"Circuit breaker {self.name} opened due to failures")
        elif self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            self.next_attempt_time = datetime.now() + timedelta(seconds=self.config.recovery_timeout)
            logger.warning(f"Circuit breaker {self.name} reopened during half-open test")
    
    def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status"""
        return {
            'name': self.name,
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure': self.last_failure_time.isoformat() if self.last_failure_time else None,
            'next_attempt': self.next_attempt_time.isoformat() if self.next_attempt_time else None
        }

class RetryManager:
    """Advanced retry logic with exponential backoff and jitter"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
    
    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"Retry succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt == self.config.max_attempts - 1:
                    logger.error(f"All {self.config.max_attempts} retry attempts failed")
                    break
                
                # Calculate delay with exponential backoff and jitter
                delay = min(
                    self.config.base_delay * (self.config.exponential_base ** attempt),
                    self.config.max_delay
                )
                
                if self.config.jitter:
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
                
                logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {delay:.2f}s")
                await asyncio.sleep(delay)
        
        raise last_exception

class FallbackManager:
    """Intelligent fallback system for when Grok API is unavailable"""
    
    def __init__(self):
        self.fallback_strategies = {
            GrokTaskType.LOGICAL_REASONING: self._logical_reasoning_fallback,
            GrokTaskType.MARKET_PSYCHOLOGY: self._market_psychology_fallback,
            GrokTaskType.IMAGE_ANALYSIS: self._image_analysis_fallback,
            GrokTaskType.REAL_TIME_SENTIMENT: self._sentiment_analysis_fallback,
            GrokTaskType.PATTERN_DETECTION: self._pattern_detection_fallback
        }
    
    async def get_fallback_response(self, request: GrokRequest) -> GrokResponse:
        """Get fallback response when Grok API is unavailable"""
        try:
            fallback_func = self.fallback_strategies.get(request.task_type)
            if fallback_func:
                content = await fallback_func(request)
            else:
                content = await self._generic_fallback(request)
            
            return GrokResponse(
                success=True,
                content=content,
                confidence=0.6,  # Lower confidence for fallback
                task_type=request.task_type,
                capability=request.capability,
                processing_time=0.1,
                fallback_used=True,
                metadata={'fallback_strategy': 'intelligent_fallback'}
            )
            
        except Exception as e:
            logger.error(f"Fallback strategy failed: {e}")
            return GrokResponse(
                success=False,
                content="",
                confidence=0.0,
                task_type=request.task_type,
                capability=request.capability,
                processing_time=0.0,
                error_message=f"Fallback failed: {str(e)}",
                fallback_used=True
            )
    
    async def _logical_reasoning_fallback(self, request: GrokRequest) -> str:
        """Fallback for logical reasoning tasks"""
        return f"""
        FALLBACK ANALYSIS - Logical Reasoning
        
        Query: {request.prompt[:200]}...
        
        Based on standard market analysis principles:
        
        1. **Market Context**: Current market conditions suggest mixed signals with both bullish and bearish indicators present.
        
        2. **Risk Assessment**: Medium risk environment with elevated volatility requiring careful position sizing.
        
        3. **Recommendation**: Consider a balanced approach with:
           - Reduced position sizes
           - Tight stop-losses
           - Diversified exposure
        
        4. **Monitoring**: Key levels to watch include major support/resistance zones and volume patterns.
        
        Note: This is a fallback analysis. For enhanced insights, ensure Grok API connectivity.
        """
    
    async def _market_psychology_fallback(self, request: GrokRequest) -> str:
        """Fallback for market psychology analysis"""
        return f"""
        FALLBACK ANALYSIS - Market Psychology
        
        Current market sentiment appears mixed based on standard indicators:
        
        **Fear & Greed Indicators:**
        - Volatility levels suggest moderate uncertainty
        - Volume patterns indicate average participation
        - Price action shows consolidation tendencies
        
        **Participant Behavior:**
        - Institutional activity: Moderate
        - Retail sentiment: Neutral to slightly cautious
        - Options flow: Balanced put/call activity
        
        **Psychological Levels:**
        - Key psychological support/resistance at round numbers
        - Market likely to test recent highs/lows
        
        Recommendation: Maintain disciplined approach with predetermined entry/exit levels.
        """
    
    async def _image_analysis_fallback(self, request: GrokRequest) -> str:
        """Fallback for image analysis tasks"""
        return f"""
        FALLBACK ANALYSIS - Chart Analysis
        
        Unable to process chart image directly. Standard technical analysis guidelines:
        
        **Key Analysis Points:**
        1. Identify current trend direction (up/down/sideways)
        2. Locate major support and resistance levels
        3. Check volume confirmation for price moves
        4. Look for chart patterns (triangles, flags, head & shoulders)
        5. Assess momentum indicators (RSI, MACD)
        
        **Risk Management:**
        - Set stop-loss below key support levels
        - Target resistance levels for profit-taking
        - Monitor volume for trend confirmation
        
        For detailed chart analysis, please ensure image processing capabilities are available.
        """
    
    async def _sentiment_analysis_fallback(self, request: GrokRequest) -> str:
        """Fallback for sentiment analysis"""
        return f"""
        FALLBACK ANALYSIS - Sentiment Analysis
        
        Standard sentiment indicators suggest:
        
        **Market Sentiment:** Neutral to Mixed
        - No extreme bullish or bearish readings
        - Moderate uncertainty in market direction
        - Balanced risk-on/risk-off positioning
        
        **Recommendation:**
        - Maintain balanced portfolio allocation
        - Avoid extreme positioning
        - Monitor for sentiment shifts
        - Use standard risk management practices
        
        For real-time sentiment analysis, ensure live data connectivity.
        """
    
    async def _pattern_detection_fallback(self, request: GrokRequest) -> str:
        """Fallback for pattern detection"""
        return f"""
        FALLBACK ANALYSIS - Pattern Detection
        
        Standard pattern recognition guidelines:
        
        **Common Patterns to Monitor:**
        1. Trend continuation patterns (flags, pennants)
        2. Reversal patterns (head & shoulders, double tops/bottoms)
        3. Consolidation patterns (triangles, rectangles)
        
        **Pattern Validation:**
        - Volume confirmation required
        - Multiple timeframe alignment
        - Risk/reward ratio assessment
        
        **Trading Approach:**
        - Wait for pattern completion
        - Enter on breakout with volume
        - Set stops below pattern support
        
        For advanced pattern recognition, ensure full system connectivity.
        """
    
    async def _generic_fallback(self, request: GrokRequest) -> str:
        """Generic fallback for unknown task types"""
        return f"""
        FALLBACK ANALYSIS - General Market Guidance
        
        Task: {request.task_type.value}
        Capability: {request.capability.value}
        
        Standard market analysis principles apply:
        
        1. **Risk Management**: Always use appropriate position sizing and stop-losses
        2. **Trend Analysis**: Follow the primary trend direction
        3. **Volume Confirmation**: Ensure volume supports price movements
        4. **Multiple Timeframes**: Check alignment across different timeframes
        5. **Market Context**: Consider overall market conditions
        
        This is a fallback response. For enhanced analysis, ensure full system connectivity.
        """

class ResilienceManager:
    """Main resilience manager coordinating all fault tolerance mechanisms"""
    
    def __init__(self):
        self.circuit_breakers = {
            'grok_api': CircuitBreaker('grok_api', CircuitBreakerConfig()),
            'live_search': CircuitBreaker('live_search', CircuitBreakerConfig(failure_threshold=3)),
            'image_analysis': CircuitBreaker('image_analysis', CircuitBreakerConfig(timeout_seconds=60.0))
        }
        
        self.retry_manager = RetryManager(RetryConfig())
        self.fallback_manager = FallbackManager()
        
    async def execute_resilient_request(self, request: GrokRequest, client_func: Callable) -> GrokResponse:
        """Execute request with full resilience mechanisms"""
        circuit_key = self._get_circuit_key(request)
        circuit = self.circuit_breakers.get(circuit_key, self.circuit_breakers['grok_api'])
        
        # Check circuit breaker
        if not circuit.can_execute():
            logger.warning(f"Circuit breaker {circuit_key} is open, using fallback")
            return await self.fallback_manager.get_fallback_response(request)
        
        try:
            # Execute with retry logic
            response = await self.retry_manager.execute_with_retry(client_func, request)
            
            # Record success
            circuit.record_success()
            return response
            
        except Exception as e:
            # Record failure
            circuit.record_failure()
            
            logger.error(f"Request failed after retries: {e}")
            
            # Use fallback
            return await self.fallback_manager.get_fallback_response(request)
    
    def _get_circuit_key(self, request: GrokRequest) -> str:
        """Determine which circuit breaker to use"""
        if request.search_parameters:
            return 'live_search'
        elif request.capability == GrokCapability.VISION:
            return 'image_analysis'
        else:
            return 'grok_api'
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status"""
        circuit_statuses = {name: cb.get_status() for name, cb in self.circuit_breakers.items()}
        
        # Calculate overall health score
        healthy_circuits = sum(1 for cb in self.circuit_breakers.values() if cb.state == CircuitState.CLOSED)
        health_score = healthy_circuits / len(self.circuit_breakers)
        
        return {
            'health_score': health_score,
            'status': 'healthy' if health_score > 0.8 else 'degraded' if health_score > 0.5 else 'unhealthy',
            'circuit_breakers': circuit_statuses,
            'timestamp': datetime.now().isoformat()
        }
