"""
A.T.L.A.S. Security Audit Script
Comprehensive security analysis for trading system deployment
"""

import os
import re
import json
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
import hashlib

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SecurityAuditor:
    """Comprehensive security auditor for A.T.L.A.S. system"""
    
    def __init__(self):
        self.audit_results = {}
        self.security_score = 0
        self.critical_issues = []
        self.warnings = []
        
    def scan_for_hardcoded_secrets(self) -> Dict[str, Any]:
        """Scan for hardcoded API keys, passwords, and secrets"""
        logger.info("Scanning for hardcoded secrets...")
        
        secret_patterns = {
            'api_keys': [
                r'api[_-]?key["\s]*[:=]["\s]*[a-zA-Z0-9]{20,}',
                r'apikey["\s]*[:=]["\s]*[a-zA-Z0-9]{20,}',
                r'key["\s]*[:=]["\s]*[a-zA-Z0-9]{32,}',
            ],
            'tokens': [
                r'token["\s]*[:=]["\s]*[a-zA-Z0-9]{20,}',
                r'access[_-]?token["\s]*[:=]["\s]*[a-zA-Z0-9]{20,}',
                r'bearer["\s]*[:=]["\s]*[a-zA-Z0-9]{20,}',
            ],
            'passwords': [
                r'password["\s]*[:=]["\s]*[a-zA-Z0-9]{8,}',
                r'passwd["\s]*[:=]["\s]*[a-zA-Z0-9]{8,}',
                r'pwd["\s]*[:=]["\s]*[a-zA-Z0-9]{8,}',
            ],
            'database_urls': [
                r'postgresql://[^"\s]+',
                r'mysql://[^"\s]+',
                r'mongodb://[^"\s]+',
            ]
        }
        
        findings = {}
        total_issues = 0
        
        for py_file in Path('.').glob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_issues = []
                for category, patterns in secret_patterns.items():
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            # Skip obvious placeholders
                            if any(placeholder in match.group().lower() for placeholder in 
                                  ['placeholder', 'your_', 'demo', 'test', 'example', 'xxx']):
                                continue
                            
                            line_num = content[:match.start()].count('\n') + 1
                            file_issues.append({
                                'category': category,
                                'pattern': pattern,
                                'match': match.group(),
                                'line': line_num,
                                'severity': 'CRITICAL' if category in ['api_keys', 'tokens'] else 'HIGH'
                            })
                            total_issues += 1
                
                if file_issues:
                    findings[str(py_file)] = file_issues
                    
            except Exception as e:
                logger.error(f"Error scanning {py_file}: {e}")
        
        return {
            'total_files_scanned': len(list(Path('.').glob('*.py'))),
            'files_with_secrets': len(findings),
            'total_secret_issues': total_issues,
            'findings': findings,
            'security_score_impact': -20 if total_issues > 0 else 0
        }
    
    def check_input_validation(self) -> Dict[str, Any]:
        """Check for missing input validation vulnerabilities"""
        logger.info("Checking input validation...")
        
        validation_issues = []
        
        # Patterns that indicate potential input validation issues
        dangerous_patterns = [
            (r'eval\s*\(', 'Code execution vulnerability'),
            (r'exec\s*\(', 'Code execution vulnerability'),
            (r'os\.system\s*\(', 'Command injection vulnerability'),
            (r'subprocess\.[^(]*\([^)]*shell\s*=\s*True', 'Shell injection vulnerability'),
            (r'\.format\s*\([^)]*\)', 'String formatting without validation'),
            (r'%\s*[^%]', 'String interpolation without validation'),
            (r'json\.loads\s*\([^)]*\)', 'JSON parsing without validation'),
            (r'pickle\.loads\s*\([^)]*\)', 'Unsafe deserialization'),
        ]
        
        for py_file in Path('.').glob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern, description in dangerous_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        validation_issues.append({
                            'file': str(py_file),
                            'line': line_num,
                            'pattern': pattern,
                            'description': description,
                            'code_snippet': match.group(),
                            'severity': 'HIGH'
                        })
                        
            except Exception as e:
                logger.error(f"Error checking validation in {py_file}: {e}")
        
        return {
            'total_validation_issues': len(validation_issues),
            'issues': validation_issues,
            'security_score_impact': -5 * len(validation_issues)
        }
    
    def check_error_handling(self) -> Dict[str, Any]:
        """Check for proper error handling and information disclosure"""
        logger.info("Checking error handling...")
        
        error_issues = []
        
        # Look for bare except clauses and information disclosure
        error_patterns = [
            (r'except\s*:', 'Bare except clause - may hide errors'),
            (r'print\s*\([^)]*exception[^)]*\)', 'Exception details printed to console'),
            (r'logger\.[^(]*\([^)]*password[^)]*\)', 'Password logged'),
            (r'logger\.[^(]*\([^)]*key[^)]*\)', 'API key potentially logged'),
            (r'traceback\.print_exc\s*\(\)', 'Full traceback exposed'),
        ]
        
        for py_file in Path('.').glob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern, description in error_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        error_issues.append({
                            'file': str(py_file),
                            'line': line_num,
                            'description': description,
                            'code_snippet': match.group(),
                            'severity': 'MEDIUM'
                        })
                        
            except Exception as e:
                logger.error(f"Error checking error handling in {py_file}: {e}")
        
        return {
            'total_error_issues': len(error_issues),
            'issues': error_issues,
            'security_score_impact': -2 * len(error_issues)
        }
    
    def check_file_permissions(self) -> Dict[str, Any]:
        """Check file permissions for security issues"""
        logger.info("Checking file permissions...")
        
        permission_issues = []
        
        # Check for overly permissive files
        sensitive_files = ['.env', 'config.py', 'atlas_secrets_manager.py']
        
        for file_path in sensitive_files:
            if os.path.exists(file_path):
                stat_info = os.stat(file_path)
                permissions = oct(stat_info.st_mode)[-3:]
                
                # Check if file is world-readable (last digit > 0)
                if int(permissions[-1]) > 0:
                    permission_issues.append({
                        'file': file_path,
                        'permissions': permissions,
                        'issue': 'World-readable sensitive file',
                        'severity': 'HIGH'
                    })
                
                # Check if file is group-writable (middle digit >= 2)
                if int(permissions[-2]) >= 2:
                    permission_issues.append({
                        'file': file_path,
                        'permissions': permissions,
                        'issue': 'Group-writable sensitive file',
                        'severity': 'MEDIUM'
                    })
        
        return {
            'total_permission_issues': len(permission_issues),
            'issues': permission_issues,
            'security_score_impact': -10 * len([i for i in permission_issues if i['severity'] == 'HIGH'])
        }
    
    def check_dependency_security(self) -> Dict[str, Any]:
        """Check for known vulnerable dependencies"""
        logger.info("Checking dependency security...")
        
        # Known vulnerable patterns (simplified check)
        vulnerable_packages = {
            'requests': ['2.25.0', '2.25.1'],  # Example vulnerable versions
            'urllib3': ['1.26.0', '1.26.1'],
            'pillow': ['8.0.0', '8.0.1'],
        }
        
        dependency_issues = []
        
        if os.path.exists('requirements.txt'):
            try:
                with open('requirements.txt', 'r') as f:
                    requirements = f.read()
                
                for package, vulnerable_versions in vulnerable_packages.items():
                    for version in vulnerable_versions:
                        if f"{package}=={version}" in requirements:
                            dependency_issues.append({
                                'package': package,
                                'version': version,
                                'issue': 'Known vulnerable version',
                                'severity': 'HIGH'
                            })
                            
            except Exception as e:
                logger.error(f"Error checking dependencies: {e}")
        
        return {
            'total_dependency_issues': len(dependency_issues),
            'issues': dependency_issues,
            'security_score_impact': -15 * len(dependency_issues)
        }
    
    def check_trading_security(self) -> Dict[str, Any]:
        """Check trading-specific security issues"""
        logger.info("Checking trading-specific security...")
        
        trading_issues = []
        
        # Check for paper trading enforcement
        config_files = ['config.py', 'atlas_trading_core.py']
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        content = f.read()
                    
                    # Check for live trading without proper safeguards
                    if 'paper_trading_mode = False' in content:
                        trading_issues.append({
                            'file': config_file,
                            'issue': 'Live trading enabled without safeguards',
                            'severity': 'CRITICAL'
                        })
                    
                    # Check for hardcoded position sizes
                    if re.search(r'position_size\s*=\s*[0-9.]+', content):
                        trading_issues.append({
                            'file': config_file,
                            'issue': 'Hardcoded position sizes detected',
                            'severity': 'MEDIUM'
                        })
                        
                except Exception as e:
                    logger.error(f"Error checking trading security in {config_file}: {e}")
        
        return {
            'total_trading_issues': len(trading_issues),
            'issues': trading_issues,
            'security_score_impact': -30 * len([i for i in trading_issues if i['severity'] == 'CRITICAL'])
        }
    
    async def run_comprehensive_audit(self) -> Dict[str, Any]:
        """Run comprehensive security audit"""
        logger.info("Starting comprehensive security audit...")
        
        start_time = datetime.now()
        
        # Run all security checks
        secret_scan = self.scan_for_hardcoded_secrets()
        input_validation = self.check_input_validation()
        error_handling = self.check_error_handling()
        file_permissions = self.check_file_permissions()
        dependency_security = self.check_dependency_security()
        trading_security = self.check_trading_security()
        
        # Calculate overall security score (start with 100)
        base_score = 100
        total_impact = (
            secret_scan['security_score_impact'] +
            input_validation['security_score_impact'] +
            error_handling['security_score_impact'] +
            file_permissions['security_score_impact'] +
            dependency_security['security_score_impact'] +
            trading_security['security_score_impact']
        )
        
        self.security_score = max(0, base_score + total_impact)
        
        # Determine security level
        if self.security_score >= 90:
            security_level = "EXCELLENT"
        elif self.security_score >= 75:
            security_level = "GOOD"
        elif self.security_score >= 60:
            security_level = "FAIR"
        elif self.security_score >= 40:
            security_level = "POOR"
        else:
            security_level = "CRITICAL"
        
        # Collect critical issues
        all_issues = []
        for check_result in [secret_scan, input_validation, error_handling, 
                           file_permissions, dependency_security, trading_security]:
            if 'issues' in check_result:
                all_issues.extend(check_result['issues'])
            elif 'findings' in check_result:
                for file_findings in check_result['findings'].values():
                    all_issues.extend(file_findings)
        
        critical_issues = [issue for issue in all_issues if issue.get('severity') == 'CRITICAL']
        high_issues = [issue for issue in all_issues if issue.get('severity') == 'HIGH']
        
        audit_duration = (datetime.now() - start_time).total_seconds()
        
        audit_results = {
            'audit_timestamp': datetime.now().isoformat(),
            'audit_duration_seconds': audit_duration,
            'security_score': self.security_score,
            'security_level': security_level,
            'total_issues': len(all_issues),
            'critical_issues_count': len(critical_issues),
            'high_issues_count': len(high_issues),
            'checks': {
                'hardcoded_secrets': secret_scan,
                'input_validation': input_validation,
                'error_handling': error_handling,
                'file_permissions': file_permissions,
                'dependency_security': dependency_security,
                'trading_security': trading_security
            },
            'critical_issues': critical_issues,
            'recommendations': self._generate_security_recommendations(security_level, critical_issues, high_issues)
        }
        
        self.audit_results = audit_results
        return audit_results
    
    def _generate_security_recommendations(self, security_level: str, critical_issues: List, high_issues: List) -> List[str]:
        """Generate security recommendations based on audit results"""
        recommendations = []
        
        if critical_issues:
            recommendations.append("🚨 IMMEDIATE ACTION REQUIRED: Address all CRITICAL security issues before deployment")
            recommendations.append("🔒 Review and remove all hardcoded secrets and API keys")
            recommendations.append("🛡️ Implement proper secrets management system")
        
        if high_issues:
            recommendations.append("⚠️ Address HIGH severity security issues")
            recommendations.append("🔍 Implement comprehensive input validation")
            recommendations.append("📝 Review error handling to prevent information disclosure")
        
        if security_level in ["POOR", "CRITICAL"]:
            recommendations.append("🚫 DO NOT DEPLOY to production until security score improves")
            recommendations.append("🔐 Conduct thorough security review with external auditor")
        
        if not recommendations:
            recommendations.append("✅ Security audit passed! System ready for deployment")
            recommendations.append("🔄 Schedule regular security audits")
        
        return recommendations
    
    def save_audit_report(self, filename: str = "security_audit_report.json"):
        """Save security audit report"""
        try:
            with open(filename, 'w') as f:
                json.dump(self.audit_results, f, indent=2, default=str)
            logger.info(f"Security audit report saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving audit report: {e}")


async def main():
    """Main security audit function"""
    auditor = SecurityAuditor()
    
    print("\n" + "="*80)
    print("A.T.L.A.S. COMPREHENSIVE SECURITY AUDIT")
    print("="*80)
    
    # Run comprehensive audit
    results = await auditor.run_comprehensive_audit()
    
    # Print summary
    print(f"\nSecurity audit completed in {results['audit_duration_seconds']:.2f} seconds")
    print(f"Security Score: {results['security_score']:.1f}/100")
    print(f"Security Level: {results['security_level']}")
    print(f"Total Issues: {results['total_issues']}")
    print(f"Critical Issues: {results['critical_issues_count']}")
    print(f"High Issues: {results['high_issues_count']}")
    
    print(f"\nSecurity Check Results:")
    for check_name, check_result in results['checks'].items():
        issue_count = check_result.get('total_issues', check_result.get('total_secret_issues', 
                     check_result.get('total_validation_issues', check_result.get('total_error_issues',
                     check_result.get('total_permission_issues', check_result.get('total_dependency_issues',
                     check_result.get('total_trading_issues', 0)))))))
        print(f"  {check_name.replace('_', ' ').title()}: {issue_count} issues")
    
    print(f"\nRecommendations:")
    for i, rec in enumerate(results['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    # Save report
    auditor.save_audit_report()
    
    print(f"\nDetailed report saved to: security_audit_report.json")
    print("="*80)


if __name__ == "__main__":
    asyncio.run(main())
