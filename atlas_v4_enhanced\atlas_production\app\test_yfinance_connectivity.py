"""
Test yfinance connectivity and functionality
"""

import yfinance as yf
import asyncio
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_yfinance_basic():
    """Test basic yfinance functionality"""
    print("Testing yfinance basic functionality...")
    
    try:
        # Test 1: Simple ticker creation
        ticker = yf.Ticker("AAPL")
        print("✅ Ticker created successfully")
        
        # Test 2: Try fast_info (less likely to be rate limited)
        try:
            fast_info = ticker.fast_info
            if hasattr(fast_info, 'last_price'):
                print(f"✅ Fast info works: AAPL price = ${fast_info.last_price}")
            else:
                print("❌ Fast info available but no price data")
        except Exception as e:
            print(f"❌ Fast info failed: {e}")
        
        # Test 3: Try regular info
        try:
            info = ticker.info
            if info and 'regularMarketPrice' in info:
                print(f"✅ Regular info works: AAPL price = ${info['regularMarketPrice']}")
            else:
                print("❌ Regular info available but no price data")
        except Exception as e:
            print(f"❌ Regular info failed: {e}")
        
        # Test 4: Try historical data
        try:
            hist = ticker.history(period="5d")
            if not hist.empty:
                print(f"✅ Historical data works: {len(hist)} days of data")
                print(f"   Latest close: ${hist['Close'].iloc[-1]:.2f}")
            else:
                print("❌ Historical data empty")
        except Exception as e:
            print(f"❌ Historical data failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic yfinance test failed: {e}")
        return False


async def test_multiple_symbols():
    """Test multiple symbols"""
    print("\nTesting multiple symbols...")
    
    symbols = ["AAPL", "MSFT", "GOOGL"]
    results = {}
    
    for symbol in symbols:
        try:
            ticker = yf.Ticker(symbol)
            
            # Try fast_info first
            try:
                fast_info = ticker.fast_info
                if hasattr(fast_info, 'last_price'):
                    results[symbol] = f"${fast_info.last_price:.2f}"
                    print(f"✅ {symbol}: ${fast_info.last_price:.2f}")
                    continue
            except:
                pass
            
            # Fallback to regular info
            try:
                info = ticker.info
                if info and 'regularMarketPrice' in info:
                    results[symbol] = f"${info['regularMarketPrice']:.2f}"
                    print(f"✅ {symbol}: ${info['regularMarketPrice']:.2f}")
                    continue
            except:
                pass
            
            results[symbol] = "Failed"
            print(f"❌ {symbol}: Failed to get price")
            
        except Exception as e:
            results[symbol] = f"Error: {e}"
            print(f"❌ {symbol}: Error - {e}")
    
    successful = len([r for r in results.values() if not r.startswith(("Failed", "Error"))])
    print(f"\nResults: {successful}/{len(symbols)} symbols successful")
    
    return successful > 0


async def test_with_delays():
    """Test with delays to avoid rate limiting"""
    print("\nTesting with delays to avoid rate limiting...")
    
    symbols = ["AAPL", "MSFT"]
    
    for symbol in symbols:
        try:
            print(f"Testing {symbol}...")
            
            # Add delay
            await asyncio.sleep(3)
            
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="5d")
            
            if not hist.empty:
                print(f"✅ {symbol}: Got {len(hist)} days of data")
            else:
                print(f"❌ {symbol}: No historical data")
                
        except Exception as e:
            print(f"❌ {symbol}: Error - {e}")


async def main():
    """Main test function"""
    print("="*60)
    print("YFINANCE CONNECTIVITY TEST")
    print("="*60)
    
    # Test 1: Basic functionality
    basic_works = await test_yfinance_basic()
    
    # Test 2: Multiple symbols
    if basic_works:
        multi_works = await test_multiple_symbols()
        
        # Test 3: With delays
        if not multi_works:
            await test_with_delays()
    
    print("\n" + "="*60)
    print("TEST COMPLETED")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
