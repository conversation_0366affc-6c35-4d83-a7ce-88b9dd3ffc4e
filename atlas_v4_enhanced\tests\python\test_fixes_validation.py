"""
A.T.L.A.S. Test Fixes Validation Script
Validates that the identified fixes are working correctly
"""

import asyncio
import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from config import settings
from atlas_orchestrator import AtlasOrchestrator
from atlas_ai_core import AtlasAIEngine
from atlas_market_core import AtlasMarketEngine
from atlas_lee_method import LeeMethodScanner
import pandas as pd
import numpy as np

class FixesValidator:
    """Validate that all fixes are working correctly"""
    
    def __init__(self):
        self.results = {}
        self.orchestrator = None
        
    async def validate_all_fixes(self):
        """Run validation tests for all fixes"""
        print("🔍 A.T.L.A.S. Fixes Validation Starting...")
        print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Test 1: AI Core Response Format Fix
        await self.test_ai_response_format()
        
        # Test 2: Market Data Configuration Fix
        await self.test_market_data_config()
        
        # Test 3: Lee Method Scanner Fix
        await self.test_lee_method_scanner()
        
        # Test 4: Port Configuration Fix
        self.test_port_configuration()
        
        # Test 5: Grok Integration Fallbacks
        await self.test_grok_fallbacks()
        
        # Generate summary
        self.generate_summary()
        
    async def test_ai_response_format(self):
        """Test that AI response format is correct"""
        print("\n🧠 Testing AI Core Response Format Fix...")
        try:
            # Initialize orchestrator
            self.orchestrator = AtlasOrchestrator()
            await self.orchestrator.initialize()
            
            # Test message processing through orchestrator
            response = await self.orchestrator.process_message(
                "Hello, what is A.T.L.A.S.?", 
                "test_session"
            )
            
            # Validate response format
            if isinstance(response, dict) and 'response' in response:
                print("✅ AI response format: FIXED")
                print(f"   Response type: {response.get('type', 'unknown')}")
                print(f"   Confidence: {response.get('confidence', 0.0)}")
                self.results['ai_response_format'] = 'PASSED'
            else:
                print("❌ AI response format: FAILED")
                self.results['ai_response_format'] = 'FAILED'
                
        except Exception as e:
            print(f"❌ AI response format test error: {e}")
            self.results['ai_response_format'] = 'ERROR'
            
    async def test_market_data_config(self):
        """Test that market data configuration is working"""
        print("\n📊 Testing Market Data Configuration Fix...")
        try:
            # Check FMP API key configuration
            fmp_key = settings.FMP_API_KEY
            if fmp_key and fmp_key != "None":
                print("✅ FMP API key: CONFIGURED")
                print(f"   Key: {fmp_key[:10]}...")
                
                # Test market engine through orchestrator
                if self.orchestrator and 'market' in self.orchestrator.engines:
                    market_engine = self.orchestrator.engines['market']
                    quote = await market_engine.get_quote("AAPL")
                    
                    if quote and hasattr(quote, 'price'):
                        print("✅ Market data fetching: WORKING")
                        print(f"   AAPL Price: ${quote.price}")
                        self.results['market_data_config'] = 'PASSED'
                    else:
                        print("⚠️ Market data fetching: API may be rate limited")
                        self.results['market_data_config'] = 'PARTIAL'
                else:
                    print("⚠️ Market engine not available through orchestrator")
                    self.results['market_data_config'] = 'PARTIAL'
            else:
                print("❌ FMP API key: NOT CONFIGURED")
                self.results['market_data_config'] = 'FAILED'
                
        except Exception as e:
            print(f"❌ Market data config test error: {e}")
            self.results['market_data_config'] = 'ERROR'
            
    async def test_lee_method_scanner(self):
        """Test that Lee Method scanner is working"""
        print("\n🎯 Testing Lee Method Scanner Fix...")
        try:
            scanner = LeeMethodScanner()
            
            # Test pattern detection method exists
            if hasattr(scanner, 'detect_lee_method_pattern'):
                print("✅ Lee Method pattern detection method: EXISTS")
                
                # Test with sample data
                test_data = pd.DataFrame({
                    'close': np.random.randn(100).cumsum() + 100,
                    'high': np.random.randn(100).cumsum() + 101,
                    'low': np.random.randn(100).cumsum() + 99,
                    'volume': np.random.randint(1000000, 5000000, 100)
                })
                
                # Calculate indicators
                test_data_with_indicators = scanner.calculate_lee_method_indicators(test_data)
                
                # Test pattern detection
                result = scanner.detect_lee_method_pattern(test_data_with_indicators)
                
                print("✅ Lee Method pattern detection: FUNCTIONAL")
                print(f"   Pattern found: {result.get('pattern_found', False) if result else False}")
                self.results['lee_method_scanner'] = 'PASSED'
            else:
                print("❌ Lee Method pattern detection method: MISSING")
                self.results['lee_method_scanner'] = 'FAILED'
                
        except Exception as e:
            print(f"❌ Lee Method scanner test error: {e}")
            self.results['lee_method_scanner'] = 'ERROR'
            
    def test_port_configuration(self):
        """Test that port configuration is correct"""
        print("\n🔌 Testing Port Configuration Fix...")
        try:
            # Check settings port
            port = settings.PORT
            if port == 8001:
                print("✅ Port configuration: FIXED")
                print(f"   Configured port: {port}")
                self.results['port_configuration'] = 'PASSED'
            else:
                print(f"❌ Port configuration: INCORRECT (expected 8001, got {port})")
                self.results['port_configuration'] = 'FAILED'
                
        except Exception as e:
            print(f"❌ Port configuration test error: {e}")
            self.results['port_configuration'] = 'ERROR'
            
    async def test_grok_fallbacks(self):
        """Test that Grok integration fallbacks are working"""
        print("\n🤖 Testing Grok Integration Fallbacks...")
        try:
            # Use the AI engine from orchestrator if available
            if self.orchestrator and 'ai' in self.orchestrator.engines:
                ai_engine = self.orchestrator.engines['ai']

                # Check validation mode capability
                validation_mode = getattr(ai_engine, 'validation_mode', False)
                print(f"✅ Validation mode available: {validation_mode}")

                # Check fallback mechanisms exist (they should be attributes)
                has_grok_client = hasattr(ai_engine, '_grok_client')
                has_openai_client = hasattr(ai_engine, '_openai_client')
                has_fallback_method = hasattr(ai_engine, '_call_grok_api')

                if has_grok_client and has_openai_client and has_fallback_method:
                    print("✅ Grok fallback architecture: IMPLEMENTED")
                    print("   Primary: Grok AI")
                    print("   Fallback: OpenAI")
                    print("   Final: Static response")
                    self.results['grok_fallbacks'] = 'PASSED'
                else:
                    print("⚠️ Grok fallback architecture: PARTIAL")
                    print(f"   Grok client: {has_grok_client}")
                    print(f"   OpenAI client: {has_openai_client}")
                    print(f"   Fallback method: {has_fallback_method}")
                    self.results['grok_fallbacks'] = 'PARTIAL'
            else:
                print("❌ AI engine not available through orchestrator")
                self.results['grok_fallbacks'] = 'FAILED'

        except Exception as e:
            print(f"❌ Grok fallbacks test error: {e}")
            self.results['grok_fallbacks'] = 'ERROR'
            
    def generate_summary(self):
        """Generate test summary"""
        print("\n" + "=" * 60)
        print("📋 FIXES VALIDATION SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result == 'PASSED')
        partial_tests = sum(1 for result in self.results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.results.values() if result in ['FAILED', 'ERROR'])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"⚠️ Partial: {partial_tests}")
        print(f"❌ Failed: {failed_tests}")
        
        success_rate = (passed_tests + partial_tests) / total_tests * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT: Fixes are working well!")
        elif success_rate >= 75:
            print("👍 GOOD: Most fixes are working")
        else:
            print("⚠️ NEEDS ATTENTION: Some fixes need more work")
            
        print("\nDetailed Results:")
        for test_name, result in self.results.items():
            status_icon = "✅" if result == 'PASSED' else "⚠️" if result == 'PARTIAL' else "❌"
            print(f"  {status_icon} {test_name}: {result}")

async def main():
    """Main entry point"""
    validator = FixesValidator()
    await validator.validate_all_fixes()

if __name__ == "__main__":
    asyncio.run(main())
