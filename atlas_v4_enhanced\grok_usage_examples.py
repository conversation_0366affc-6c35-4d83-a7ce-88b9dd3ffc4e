"""
A.T.L.A.S. v5.0 Grok Integration Usage Examples
Comprehensive examples demonstrating Grok API integration across all components
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import A.T.L.A.S. components
try:
    from atlas_orchestrator import AtlasOrchestrator
    from atlas_ai_core import AtlasAIEngine
    from atlas_grok_integration import (
        AtlasGrokIntegrationEngine, GrokRequest, GrokTaskType, GrokCapability
    )
    from models import ImageType, MarketType, MarketRegion
    IMPORTS_AVAILABLE = True
except ImportError as e:
    logger.error(f"Import failed: {e}")
    IMPORTS_AVAILABLE = False

class GrokUsageExamples:
    """Comprehensive examples of Grok integration usage"""
    
    def __init__(self):
        self.orchestrator = None
        self.ai_engine = None
        self.grok_engine = None
        
    async def initialize(self):
        """Initialize all components"""
        try:
            logger.info("Initializing A.T.L.A.S. components...")
            
            # Initialize orchestrator
            self.orchestrator = AtlasOrchestrator()
            await self.orchestrator.initialize()
            
            # Get AI engine
            self.ai_engine = self.orchestrator.engines.get('ai')
            
            # Initialize Grok engine directly
            self.grok_engine = AtlasGrokIntegrationEngine()
            await self.grok_engine.initialize()
            
            logger.info("✓ All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            return False

    async def example_1_enhanced_causal_analysis(self):
        """Example 1: Enhanced Causal Analysis with Grok Reasoning"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 1: Enhanced Causal Analysis")
        logger.info("="*50)
        
        try:
            # Define intervention scenario
            symbol = "AAPL"
            intervention = {
                "sentiment": 0.2,  # 20% positive sentiment increase
                "volume": 0.15,    # 15% volume increase
                "news_impact": 0.1 # 10% positive news impact
            }
            
            logger.info(f"Analyzing causal impact for {symbol}")
            logger.info(f"Intervention: {intervention}")
            
            # Get enhanced prediction using Grok
            if self.ai_engine and hasattr(self.ai_engine, 'get_enhanced_prediction'):
                result = await self.ai_engine.get_enhanced_prediction(
                    symbol=symbol,
                    market_data={'intervention': intervention},
                    prediction_type="causal_analysis"
                )
                
                # Display results
                logger.info("\nResults:")
                logger.info(f"Grok Enhanced: {result.get('grok_enhanced', False)}")
                
                if result.get('grok_enhanced'):
                    logger.info(f"Combined Confidence: {result.get('combined_confidence', 0):.2f}")
                    logger.info(f"Reasoning Chain: {len(result.get('reasoning_chain', []))} steps")
                    
                    # Show reasoning chain
                    for i, step in enumerate(result.get('reasoning_chain', [])[:3]):
                        logger.info(f"  Step {i+1}: {step}")
                    
                    # Show improvement metrics
                    metrics = result.get('improvement_metrics', {})
                    logger.info(f"Confidence Improvement: {metrics.get('confidence_improvement', 0):.3f}")
                else:
                    logger.info("Using fallback analysis (Grok not available)")
                    logger.info(f"Base Confidence: {result.get('confidence', 0):.2f}")
                
                return result
            else:
                logger.warning("AI engine not available for enhanced predictions")
                return None
                
        except Exception as e:
            logger.error(f"Enhanced causal analysis failed: {e}")
            return None

    async def example_2_market_psychology_enhancement(self):
        """Example 2: Market Psychology Analysis with Grok"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 2: Market Psychology Enhancement")
        logger.info("="*50)
        
        try:
            symbol = "TSLA"
            market_data = {
                "price": 250.0,
                "volume": 45000000,
                "change_percent": 3.5,
                "volatility": 0.35,
                "options_flow": "bullish"
            }
            
            logger.info(f"Analyzing market psychology for {symbol}")
            logger.info(f"Market Data: {market_data}")
            
            # Get enhanced market psychology
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'theory_of_mind_engine') and 
                self.ai_engine.theory_of_mind_engine):
                
                result = await self.ai_engine.theory_of_mind_engine.analyze_market_psychology(
                    symbol=symbol,
                    market_data=market_data
                )
                
                # Display results
                logger.info("\nResults:")
                logger.info(f"Dominant Emotion: {result.dominant_emotion.value}")
                logger.info(f"Fear/Greed Index: {result.fear_greed_index:.2f}")
                logger.info(f"Confidence: {result.confidence:.2f}")
                
                if hasattr(result, 'grok_enhanced') and result.grok_enhanced:
                    logger.info("✓ Enhanced with Grok insights")
                    if result.grok_insights:
                        logger.info(f"Grok Insights: {result.grok_insights[:200]}...")
                else:
                    logger.info("Using base psychology analysis")
                
                return result
            else:
                logger.warning("Theory of Mind engine not available")
                return None
                
        except Exception as e:
            logger.error(f"Market psychology enhancement failed: {e}")
            return None

    async def example_3_image_analysis_enhancement(self):
        """Example 3: Enhanced Chart Pattern Recognition"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 3: Enhanced Image Analysis")
        logger.info("="*50)
        
        try:
            # Simulate chart image data (in real usage, load from file)
            logger.info("Simulating chart image analysis...")
            
            # Create mock image data
            mock_image_data = b"mock_chart_image_data"
            image_id = f"AAPL_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'image_analyzer') and 
                self.ai_engine.image_analyzer):
                
                result = await self.ai_engine.image_analyzer.analyze_image(
                    image_data=mock_image_data,
                    image_id=image_id,
                    image_type=ImageType.PRICE_CHART
                )
                
                # Display results
                logger.info("\nResults:")
                logger.info(f"Image ID: {result.image_id}")
                logger.info(f"Detected Patterns: {[p.value for p in result.detected_patterns]}")
                logger.info(f"Trend Direction: {result.trend_direction.value}")
                logger.info(f"Confidence: {result.confidence:.2f}")
                
                if hasattr(result, 'grok_enhanced') and result.grok_enhanced:
                    logger.info("✓ Enhanced with Grok vision analysis")
                    if result.grok_insights:
                        logger.info(f"Grok Vision Insights: {result.grok_insights[:200]}...")
                else:
                    logger.info("Using base image analysis")
                
                return result
            else:
                logger.warning("Image analyzer not available")
                return None
                
        except Exception as e:
            logger.error(f"Image analysis enhancement failed: {e}")
            return None

    async def example_4_ml_model_optimization(self):
        """Example 4: ML Model Optimization with Grok"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 4: ML Model Optimization")
        logger.info("="*50)
        
        try:
            # Example model code to optimize
            model_code = '''
import torch
import torch.nn as nn

class SimplePredictor(nn.Module):
    def __init__(self, input_size=10, hidden_size=50, output_size=1):
        super(SimplePredictor, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.fc2(x)
        return x
'''
            
            logger.info("Optimizing ML model with Grok...")
            
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'ml_analytics') and 
                self.ai_engine.ml_analytics and
                hasattr(self.ai_engine.ml_analytics, 'optimize_ml_model_with_grok')):
                
                result = await self.ai_engine.ml_analytics.optimize_ml_model_with_grok(
                    model_name="simple_predictor",
                    model_code=model_code,
                    optimization_target="performance_and_accuracy"
                )
                
                # Display results
                logger.info("\nResults:")
                logger.info(f"Optimization Success: {result.get('success', False)}")
                
                if result.get('success'):
                    logger.info(f"Confidence: {result.get('confidence', 0):.2f}")
                    logger.info(f"Processing Time: {result.get('processing_time', 0):.2f}s")
                    
                    recommendations = result.get('recommendations', [])
                    logger.info(f"Recommendations: {len(recommendations)} found")
                    for i, rec in enumerate(recommendations[:3]):
                        logger.info(f"  {i+1}. {rec}")
                else:
                    logger.warning(f"Optimization failed: {result.get('error', 'Unknown error')}")
                
                return result
            else:
                logger.warning("ML analytics with Grok optimization not available")
                return None
                
        except Exception as e:
            logger.error(f"ML model optimization failed: {e}")
            return None

    async def example_5_real_time_sentiment(self):
        """Example 5: Real-time Market Sentiment with Grok"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 5: Real-time Market Sentiment")
        logger.info("="*50)
        
        try:
            symbols = ["BTC-USD", "ETH-USD"]
            
            logger.info(f"Getting real-time sentiment for: {symbols}")
            
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'global_markets_engine') and 
                self.ai_engine.global_markets_engine):
                
                # Get enhanced market sentiment
                for symbol in symbols:
                    result = await self.ai_engine.global_markets_engine.get_enhanced_market_sentiment(
                        symbol=symbol,
                        market_type=MarketType.CRYPTOCURRENCY,
                        region=MarketRegion.NORTH_AMERICA
                    )
                    
                    logger.info(f"\n{symbol} Results:")
                    logger.info(f"Grok Enhanced: {result.get('grok_enhanced', False)}")
                    
                    if result.get('grok_enhanced'):
                        enhanced_sentiment = result.get('enhanced_sentiment', {})
                        logger.info(f"Confidence: {enhanced_sentiment.get('confidence', 0):.2f}")
                        
                        sentiment_analysis = enhanced_sentiment.get('sentiment_analysis', '')
                        if sentiment_analysis:
                            logger.info(f"Analysis: {sentiment_analysis[:150]}...")
                    else:
                        logger.info("Using base market data")
                        base_data = result.get('base_data', {})
                        logger.info(f"Price: ${base_data.get('current_price', 0):,.2f}")
                
                return True
            else:
                logger.warning("Global markets engine not available")
                return None
                
        except Exception as e:
            logger.error(f"Real-time sentiment analysis failed: {e}")
            return None

    async def example_6_privacy_and_ethics_monitoring(self):
        """Example 6: Privacy and Ethics Compliance Monitoring"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 6: Privacy and Ethics Monitoring")
        logger.info("="*50)
        
        try:
            # Test privacy compliance
            logger.info("Testing GDPR compliance monitoring...")
            
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'privacy_learning_engine') and 
                self.ai_engine.privacy_learning_engine and
                hasattr(self.ai_engine.privacy_learning_engine, 'audit_grok_data_usage')):
                
                test_data = {
                    'task_type': 'market_analysis',
                    'prompt': 'Analyze market trends for AAPL stock',
                    'context': {'symbol': 'AAPL', 'market_type': 'equity'}
                }
                
                audit_result = await self.ai_engine.privacy_learning_engine.audit_grok_data_usage(
                    grok_request_data=test_data,
                    user_consent=True
                )
                
                logger.info("Privacy Audit Results:")
                logger.info(f"GDPR Compliant: {audit_result.get('gdpr_compliant', False)}")
                logger.info(f"Risk Level: {audit_result.get('risk_level', 'UNKNOWN')}")
                logger.info(f"Data Elements: {audit_result.get('data_elements', [])}")
            
            # Test bias auditing
            logger.info("\nTesting bias auditing...")
            
            if (self.ai_engine and 
                hasattr(self.ai_engine, 'ethical_ai_engine') and 
                self.ai_engine.ethical_ai_engine and
                hasattr(self.ai_engine.ethical_ai_engine, 'audit_grok_output_bias')):
                
                test_response = "Based on market analysis, AAPL shows strong fundamentals with institutional support."
                test_context = {'task_type': 'market_analysis', 'symbol': 'AAPL'}
                
                bias_audit = await self.ai_engine.ethical_ai_engine.audit_grok_output_bias(
                    grok_response=test_response,
                    request_context=test_context
                )
                
                logger.info("Bias Audit Results:")
                logger.info(f"Bias Level: {bias_audit.get('bias_level', 'UNKNOWN')}")
                logger.info(f"Overall Score: {bias_audit.get('overall_bias_score', 0):.3f}")
                
                recommendations = bias_audit.get('recommendations', [])
                if recommendations:
                    logger.info("Recommendations:")
                    for rec in recommendations[:2]:
                        logger.info(f"  - {rec}")
            
            return True
            
        except Exception as e:
            logger.error(f"Privacy and ethics monitoring failed: {e}")
            return None

    async def example_7_comprehensive_status_monitoring(self):
        """Example 7: Comprehensive System Status with Grok Integration"""
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 7: System Status Monitoring")
        logger.info("="*50)
        
        try:
            if self.orchestrator:
                # Get comprehensive system status
                status = await self.orchestrator.get_comprehensive_system_status()
                
                logger.info("System Status:")
                logger.info(f"Orchestrator: {status.get('orchestrator_status', 'UNKNOWN')}")
                logger.info(f"Total Engines: {status.get('total_engines', 0)}")
                logger.info(f"Active Engines: {status.get('active_engines', 0)}")
                logger.info(f"System Ready: {status.get('system_ready', False)}")
                
                # Grok integration status
                grok_status = status.get('grok_integration', {})
                logger.info(f"\nGrok Integration:")
                logger.info(f"Available: {grok_status.get('grok_available', False)}")
                
                if grok_status.get('grok_available'):
                    engines_with_grok = grok_status.get('engines_with_grok', [])
                    logger.info(f"Enhanced Engines: {engines_with_grok}")
                    
                    perf_metrics = grok_status.get('performance_metrics', {})
                    logger.info(f"Total Enhancements: {perf_metrics.get('total_enhancements', 0)}")
                    logger.info(f"Success Rate: {perf_metrics.get('successful_enhancements', 0)}")
                    logger.info(f"Avg Improvement: {perf_metrics.get('average_improvement', 0):.3f}")
                
                return status
            else:
                logger.warning("Orchestrator not available")
                return None
                
        except Exception as e:
            logger.error(f"Status monitoring failed: {e}")
            return None

    async def run_all_examples(self):
        """Run all usage examples"""
        logger.info("🚀 Starting A.T.L.A.S. Grok Integration Examples")
        logger.info("="*60)
        
        if not IMPORTS_AVAILABLE:
            logger.error("Required imports not available - cannot run examples")
            return False
        
        # Initialize components
        if not await self.initialize():
            logger.error("Failed to initialize components")
            return False
        
        # Run all examples
        examples = [
            self.example_1_enhanced_causal_analysis,
            self.example_2_market_psychology_enhancement,
            self.example_3_image_analysis_enhancement,
            self.example_4_ml_model_optimization,
            self.example_5_real_time_sentiment,
            self.example_6_privacy_and_ethics_monitoring,
            self.example_7_comprehensive_status_monitoring
        ]
        
        results = []
        for i, example in enumerate(examples, 1):
            try:
                logger.info(f"\n🔄 Running Example {i}...")
                result = await example()
                results.append(result is not None)
                
                if result is not None:
                    logger.info(f"✅ Example {i} completed successfully")
                else:
                    logger.warning(f"⚠️ Example {i} completed with warnings")
                    
            except Exception as e:
                logger.error(f"❌ Example {i} failed: {e}")
                results.append(False)
        
        # Summary
        successful = sum(results)
        total = len(results)
        
        logger.info("\n" + "="*60)
        logger.info("📊 EXAMPLES SUMMARY")
        logger.info("="*60)
        logger.info(f"Total Examples: {total}")
        logger.info(f"Successful: {successful}")
        logger.info(f"Success Rate: {successful/total*100:.1f}%")
        
        if successful == total:
            logger.info("🎉 All examples completed successfully!")
        elif successful > total * 0.7:
            logger.info("✅ Most examples completed successfully")
        else:
            logger.warning("⚠️ Some examples failed - check logs for details")
        
        return successful >= total * 0.7

async def main():
    """Main function to run all examples"""
    examples = GrokUsageExamples()
    success = await examples.run_all_examples()
    
    if success:
        logger.info("\n🎯 Grok integration examples completed successfully!")
        logger.info("The A.T.L.A.S. v5.0 system is ready for enhanced AI trading!")
    else:
        logger.error("\n❌ Some examples failed - please check the logs")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
