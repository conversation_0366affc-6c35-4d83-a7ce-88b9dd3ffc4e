#!/usr/bin/env python3
"""
Comprehensive A.T.L.A.S. Scanner System Validation
Tests all scanner components including pattern detection accuracy, alert delivery speed, and system performance under load
"""

import asyncio
import json
import time
import traceback
from datetime import datetime
import concurrent.futures

async def test_complete_scanner_system():
    """Comprehensive scanner system validation"""
    try:
        print('🔍 TESTING COMPLETE A.T.L.A.S. SCANNER SYSTEM...')
        print('=' * 60)
        
        # Test imports and initialization
        print('📦 Testing system imports and initialization...')
        from atlas_realtime_scanner import AtlasRealtimeScanner
        from atlas_lee_method import LeeMethodScanner
        from atlas_market_core import AtlasMarketEngine
        from atlas_alert_manager import AtlasAlertManager
        from sp500_symbols import get_sp500_symbols, get_high_volume_symbols
        from config import get_api_config
        print('✅ All imports successful')
        
        # Initialize complete system
        print('🚀 Initializing complete scanner system...')
        
        # Initialize market engine
        market_engine = AtlasMarketEngine()
        await market_engine.initialize()
        print('✅ Market engine initialized')
        
        # Initialize Lee Method scanner
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        lee_scanner = LeeMethodScanner(fmp_api_key, market_engine)
        await lee_scanner.initialize()
        print('✅ Lee Method scanner initialized')
        
        # Initialize real-time scanner
        realtime_scanner = AtlasRealtimeScanner()
        print('✅ Real-time scanner initialized')
        
        # Initialize alert manager
        alert_manager = AtlasAlertManager()
        print('✅ Alert manager initialized')
        
        # Test system configuration
        print('⚙️ Testing system configuration...')
        config = realtime_scanner.config
        print(f'   Scan Interval: {config.scan_interval}s (Target: 1-5s)')
        print(f'   Priority Scan Interval: {config.priority_scan_interval}s (Target: 1-2s)')
        print(f'   Max Concurrent: {config.max_concurrent_scans}')
        print(f'   Min Confidence: {config.min_confidence}')
        print(f'   Market Hours Only: {config.market_hours_only}')
        
        # Validate configuration targets
        config_valid = (
            1 <= config.scan_interval <= 5 and
            1 <= config.priority_scan_interval <= 2 and
            config.max_concurrent_scans >= 4 and
            config.min_confidence >= 0.6
        )
        print(f'✅ Configuration meets ultra-responsive targets: {config_valid}')
        
        # Test symbol management
        print('📊 Testing symbol management...')
        sp500_symbols = get_sp500_symbols()
        high_volume_symbols = get_high_volume_symbols()
        print(f'   S&P 500 Symbols: {len(sp500_symbols)}')
        print(f'   High Volume Symbols: {len(high_volume_symbols)}')
        
        if len(sp500_symbols) >= 300:  # Should have most S&P 500 symbols
            print('✅ Symbol coverage adequate')
        else:
            print('⚠️  Symbol coverage may be insufficient')
        
        # Test Lee Method pattern detection accuracy
        print('🎯 Testing Lee Method pattern detection accuracy...')
        
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'SPY', 'QQQ', 'IWM', 'NVDA', 'META', 'AMZN']
        detection_results = []
        
        detection_start = time.time()
        
        for symbol in test_symbols:
            try:
                start_time = time.time()
                signal = await lee_scanner.scan_symbol(symbol)
                detection_time = (time.time() - start_time) * 1000  # ms
                
                result = {
                    'symbol': symbol,
                    'pattern_found': signal is not None,
                    'detection_time_ms': detection_time,
                    'success': True
                }
                
                if signal:
                    result.update({
                        'consecutive_bars': signal.consecutive_bars,
                        'confidence': signal.confidence,
                        'signal_strength': signal.signal_strength.value
                    })
                
                detection_results.append(result)
                print(f'   {symbol}: {"✅ Pattern" if signal else "⚪ No pattern"} ({detection_time:.1f}ms)')
                
            except Exception as e:
                detection_results.append({
                    'symbol': symbol,
                    'success': False,
                    'error': str(e),
                    'detection_time_ms': 0
                })
                print(f'   {symbol}: ❌ Error - {str(e)[:50]}')
        
        total_detection_time = time.time() - detection_start
        avg_detection_time = (total_detection_time / len(test_symbols)) * 1000
        
        successful_detections = sum(1 for r in detection_results if r['success'])
        patterns_found = sum(1 for r in detection_results if r.get('pattern_found', False))
        
        print(f'✅ Pattern detection summary:')
        print(f'   Successful scans: {successful_detections}/{len(test_symbols)}')
        print(f'   Patterns found: {patterns_found}')
        print(f'   Average detection time: {avg_detection_time:.1f}ms')
        print(f'   Total scan time: {total_detection_time:.2f}s')
        
        # Test concurrent scanning performance
        print('🚀 Testing concurrent scanning performance...')
        
        concurrent_symbols = test_symbols[:5]  # Test with 5 symbols
        concurrent_start = time.time()
        
        # Run concurrent scans
        tasks = [lee_scanner.scan_symbol(symbol) for symbol in concurrent_symbols]
        concurrent_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        concurrent_time = time.time() - concurrent_start
        
        successful_concurrent = sum(1 for r in concurrent_results if not isinstance(r, Exception))
        print(f'✅ Concurrent scanning:')
        print(f'   Symbols scanned: {len(concurrent_symbols)}')
        print(f'   Successful: {successful_concurrent}')
        print(f'   Total time: {concurrent_time:.2f}s')
        print(f'   Avg time per symbol: {(concurrent_time/len(concurrent_symbols)*1000):.1f}ms')
        
        # Test alert delivery performance
        print('🚨 Testing alert delivery performance...')
        
        # Mock WebSocket connections
        class MockWebSocket:
            def __init__(self, name):
                self.name = name
                self.messages = []
                self.send_times = []
                
            async def send_text(self, message):
                self.messages.append(json.loads(message))
                self.send_times.append(time.time())
        
        # Create multiple mock connections
        mock_connections = [MockWebSocket(f"client_{i}") for i in range(10)]
        for ws in mock_connections:
            alert_manager.add_websocket_connection(ws)
        
        # Test alert generation and delivery
        alert_start = time.time()
        
        pattern_result = {
            'symbol': 'TEST_ALERT',
            'pattern_found': True,
            'consecutive_bars': 5,
            'decline_percent': -3.2,
            'confidence': 0.88,
            'current_price': 155.75,
            'signal_strength': 'STRONG',
            'recommendation': {
                'action': 'long_entry',
                'message': 'Lee Method: 5 consecutive declining bars detected',
                'confidence': 0.88
            }
        }
        
        alert = await alert_manager.generate_lee_method_alert(
            symbol='TEST_ALERT',
            pattern_result=pattern_result,
            market_data={}
        )
        
        alert_delivery_time = (time.time() - alert_start) * 1000
        
        # Verify all connections received the alert
        alerts_received = sum(1 for ws in mock_connections if ws.messages)
        
        print(f'✅ Alert delivery performance:')
        print(f'   Alert generation time: {alert_delivery_time:.2f}ms')
        print(f'   Connections: {len(mock_connections)}')
        print(f'   Alerts delivered: {alerts_received}')
        print(f'   Delivery success rate: {(alerts_received/len(mock_connections)*100):.1f}%')
        
        # Test system load performance
        print('⚡ Testing system performance under load...')
        
        load_test_symbols = ['SPY', 'QQQ', 'IWM', 'DIA', 'VTI']
        load_iterations = 3
        
        load_start = time.time()
        
        for iteration in range(load_iterations):
            iteration_start = time.time()
            
            # Simulate rapid scanning
            scan_tasks = [lee_scanner.scan_symbol(symbol) for symbol in load_test_symbols]
            await asyncio.gather(*scan_tasks, return_exceptions=True)
            
            iteration_time = time.time() - iteration_start
            print(f'   Load test iteration {iteration + 1}: {iteration_time:.2f}s')
        
        total_load_time = time.time() - load_start
        avg_load_time = total_load_time / load_iterations
        
        print(f'✅ Load test summary:')
        print(f'   Total iterations: {load_iterations}')
        print(f'   Symbols per iteration: {len(load_test_symbols)}')
        print(f'   Average iteration time: {avg_load_time:.2f}s')
        print(f'   Scans per second: {(len(load_test_symbols)/avg_load_time):.1f}')
        
        # Performance benchmarks
        print('📊 Performance benchmark validation...')
        
        benchmarks = {
            'Pattern Detection Speed': avg_detection_time <= 1000,  # <1s per symbol
            'Alert Delivery Speed': alert_delivery_time <= 2000,   # <2s total
            'Concurrent Processing': concurrent_time <= 5,          # <5s for 5 symbols
            'System Load Handling': avg_load_time <= 10,           # <10s per iteration
            'Configuration Compliance': config_valid,
            'Symbol Coverage': len(sp500_symbols) >= 300,
            'Detection Success Rate': (successful_detections/len(test_symbols)) >= 0.8
        }
        
        passed_benchmarks = sum(1 for passed in benchmarks.values() if passed)
        total_benchmarks = len(benchmarks)
        
        print(f'📋 Benchmark Results:')
        for benchmark, passed in benchmarks.items():
            status = '✅' if passed else '❌'
            print(f'   {status} {benchmark}: {"PASS" if passed else "FAIL"}')
        
        # Final system validation
        overall_success = passed_benchmarks == total_benchmarks
        
        print('\n🔧 COMPLETE SCANNER SYSTEM TEST RESULTS:')
        print(f'   Benchmarks Passed: {passed_benchmarks}/{total_benchmarks}')
        print(f'   Success Rate: {(passed_benchmarks/total_benchmarks*100):.1f}%')
        print(f'   Overall Status: {"✅ PASS" if overall_success else "❌ FAIL"}')
        
        if overall_success:
            print('\n🎯 SYSTEM VALIDATION COMPLETE - ALL TESTS PASSED')
            print('✅ Lee Method pattern detection: OPERATIONAL')
            print('✅ Ultra-responsive scanning: CONFIRMED')
            print('✅ Alert delivery system: FUNCTIONAL')
            print('✅ Performance targets: MET')
            print('✅ System ready for live trading operations')
        else:
            print('\n⚠️  SYSTEM VALIDATION INCOMPLETE - SOME TESTS FAILED')
            print('   Review failed benchmarks and optimize accordingly')
        
        return overall_success
        
    except Exception as e:
        print(f'❌ COMPLETE SCANNER SYSTEM TEST FAILED: {e}')
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_scanner_system())
    exit(0 if success else 1)
