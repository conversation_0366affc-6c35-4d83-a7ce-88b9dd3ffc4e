#!/usr/bin/env python3
"""
Complete test of progress tracking with WebSocket connection and chat request
"""
import asyncio
import json
import websockets
import aiohttp
import time

async def test_complete_progress_flow():
    session_id = f'test_complete_{int(time.time())}'
    ws_uri = f'ws://localhost:8001/ws/{session_id}'
    chat_url = 'http://localhost:8001/api/v1/chat'
    
    print(f'Testing complete progress flow with session: {session_id}')
    
    try:
        # Connect to WebSocket first
        async with websockets.connect(ws_uri) as websocket:
            print('WebSocket connected successfully')
            
            # Wait for connection confirmation
            message = await websocket.recv()
            data = json.loads(message)
            print(f'Connection confirmed: {data.get("type", "unknown")}')
            
            # Now make a chat request with the same session ID
            chat_payload = {
                'message': 'Analyze AAPL stock for me',
                'session_id': session_id
            }
            
            print('Making chat request...')
            
            # Use aiohttp to make the chat request
            async with aiohttp.ClientSession() as session:
                async with session.post(chat_url, json=chat_payload) as response:
                    chat_response = await response.json()
                    print(f'Chat request sent, status: {response.status}')
            
            # Listen for progress updates
            print('Listening for progress updates...')
            timeout = time.time() + 15
            progress_updates_received = 0
            
            while time.time() < timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    
                    if data.get('type') == 'progress_update':
                        progress_updates_received += 1
                        print(f'Progress Update #{progress_updates_received}:')
                        print(f'   Stage: {data.get("data", {}).get("stage", "N/A")}')
                        print(f'   Percentage: {data.get("data", {}).get("percentage", "N/A")}%')
                        print(f'   Message: {data.get("data", {}).get("message", "N/A")}')
                        print(f'   Status: {data.get("data", {}).get("status", "N/A")}')
                        print('---')
                    else:
                        print(f'Other message: {data.get("type", "unknown")}')
                        
                except asyncio.TimeoutError:
                    continue
                    
            print(f'Test completed. Received {progress_updates_received} progress updates.')
            
            if progress_updates_received > 0:
                print('SUCCESS: Progress tracking is working!')
            else:
                print('ISSUE: No progress updates received')
            
    except Exception as e:
        print(f'Test failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_complete_progress_flow())
