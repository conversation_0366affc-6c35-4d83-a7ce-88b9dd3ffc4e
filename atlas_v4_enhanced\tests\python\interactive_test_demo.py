#!/usr/bin/env python3
"""
Interactive A.T.L.A.S. Testing Demo
Demonstrates the chatbot functionality with real-time testing
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class InteractiveATLASDemo:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session_id = f"demo_session_{int(time.time())}"
        
    async def send_query(self, message: str):
        """Send a query to A.T.L.A.S. and display the response"""
        print(f"\n{'='*80}")
        print(f"🧪 TESTING QUERY: {message}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "message": message,
                    "session_id": self.session_id,
                    "user_id": "demo_user"
                }
                
                print("📡 Sending request to A.T.L.A.S...")
                
                async with session.post(
                    f"{self.base_url}/api/v1/chat",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        print(f"✅ SUCCESS! Response received in {response_time:.2f} seconds")
                        print(f"🎯 Confidence: {data.get('confidence', 0):.2f}")
                        print(f"📝 Type: {data.get('type', 'unknown')}")
                        print(f"🤖 Context: {data.get('context', {})}")
                        print(f"\n💬 A.T.L.A.S. RESPONSE:")
                        print("-" * 80)
                        print(data.get('response', 'No response'))
                        print("-" * 80)
                        
                        return True
                    else:
                        print(f"❌ ERROR: HTTP {response.status}")
                        return False
                        
        except Exception as e:
            response_time = time.time() - start_time
            print(f"❌ EXCEPTION: {str(e)} (after {response_time:.2f}s)")
            return False

async def main():
    demo = InteractiveATLASDemo()
    
    print("🚀 A.T.L.A.S. INTERACTIVE TESTING DEMO")
    print("🌟 Advanced Trading & Learning Analysis System")
    print("📅 " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("\n" + "="*80)
    print("This demo will test various A.T.L.A.S. capabilities:")
    print("• Market Analysis")
    print("• Trading Recommendations") 
    print("• Technical Analysis")
    print("• AI Integration")
    print("• Educational Content")
    print("="*80)
    
    # Demo test queries
    demo_queries = [
        {
            "query": "Hello A.T.L.A.S., please introduce yourself",
            "description": "System Introduction Test"
        },
        {
            "query": "Analyze AAPL stock for me",
            "description": "Market Analysis Test"
        },
        {
            "query": "Should I buy TSLA right now?",
            "description": "Trading Strategy Test"
        },
        {
            "query": "What's the RSI for NVDA?",
            "description": "Technical Analysis Test"
        },
        {
            "query": "Use advanced AI to analyze current market conditions",
            "description": "AI Integration Test"
        },
        {
            "query": "What is a stock and how does trading work?",
            "description": "Educational Content Test"
        }
    ]
    
    print(f"\n🎬 Starting demo with {len(demo_queries)} test queries...")
    
    for i, test in enumerate(demo_queries, 1):
        print(f"\n🔄 TEST {i}/{len(demo_queries)}: {test['description']}")
        
        success = await demo.send_query(test['query'])
        
        if success:
            print("✅ Test completed successfully!")
        else:
            print("❌ Test failed!")
        
        # Pause between tests
        if i < len(demo_queries):
            print(f"\n⏳ Waiting 3 seconds before next test...")
            await asyncio.sleep(3)
    
    print(f"\n🎉 DEMO COMPLETED!")
    print("="*80)
    print("💡 You can now test the system through:")
    print("   • Web Interface: http://localhost:8001")
    print("   • API Endpoint: http://localhost:8001/api/v1/chat")
    print("   • This demo script with custom queries")
    print("="*80)

if __name__ == "__main__":
    print("🚀 Starting A.T.L.A.S. Interactive Demo...")
    asyncio.run(main())
