"""
A.T.L.A.S. Enhanced Security Audit & Remediation
Production-ready security audit with automated fixes and compliance validation
"""

import os
import re
import json
import logging
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class SecurityIssue:
    """Represents a security issue with remediation info"""
    severity: str
    category: str
    title: str
    description: str
    file_path: str = None
    line_number: int = None
    remediation: str = None
    auto_fixable: bool = False


class EnhancedSecurityAuditor:
    """Enhanced security auditor with remediation capabilities"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.issues = []
        self.files_scanned = 0
        
        # Critical security patterns
        self.critical_patterns = {
            'hardcoded_api_keys': [
                r'(?i)(api_key|apikey)\s*=\s*["\'][A-Za-z0-9]{20,}["\']',
                r'(?i)(secret_key|secretkey)\s*=\s*["\'][A-Za-z0-9]{20,}["\']',
                r'(?i)(access_token|token)\s*=\s*["\'][A-Za-z0-9]{20,}["\']'
            ],
            'hardcoded_passwords': [
                r'(?i)password\s*=\s*["\'][^"\']{3,}["\']',
                r'(?i)passwd\s*=\s*["\'][^"\']{3,}["\']'
            ],
            'sql_injection_risks': [
                r'(?i)execute\s*\(\s*["\'].*%.*["\']',
                r'(?i)cursor\.execute\s*\(\s*["\'].*\+.*["\']'
            ],
            'command_injection_risks': [
                r'(?i)os\.system\s*\(',
                r'(?i)subprocess\.call\s*\([^)]*shell\s*=\s*True'
            ]
        }
        
        # Production readiness checks
        self.production_checks = {
            'debug_mode': r'(?i)debug\s*=\s*True',
            'test_credentials': r'(?i)(test|demo|example).*(?:password|key|secret)',
            'insecure_protocols': r'(?i)http://(?!localhost|127\.0\.0\.1)',
            'missing_ssl': r'(?i)ssl_verify\s*=\s*False'
        }
    
    def add_issue(self, severity: str, category: str, title: str, description: str,
                  file_path: str = None, line_number: int = None, remediation: str = None,
                  auto_fixable: bool = False):
        """Add a security issue"""
        issue = SecurityIssue(
            severity=severity,
            category=category,
            title=title,
            description=description,
            file_path=file_path,
            line_number=line_number,
            remediation=remediation,
            auto_fixable=auto_fixable
        )
        self.issues.append(issue)
    
    async def scan_critical_security_issues(self):
        """Scan for critical security issues"""
        logger.info("Scanning for critical security issues...")
        
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                self.files_scanned += 1
                
                # Check critical patterns
                for category, patterns in self.critical_patterns.items():
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.MULTILINE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = lines[line_num - 1] if line_num <= len(lines) else ""
                            
                            self.add_issue(
                                severity='CRITICAL',
                                category=category,
                                title=f"Critical Security Issue: {category.replace('_', ' ').title()}",
                                description=f"Found {category.replace('_', ' ')} in {file_path.name}",
                                file_path=str(file_path),
                                line_number=line_num,
                                remediation=self._get_remediation(category),
                                auto_fixable=category in ['debug_mode', 'test_credentials']
                            )
            
            except Exception as e:
                logger.warning(f"Failed to scan {file_path}: {e}")
    
    async def check_production_readiness(self):
        """Check production readiness"""
        logger.info("Checking production readiness...")
        
        config_files = list(self.project_root.rglob("*.py")) + \
                      list(self.project_root.rglob("*.env*")) + \
                      list(self.project_root.rglob("config.*"))
        
        for file_path in config_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Check production readiness patterns
                for check, pattern in self.production_checks.items():
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        severity = 'HIGH' if check in ['debug_mode', 'missing_ssl'] else 'MEDIUM'
                        
                        self.add_issue(
                            severity=severity,
                            category='production_readiness',
                            title=f"Production Issue: {check.replace('_', ' ').title()}",
                            description=f"Production readiness issue in {file_path.name}",
                            file_path=str(file_path),
                            line_number=line_num,
                            remediation=self._get_production_remediation(check),
                            auto_fixable=check == 'debug_mode'
                        )
            
            except Exception as e:
                logger.warning(f"Failed to check {file_path}: {e}")
    
    async def check_trading_security(self):
        """Check trading-specific security measures"""
        logger.info("Checking trading security...")
        
        # Check for paper trading enforcement
        trading_files = list(self.project_root.rglob("*trading*.py")) + \
                       list(self.project_root.rglob("*trade*.py"))
        
        paper_trading_found = False
        live_trading_safeguards = False
        
        for file_path in trading_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Check for paper trading
                if re.search(r'(?i)paper.*trading.*=.*true', content):
                    paper_trading_found = True
                
                # Check for live trading safeguards
                if re.search(r'(?i)paper.*trading.*mode', content) and \
                   re.search(r'(?i)risk.*management', content):
                    live_trading_safeguards = True
                
                # Check for dangerous trading patterns
                if re.search(r'(?i)market.*order.*unlimited', content):
                    self.add_issue(
                        severity='CRITICAL',
                        category='trading_security',
                        title='Unlimited Market Orders Detected',
                        description='Unlimited market orders pose significant financial risk',
                        file_path=str(file_path),
                        remediation='Implement position size limits and risk controls'
                    )
            
            except Exception as e:
                logger.warning(f"Failed to check trading file {file_path}: {e}")
        
        # Overall trading security assessment
        if not paper_trading_found:
            self.add_issue(
                severity='CRITICAL',
                category='trading_security',
                title='Paper Trading Not Enforced',
                description='No paper trading enforcement found in trading modules',
                remediation='Implement mandatory paper trading mode for safety',
                auto_fixable=False
            )
        
        if not live_trading_safeguards:
            self.add_issue(
                severity='HIGH',
                category='trading_security',
                title='Insufficient Trading Safeguards',
                description='Trading safeguards and risk management not properly implemented',
                remediation='Implement comprehensive risk management and position limits'
            )
    
    async def check_api_security(self):
        """Check API security measures"""
        logger.info("Checking API security...")
        
        api_files = list(self.project_root.rglob("*api*.py")) + \
                   list(self.project_root.rglob("*server*.py")) + \
                   list(self.project_root.rglob("*endpoint*.py"))
        
        for file_path in api_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Check for missing authentication
                if 'def ' in content and '@' not in content:
                    self.add_issue(
                        severity='HIGH',
                        category='api_security',
                        title='Missing API Authentication',
                        description='API endpoints may lack proper authentication',
                        file_path=str(file_path),
                        remediation='Implement authentication decorators for all API endpoints'
                    )
                
                # Check for CORS issues
                if re.search(r'(?i)cors.*origin.*\*', content):
                    self.add_issue(
                        severity='MEDIUM',
                        category='api_security',
                        title='Permissive CORS Configuration',
                        description='CORS allows all origins which may be insecure',
                        file_path=str(file_path),
                        remediation='Restrict CORS to specific trusted domains'
                    )
            
            except Exception as e:
                logger.warning(f"Failed to check API file {file_path}: {e}")
    
    def _get_remediation(self, category: str) -> str:
        """Get remediation advice for security category"""
        remediations = {
            'hardcoded_api_keys': 'Move API keys to environment variables or secure key management',
            'hardcoded_passwords': 'Use environment variables and secure password storage',
            'sql_injection_risks': 'Use parameterized queries or ORM with proper escaping',
            'command_injection_risks': 'Validate inputs and use safe subprocess alternatives'
        }
        return remediations.get(category, 'Review and fix security issue')
    
    def _get_production_remediation(self, check: str) -> str:
        """Get remediation for production readiness issues"""
        remediations = {
            'debug_mode': 'Set DEBUG=False in production configuration',
            'test_credentials': 'Replace test credentials with production values',
            'insecure_protocols': 'Use HTTPS instead of HTTP for all external communications',
            'missing_ssl': 'Enable SSL certificate verification'
        }
        return remediations.get(check, 'Fix production configuration issue')
    
    async def auto_fix_issues(self):
        """Automatically fix issues that can be safely remediated"""
        logger.info("Attempting to auto-fix security issues...")
        
        fixed_count = 0
        
        for issue in self.issues:
            if issue.auto_fixable and issue.file_path:
                try:
                    with open(issue.file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Auto-fix debug mode
                    if issue.category == 'debug_mode':
                        content = re.sub(r'(?i)debug\s*=\s*True', 'DEBUG = False', content)
                        fixed_count += 1
                    
                    # Write back fixed content
                    with open(issue.file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    logger.info(f"Auto-fixed: {issue.title} in {issue.file_path}")
                
                except Exception as e:
                    logger.warning(f"Failed to auto-fix {issue.title}: {e}")
        
        logger.info(f"Auto-fixed {fixed_count} security issues")
        return fixed_count
    
    def calculate_security_score(self) -> float:
        """Calculate overall security score"""
        if not self.issues:
            return 100.0
        
        # Weight penalties by severity
        penalty = 0
        for issue in self.issues:
            if issue.severity == 'CRITICAL':
                penalty += 25
            elif issue.severity == 'HIGH':
                penalty += 15
            elif issue.severity == 'MEDIUM':
                penalty += 5
            elif issue.severity == 'LOW':
                penalty += 2
        
        return max(0, 100 - penalty)
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate compliance report"""
        issues_by_severity = {}
        issues_by_category = {}
        
        for issue in self.issues:
            # Count by severity
            issues_by_severity[issue.severity] = issues_by_severity.get(issue.severity, 0) + 1
            
            # Count by category
            issues_by_category[issue.category] = issues_by_category.get(issue.category, 0) + 1
        
        security_score = self.calculate_security_score()
        
        # Determine compliance level
        if security_score >= 95:
            compliance_level = "EXCELLENT"
            deployment_ready = True
        elif security_score >= 85:
            compliance_level = "GOOD"
            deployment_ready = True
        elif security_score >= 70:
            compliance_level = "FAIR"
            deployment_ready = False
        else:
            compliance_level = "POOR"
            deployment_ready = False
        
        return {
            'timestamp': datetime.now().isoformat(),
            'files_scanned': self.files_scanned,
            'total_issues': len(self.issues),
            'security_score': security_score,
            'compliance_level': compliance_level,
            'deployment_ready': deployment_ready,
            'issues_by_severity': issues_by_severity,
            'issues_by_category': issues_by_category,
            'critical_issues': [asdict(issue) for issue in self.issues if issue.severity == 'CRITICAL'],
            'auto_fixable_issues': len([issue for issue in self.issues if issue.auto_fixable])
        }
    
    async def run_comprehensive_audit(self) -> Dict[str, Any]:
        """Run comprehensive security audit"""
        logger.info("Starting enhanced security audit...")
        start_time = datetime.now()
        
        # Run all security checks
        await self.scan_critical_security_issues()
        await self.check_production_readiness()
        await self.check_trading_security()
        await self.check_api_security()
        
        # Generate compliance report
        report = self.generate_compliance_report()
        
        audit_duration = datetime.now() - start_time
        report['audit_duration_seconds'] = audit_duration.total_seconds()
        
        logger.info(f"Enhanced security audit completed in {audit_duration.total_seconds():.2f} seconds")
        
        return report


async def main():
    """Run enhanced security audit"""
    print("="*80)
    print("A.T.L.A.S. ENHANCED SECURITY AUDIT & REMEDIATION")
    print("="*80)
    
    auditor = EnhancedSecurityAuditor()
    
    # Run comprehensive audit
    report = await auditor.run_comprehensive_audit()
    
    # Display results
    print(f"\n📊 SECURITY AUDIT RESULTS:")
    print(f"   Files Scanned: {report['files_scanned']}")
    print(f"   Total Issues: {report['total_issues']}")
    print(f"   Security Score: {report['security_score']:.1f}/100")
    print(f"   Compliance Level: {report['compliance_level']}")
    print(f"   Deployment Ready: {'✅ YES' if report['deployment_ready'] else '❌ NO'}")
    print(f"   Audit Duration: {report['audit_duration_seconds']:.2f} seconds")
    
    # Show issues by severity
    if report['issues_by_severity']:
        print(f"\n🔍 ISSUES BY SEVERITY:")
        severity_emojis = {'CRITICAL': '🔴', 'HIGH': '🟡', 'MEDIUM': '🟠', 'LOW': '🔵'}
        for severity, count in report['issues_by_severity'].items():
            emoji = severity_emojis.get(severity, '⚪')
            print(f"   {emoji} {severity}: {count}")
    
    # Show critical issues
    if report['critical_issues']:
        print(f"\n🚨 CRITICAL ISSUES (MUST FIX):")
        for i, issue in enumerate(report['critical_issues'][:5], 1):
            print(f"   {i}. {issue['title']}")
            print(f"      File: {issue['file_path']}")
            print(f"      Remediation: {issue['remediation']}")
    
    # Auto-fix if possible
    if report['auto_fixable_issues'] > 0:
        print(f"\n🔧 AUTO-FIXING {report['auto_fixable_issues']} ISSUES...")
        fixed_count = await auditor.auto_fix_issues()
        print(f"   ✅ Fixed {fixed_count} issues automatically")
    
    # Recommendations
    print(f"\n💡 SECURITY RECOMMENDATIONS:")
    if report['deployment_ready']:
        print("   ✅ System meets security requirements for production deployment")
        print("   🔄 Continue regular security monitoring and updates")
    else:
        print("   ❌ System NOT ready for production deployment")
        print("   🔧 Fix critical and high-severity issues before deployment")
        print("   🔍 Conduct additional security review")
    
    print("   🛡️ Implement continuous security monitoring")
    print("   📋 Regular security audits and penetration testing")
    print("   🔑 Secure key management and rotation")
    
    # Save report
    report_file = f"enhanced_security_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n📄 Detailed report saved to: {report_file}")
    
    return report['deployment_ready']


if __name__ == "__main__":
    deployment_ready = asyncio.run(main())
    exit(0 if deployment_ready else 1)
