#!/usr/bin/env python3
"""
Test script to verify WebSocket progress tracking functionality
"""
import asyncio
import json
import websockets
import time

async def test_progress_websocket():
    session_id = f'test_session_{int(time.time())}'
    uri = f'ws://localhost:8001/ws/{session_id}'
    
    print(f'Connecting to WebSocket: {uri}')
    
    try:
        async with websockets.connect(uri) as websocket:
            print('WebSocket connected successfully')
            
            # Wait for connection confirmation
            message = await websocket.recv()
            data = json.loads(message)
            print(f'Connection message: {data.get("type", "unknown")}')
            
            # Send a test message to trigger progress
            test_message = {
                'type': 'get_operations'
            }
            await websocket.send(json.dumps(test_message))
            print('Sent test message')
            
            # Listen for progress updates for 10 seconds
            timeout = time.time() + 10
            while time.time() < timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    
                    if data.get('type') == 'progress_update':
                        print('Progress Update Received:')
                        print(f'   Stage: {data.get("data", {}).get("stage", "N/A")}')
                        print(f'   Percentage: {data.get("data", {}).get("percentage", "N/A")}%')
                        print(f'   Message: {data.get("data", {}).get("message", "N/A")}')
                        print(f'   Status: {data.get("data", {}).get("status", "N/A")}')
                    else:
                        print(f'Other message: {data.get("type", "unknown")}')
                        
                except asyncio.TimeoutError:
                    continue
                    
            print('WebSocket test completed')
            
    except Exception as e:
        print(f'WebSocket test failed: {e}')

if __name__ == '__main__':
    asyncio.run(test_progress_websocket())
