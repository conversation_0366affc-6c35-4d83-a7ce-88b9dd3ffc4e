# A.T.L.A.S Trading System Environment Variables Template
# SECURITY: This template contains NO sensitive data - users must provide their own API keys

# =============================================================================
# IMPORTANT: REPLACE ALL PLACEHOLDER VALUES WITH YOUR ACTUAL API KEYS
# =============================================================================

# Alpaca Trading API (Paper Trading - Safe for Testing)
# Get your keys from: https://app.alpaca.markets/paper/dashboard/overview
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=YOUR_ALPACA_API_KEY_HERE
ALPACA_SECRET_KEY=YOUR_ALPACA_SECRET_KEY_HERE

# Financial Modeling Prep API (Market Data)
# Get your key from: https://financialmodelingprep.com/developer/docs
FMP_API_KEY=YOUR_FMP_API_KEY_HERE

# Grok AI (X.AI) API - Primary AI Provider
# Get your key from: https://console.x.ai/
GROK_API_KEY=YOUR_GROK_API_KEY_HERE
GROK_BASE_URL=https://api.x.ai/v1
GROK_MODEL=grok-3-latest
GROK_TEMPERATURE=0.2

# OpenAI API (Fallback AI Provider)
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE

# Predicto API (Optional - Advanced Analytics)
# Get your key from: https://predic.to/
PREDICTO_API_KEY=YOUR_PREDICTO_API_KEY_HERE

# =============================================================================
# SYSTEM CONFIGURATION (Safe to keep as-is)
# =============================================================================

# Trading Configuration
PAPER_TRADING=true
DEBUG=false
ENVIRONMENT=production

# Validation Mode (set to true for testing without all API keys)
VALIDATION_MODE=false

# Enhanced Security Settings
LOG_LEVEL=INFO
PORT=8002

# Database Configuration
DATABASE_URL=sqlite:///atlas.db

# Performance Settings
API_TIMEOUT=30
CACHE_TTL=300
MAX_SCAN_RESULTS=50

# ML Model Configuration
ML_MODELS_ENABLED=true
ML_PREDICTION_CONFIDENCE_THRESHOLD=0.7

# Options Trading Configuration
OPTIONS_TRADING_ENABLED=true
OPTIONS_MAX_EXPIRY_DAYS=45
OPTIONS_MIN_VOLUME=100
OPTIONS_MAX_SPREAD_PERCENT=5.0

# Risk Management
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10

# Proactive Assistant Configuration
PROACTIVE_ASSISTANT_ENABLED=true
MORNING_BRIEFING_TIME=09:00
ALERT_COOLDOWN_MINUTES=15
MIN_SIGNAL_STRENGTH=4

# Enhanced Memory System
ENHANCED_MEMORY_ENABLED=true
CONVERSATION_MEMORY_LIMIT=1000
MEMORY_IMPORTANCE_THRESHOLD=0.5

# Web Search APIs (Optional)
# Get your keys from: https://console.developers.google.com/
GOOGLE_SEARCH_API_KEY=YOUR_GOOGLE_SEARCH_API_KEY_HERE
GOOGLE_SEARCH_ENGINE_ID=YOUR_GOOGLE_SEARCH_ENGINE_ID_HERE

# Web Search Configuration
WEB_SEARCH_ENABLED=true
WEB_SEARCH_USE_GROK=true
WEB_SEARCH_CACHE_TTL_MINUTES=30
WEB_SEARCH_RATE_LIMIT_SECONDS=1.0
WEB_SEARCH_MAX_RESULTS_DEFAULT=10
WEB_SEARCH_CONFIDENCE_THRESHOLD=0.7
WEB_SEARCH_MIN_ARTICLES_THRESHOLD=3

# Component-specific web search settings
WEB_SEARCH_AI_ENABLED=true
WEB_SEARCH_MARKET_ENABLED=true
WEB_SEARCH_RISK_ENABLED=true
WEB_SEARCH_TRADING_ENABLED=true
WEB_SEARCH_EDUCATION_ENABLED=true
WEB_SEARCH_LEE_METHOD_ENABLED=true
WEB_SEARCH_PORTFOLIO_ENABLED=true
WEB_SEARCH_NEWS_ENABLED=true
