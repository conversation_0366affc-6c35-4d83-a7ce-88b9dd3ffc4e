#!/bin/bash
# A.T.L.A.S. Production Backup Script

set -e

BACKUP_DIR="/opt/atlas/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="atlas_backup_$DATE"

echo "Starting A.T.L.A.S. backup: $BACKUP_NAME"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup database
echo "Backing up database..."
pg_dump atlas_production > "$BACKUP_DIR/$BACKUP_NAME/database.sql"

# Backup application data
echo "Backing up application data..."
tar -czf "$BACKUP_DIR/$BACKUP_NAME/app_data.tar.gz" /opt/atlas/data

# Backup configuration
echo "Backing up configuration..."
cp -r /opt/atlas/config "$BACKUP_DIR/$BACKUP_NAME/"

# Backup logs (last 7 days)
echo "Backing up recent logs..."
find /var/log/atlas -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/$BACKUP_NAME/" \;

# Create backup manifest
echo "Creating backup manifest..."
cat > "$BACKUP_DIR/$BACKUP_NAME/manifest.txt" << EOF
A.T.L.A.S. Backup Manifest
Backup Name: $BACKUP_NAME
Date: $(date)
Database: Included
App Data: Included
Configuration: Included
Logs: Last 7 days
EOF

# Compress backup
echo "Compressing backup..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

# Clean old backups (keep last 30 days)
find "$BACKUP_DIR" -name "atlas_backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
