# A.T.L.A.S. Project Brief for Devin AI

## 🎯 **What A.T.L.A.S. Is**

**A.T.L.A.S. (Advanced Trading & Learning Analytics System)** is a sophisticated **conversational AI trading assistant** - think ChatGPT but specifically designed for stock trading and financial analysis. Users can ask natural language questions like "Should I buy AAPL?" or "Help me make $100 today" and get professional-grade trading recommendations.

## 🏗️ **System Architecture Overview**

```
User asks: "Analyze AAPL for me"
    ↓
Web Interface (HTML/JavaScript)
    ↓
FastAPI Server (26 endpoints)
    ↓
AI Orchestrator (coordinates multiple AI agents)
    ↓
Specialized Engines:
├── Trading Engine (buy/sell decisions)
├── Risk Engine (position sizing, stop losses)
├── Market Engine (real-time data, news)
├── Options Engine (Black-Scholes pricing)
├── ML Engine (LSTM predictions, sentiment)
├── Lee Method Scanner (advanced momentum pattern detection)
└── Education Engine (explains concepts)
    ↓
Response in "6-Point Stock Market God Format":
1. Why This Trade (plain English)
2. Win/Loss Probabilities (exact %)
3. Money In/Out (dollar amounts)
4. Smart Stop Plans (protection)
5. Market Context (current conditions)
6. Confidence Score (0-100%)
```

## 📁 **Codebase Structure**

```
atlas_v4_enhanced/
├── 1_main_chat_engine/          # Core conversational AI
│   ├── atlas_server.py          # Main FastAPI server
│   ├── atlas_orchestrator.py    # Coordinates all engines
│   ├── atlas_ai_engine.py       # Core AI processing
│   └── atlas_interface.html     # Web UI
├── 2_trading_logic/             # Trading algorithms
│   ├── atlas_trading_engine.py  # Buy/sell logic
│   ├── atlas_risk_engine.py     # Risk management
│   ├── atlas_options_engine.py  # Options strategies
│   └── atlas_lee_method_integration.py # Lee Method pattern detection
├── 3_market_news_data/          # Market data & analysis
│   ├── atlas_market_engine.py   # Real-time quotes
│   ├── atlas_sentiment_analyzer.py # News sentiment
│   └── atlas_ml_predictor.py    # LSTM predictions
├── 4_helper_tools/              # Configuration & utilities
│   ├── config.py               # Settings management
│   ├── models.py               # Data structures
│   └── atlas_database_manager.py # Database operations
├── databases/                   # 7 SQLite databases
├── lee_method_scanner.py        # Advanced pattern detection
└── requirements.txt             # 95 Python dependencies
```

## 📈 **The Lee Method - Core Pattern Detection System**

**A.T.L.A.S. uses the Lee Method** (NOT TTM Squeeze) for advanced momentum pattern detection. The Lee Method identifies high-probability momentum shifts using **three specific criteria**:

### **Lee Method Criteria:**
1. **Histogram Pattern Detection**
   - Requirement: Three (or more) histogram bars that decrease, followed by an increase
   - The increase doesn't have to be positive, just higher than the previous bar

2. **Momentum Confirmation**
   - Requirement: Current momentum must be greater than the prior momentum bar
   - Confirms the strength and validity of the pattern

3. **Multi-Timeframe Analysis**
   - Requirement: Weekly and daily chart alignment
   - Weekly trend must align with daily trend for high-confidence signals

**This replaces traditional TTM Squeeze patterns** with more precise entry timing and better momentum confirmation.

## 🎯 **What We're Trying to Achieve**

### **Primary Goal: Get A.T.L.A.S. Fully Operational**

The system is **90% complete** but has one critical blocker preventing full functionality:

### **🚨 CRITICAL ISSUE: API Key Authentication**

**Problem**: The Lee Method scanner (real-time market analysis) is failing with 401 Unauthorized errors when accessing Financial Modeling Prep API, despite having an API key configured.

**Evidence from logs**:
```
2025-07-06 10:02:24,971 - lee_method_scanner - ERROR - Error fetching data for AAPL: 401 Client Error: Unauthorized for url: https://financialmodelingprep.com/api/v3/historical-price-full/AAPL?apikey=demo&timeseries=100
```

**Note**: The log shows "apikey=demo" but the .env file has `FMP_API_KEY=********************************` - this suggests the API key isn't being loaded properly from the environment.

### **🎯 Immediate Objectives**

1. **Fix Market Data Access**
   - **Root Cause**: API key not being loaded from .env file (logs show "demo" instead of actual key)
   - **Debug Steps**: Check why FMP_API_KEY environment variable isn't being read
   - **Validate API Key**: Test the key `********************************` directly with FMP API
   - **Fix Environment Loading**: Ensure config.py properly loads FMP_API_KEY from .env
   - **Test Lee Method Scanner**: Verify pattern detection works after API fix

2. **System Validation**
   - Start the main server: `python 1_main_chat_engine/atlas_server.py`
   - Test conversational AI through web interface at `http://localhost:8080`
   - Validate all 25+ trading features work properly

3. **Performance Optimization**
   - Ensure <2 second response times
   - Verify all databases are accessible
   - Test error handling and fallback systems

## 🔧 **Technical Context for Devin**

### **Current Status**
- ✅ **Codebase**: Complete and well-organized
- ✅ **Dependencies**: All 95 packages properly specified
- ✅ **Configuration**: Environment variables properly set up
- ✅ **Architecture**: Non-blocking FastAPI server with background initialization
- ❌ **Market Data**: Blocked by API authentication issue

### **Key Technologies**
- **Backend**: Python 3.9+, FastAPI, SQLAlchemy
- **AI/ML**: OpenAI GPT-4, DistilBERT, LSTM neural networks
- **Market Data**: Financial Modeling Prep API, Alpaca Trading API
- **Frontend**: HTML/JavaScript web interface
- **Databases**: 7 SQLite databases for different functions

### **Environment Setup**
```bash
# Current .env configuration (API keys already set)
ALPACA_API_KEY=PKI0KNC8HXZURYRA4OMC  # ✅ Working
OPENAI_API_KEY=sk-proj-9_2OZncF...    # ✅ Working
FMP_API_KEY=********************************  # ❌ Getting 401 errors - needs validation
```

### **Expected User Experience**
Once working, users should be able to:

1. **Ask Natural Questions**:
   ```
   User: "I want to make $50 today, what are my options?"
   A.T.L.A.S: "🎯 GOAL SET: $50 profit target
   
   I recommend 2-3 smaller trades rather than one big swing:
   
   📊 AAPL Analysis:
   1️⃣ WHY: Lee Method pattern detected - 3 declining histogram bars followed by uptick with momentum confirmation
   2️⃣ PROBABILITIES: 73% win, 27% loss
   3️⃣ MONEY: $1,500 position, $25 target profit
   4️⃣ STOPS: $147.50 stop loss, trail at $178
   5️⃣ CONTEXT: Tech sector rotating higher, low VIX, weekly/daily trend alignment
   6️⃣ CONFIDENCE: 87% - High conviction Lee Method setup"
   ```

2. **Get Real-time Analysis**: Live market scanning, pattern detection
3. **Educational Support**: Learn trading concepts through conversation
4. **Risk Management**: Automatic position sizing and stop-loss calculations

## 🎯 **Success Criteria**

### **Phase 1: Basic Functionality**
- [ ] Server starts without errors
- [ ] Web interface loads at localhost:8080
- [ ] Can ask questions and get AI responses
- [ ] Market data retrieval works (fix API key issue)

### **Phase 2: Full Feature Validation**
- [ ] Lee Method scanner detects momentum patterns (3 declining + 1 uptick histogram)
- [ ] Multi-timeframe analysis works (weekly/daily trend alignment)
- [ ] Options pricing calculations work
- [ ] Portfolio optimization functions
- [ ] All 26 API endpoints respond correctly

### **Phase 3: Performance Optimization**
- [ ] Response times under 2 seconds
- [ ] System handles multiple concurrent users
- [ ] Error handling works gracefully

## 💡 **Key Points for Devin**

1. **This is NOT a simple trading bot** - it's a sophisticated conversational AI system that happens to focus on trading
2. **The architecture is already excellent** - we just need to fix the API connectivity issue
3. **All the hard work is done** - the AI engines, trading logic, and user interface are complete
4. **Focus on the API key issue first** - this is the main blocker preventing full functionality
5. **The system is designed for production** - comprehensive error handling, monitoring, and scalability built-in

## 🚀 **Next Steps for Devin**

1. **Investigate the FMP API key issue** - The key `********************************` exists in .env but logs show "demo" being used - debug environment variable loading
2. **Test system startup** - run the server and identify any immediate issues
3. **Validate core functionality** - ensure the conversational AI responds properly
4. **Fix any blocking issues** - address problems preventing full operation
5. **Performance testing** - verify the system meets response time requirements

The goal is to transform A.T.L.A.S. from a 90% complete system into a fully operational AI trading assistant that users can interact with naturally to get professional-grade trading analysis and recommendations.

## 📋 **Additional Context**

### **System Features (25+ Capabilities)**
- **6-Point Stock Market God Format** - Professional trading recommendations
- **Lee Method Pattern Detection** - Advanced momentum analysis with 3 specific criteria:
  - Histogram Pattern: 3+ declining bars followed by uptick
  - Momentum Confirmation: Current momentum > previous momentum
  - Multi-timeframe Analysis: Weekly/daily trend alignment
- **Options Trading Engine** - Black-Scholes pricing with full Greeks
- **Portfolio Optimization** - Markowitz optimization with scipy integration
- **Risk Management** - VaR calculations and dynamic position sizing
- **Sentiment Analysis** - DistilBERT-based news and social media analysis
- **ML Predictions** - LSTM neural networks for price forecasting
- **Educational RAG System** - 5 trading books integrated for learning
- **Paper Trading Engine** - Alpaca integration for safe practice trading
- **Proactive Assistant** - Morning briefings and opportunity alerts
- **Multi-Database Architecture** - 7 specialized SQLite databases
- **Real-time Scanner** - Continuous market monitoring with Lee Method detection

### **Startup Commands**
```bash
# Main chatbot server (recommended)
cd atlas_v4_enhanced/1_main_chat_engine
python atlas_server.py

# Production mode
python start_production.py

# Lee Method scanner only
cd atlas_v4_enhanced
python start_lee_method_system.py

# Desktop application
cd atlas_v4_enhanced/desktop_app
python launch_desktop_app.py
```

### **API Endpoints Available**
- `GET /` - Main web interface
- `POST /api/v1/chat` - Main conversational AI endpoint
- `GET /api/v1/health` - System health check
- `GET /api/v1/scan` - Lee Method market scanner
- `GET /api/v1/quote/{symbol}` - Real-time market quotes
- `POST /api/v1/education` - Educational queries from trading books
- `GET /api/v1/portfolio` - Portfolio analysis and optimization
- Plus 19 more specialized endpoints for trading, risk, and analysis

### **Known Working Components**
- ✅ FastAPI server with non-blocking startup
- ✅ Conversational AI with OpenAI GPT-4 integration
- ✅ Database management (7 SQLite databases)
- ✅ Error handling and logging systems
- ✅ Web interface (HTML/JavaScript)
- ✅ Configuration management
- ✅ Paper trading integration with Alpaca

### **Known Issues to Address**
- ❌ **Financial Modeling Prep API authentication (401 errors)**
  - **Issue**: Logs show "apikey=demo" but .env has `FMP_API_KEY=********************************`
  - **Root Cause**: Environment variable not being loaded properly
  - **Debug**: Check `lee_method_scanner.py` and `config.py` for FMP_API_KEY usage
  - **Test API Key**: Validate key works: `curl "https://financialmodelingprep.com/api/v3/profile/AAPL?apikey=********************************"`
- ⚠️ Missing `minimal_atlas_startup.py` (removed during cleanup)
- ⚠️ Missing `atlas_robust_server_manager.py` (fallback works)

### **Performance Targets**
- **Response Time**: <2 seconds for chat responses
- **Startup Time**: <3 seconds to first request (non-blocking)
- **Uptime**: 99.9% availability target
- **Pass Rate**: 90%+ system reliability (already achieved)

### **File Locations**
- **Main Server**: `atlas_v4_enhanced/1_main_chat_engine/atlas_server.py`
- **Configuration**: `atlas_v4_enhanced/4_helper_tools/config.py`
- **Environment**: `atlas_v4_enhanced/.env`
- **Dependencies**: `atlas_v4_enhanced/requirements.txt`
- **Logs**: `atlas_v4_enhanced/lee_method_scanner.log` (shows API errors)
- **Documentation**: `atlas_v4_enhanced/README.md` (comprehensive)

This system represents a significant achievement in AI-powered trading technology, combining institutional-grade analysis with user-friendly conversational interaction. The main goal is to resolve the API connectivity issue and validate full system functionality.
