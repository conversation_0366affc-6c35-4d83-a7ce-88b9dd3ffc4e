"""
A.T.L.A.S. Comprehensive Trading Plan Engine
Generates actionable trading plans with specific dollar targets and timeframes
Integrates with Lee Method, TTM Squeeze, and multi-agent analysis systems
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
import pytz
import numpy as np

# A.T.L.A.S. Core Imports
from models import (
    ComprehensiveTradingPlan, TradingPlanTarget, TradingOpportunity,
    PortfolioIntegration, TradingPlanScenario, TradingPlanMonitoring,
    TradingPlanAlert, TradingPlanExecution, AlertPriority, SignalStrength
)
from atlas_lee_method import LeeMethodScanner, LeeMethodSignal
from atlas_market_core import AtlasMarketEngine
from atlas_risk_core import AtlasRiskEngine
from atlas_alert_manager import AtlasAlertManager
from sp500_symbols import get_sp500_symbols, get_high_volume_symbols
from config import get_api_config, settings

logger = logging.getLogger(__name__)

class AtlasTradingPlanEngine:
    """
    Comprehensive Trading Plan Generator Engine
    Creates actionable trading strategies with specific dollar targets and timeframes
    """
    
    def __init__(self):
        self.status = "initializing"
        self.lee_method_scanner = None
        self.market_engine = None
        self.risk_engine = None
        self.alert_manager = None
        
        # Trading plan configuration
        self.central_tz = pytz.timezone('US/Central')
        self.default_risk_per_trade = 0.02  # 2% risk per trade
        self.max_portfolio_risk = 0.10  # 10% max portfolio risk
        self.min_confidence_threshold = 70.0  # Minimum confidence for recommendations
        
        # Plan generation metrics
        self.plans_generated = 0
        self.active_plans = {}
        self.plan_success_rate = 0.0
        
        logger.info("[TRADING_PLAN_ENGINE] Comprehensive Trading Plan Engine initialized")
    
    async def initialize(self):
        """Initialize the trading plan engine with all required components"""
        try:
            logger.info("[TRADING_PLAN_ENGINE] Initializing trading plan engine...")
            
            # Initialize core components
            self.lee_method_scanner = LeeMethodScanner()
            await self.lee_method_scanner.initialize()
            
            self.market_engine = AtlasMarketEngine()
            await self.market_engine.initialize()
            
            self.risk_engine = AtlasRiskEngine()
            await self.risk_engine.initialize()
            
            self.alert_manager = AtlasAlertManager()
            await self.alert_manager.initialize()
            
            self.status = "active"
            logger.info("[TRADING_PLAN_ENGINE] Trading plan engine initialization completed")
            
        except Exception as e:
            logger.error(f"[TRADING_PLAN_ENGINE] Initialization failed: {e}")
            self.status = "failed"
            raise
    
    async def generate_comprehensive_trading_plan(
        self,
        target_profit: float,
        timeframe_days: int,
        starting_capital: float,
        risk_tolerance: str = "moderate"
    ) -> ComprehensiveTradingPlan:
        """
        Generate a comprehensive trading plan for specific profit target and timeframe
        
        Args:
            target_profit: Target profit amount in dollars
            timeframe_days: Target timeframe in days
            starting_capital: Starting capital amount
            risk_tolerance: Risk tolerance level (conservative, moderate, aggressive)
        
        Returns:
            ComprehensiveTradingPlan: Complete actionable trading plan
        """
        try:
            logger.info(f"[TRADING_PLAN_ENGINE] Generating plan: ${target_profit} profit in {timeframe_days} days")
            
            # Generate unique plan ID
            plan_id = f"ATLAS_PLAN_{uuid.uuid4().hex[:8].upper()}"
            
            # Create financial target
            target = await self._create_financial_target(
                target_profit, timeframe_days, starting_capital, risk_tolerance
            )
            
            # Scan for trading opportunities
            opportunities = await self._scan_trading_opportunities(target)
            
            # Analyze portfolio integration
            portfolio_integration = await self._analyze_portfolio_integration(opportunities, target)
            
            # Generate alternative scenarios
            scenarios = await self._generate_alternative_scenarios(target, opportunities)
            
            # Create monitoring framework
            monitoring = await self._create_monitoring_framework(opportunities, target)
            
            # Calculate plan metrics
            total_expected_return = sum(opp.max_profit_dollars for opp in opportunities)
            total_risk_amount = sum(opp.max_loss_dollars for opp in opportunities)
            plan_confidence = await self._calculate_plan_confidence(opportunities)
            
            # Create comprehensive trading plan
            trading_plan = ComprehensiveTradingPlan(
                plan_id=plan_id,
                plan_name=f"${target_profit:,.0f} Profit Plan - {timeframe_days} Days",
                target=target,
                opportunities=opportunities,
                portfolio_integration=portfolio_integration,
                scenarios=scenarios,
                monitoring=monitoring,
                total_expected_return=total_expected_return,
                total_risk_amount=total_risk_amount,
                plan_confidence=plan_confidence
            )
            
            # Store active plan
            self.active_plans[plan_id] = trading_plan
            self.plans_generated += 1
            
            logger.info(f"[TRADING_PLAN_ENGINE] Generated plan {plan_id} with {len(opportunities)} opportunities")
            return trading_plan
            
        except Exception as e:
            logger.error(f"[TRADING_PLAN_ENGINE] Plan generation failed: {e}")
            raise
    
    async def _create_financial_target(
        self, target_profit: float, timeframe_days: int, 
        starting_capital: float, risk_tolerance: str
    ) -> TradingPlanTarget:
        """Create financial target configuration"""
        
        # Map risk tolerance to percentage
        risk_tolerance_map = {
            "conservative": 5.0,
            "moderate": 10.0,
            "aggressive": 20.0
        }
        
        risk_percent = risk_tolerance_map.get(risk_tolerance, 10.0)
        max_drawdown = min(risk_percent * 1.5, 25.0)  # Cap at 25%
        
        return TradingPlanTarget(
            target_profit=target_profit,
            target_timeframe_days=timeframe_days,
            risk_tolerance_percent=risk_percent,
            starting_capital=starting_capital,
            max_drawdown_percent=max_drawdown
        )
    
    async def _scan_trading_opportunities(self, target: TradingPlanTarget) -> List[TradingOpportunity]:
        """Scan for high-probability trading opportunities using Lee Method and TTM Squeeze"""
        try:
            opportunities = []
            
            # Get symbols to scan (focus on high-volume, liquid stocks)
            scan_symbols = get_high_volume_symbols()[:50]  # Top 50 high-volume stocks
            
            # Perform Lee Method scan
            lee_signals = await self.lee_method_scanner.scan_multiple_symbols(scan_symbols)
            
            # Filter and rank signals by confidence
            high_confidence_signals = [
                signal for signal in lee_signals 
                if signal and signal.confidence >= self.min_confidence_threshold
            ]
            
            # Sort by confidence and select top opportunities
            high_confidence_signals.sort(key=lambda x: x.confidence, reverse=True)
            
            # Calculate number of opportunities needed based on target
            required_opportunities = min(
                max(3, int(target.target_profit / 1000)),  # At least 3, scale with target
                10  # Maximum 10 opportunities per plan
            )
            
            selected_signals = high_confidence_signals[:required_opportunities]
            
            # Convert signals to trading opportunities
            for signal in selected_signals:
                opportunity = await self._create_trading_opportunity(signal, target)
                if opportunity:
                    opportunities.append(opportunity)
            
            logger.info(f"[TRADING_PLAN_ENGINE] Created {len(opportunities)} trading opportunities")
            return opportunities
            
        except Exception as e:
            logger.error(f"[TRADING_PLAN_ENGINE] Opportunity scanning failed: {e}")
            return []
    
    async def _create_trading_opportunity(
        self, signal: LeeMethodSignal, target: TradingPlanTarget
    ) -> Optional[TradingOpportunity]:
        """Create a detailed trading opportunity from a Lee Method signal"""
        try:
            # Get current market data
            market_data = await self.market_engine.get_quote(signal.symbol)
            if not market_data:
                return None
            
            current_price = market_data.price
            
            # Calculate position sizing based on risk management
            risk_amount = target.starting_capital * (target.risk_tolerance_percent / 100) * self.default_risk_per_trade
            
            # Calculate stop loss (use Lee Method recommendation or 2% below entry)
            stop_loss_price = signal.stop_loss or (current_price * 0.98)
            risk_per_share = abs(current_price - stop_loss_price)
            
            if risk_per_share <= 0:
                return None
            
            position_size = int(risk_amount / risk_per_share)
            capital_allocation = position_size * current_price
            
            # Calculate target price (use Lee Method target or 6% above entry for 3:1 R/R)
            target_price = signal.target_price or (current_price * 1.06)
            
            # Calculate profit/loss metrics
            max_profit_dollars = position_size * (target_price - current_price)
            max_loss_dollars = position_size * risk_per_share
            max_profit_percent = ((target_price - current_price) / current_price) * 100
            max_loss_percent = (risk_per_share / current_price) * 100
            risk_reward_ratio = max_profit_dollars / max_loss_dollars if max_loss_dollars > 0 else 0
            
            # Determine market session and timing
            now_ct = datetime.now(self.central_tz)
            market_session = self._determine_market_session(now_ct)
            
            # Create trading opportunity
            opportunity = TradingOpportunity(
                symbol=signal.symbol,
                company_name=await self._get_company_name(signal.symbol),
                current_price=current_price,
                entry_price=current_price,
                exit_target_price=target_price,
                stop_loss_price=stop_loss_price,
                position_size_shares=position_size,
                capital_allocation=capital_allocation,
                trade_type="stock",
                primary_method="Lee Method TTM Squeeze",
                timeframe_analyzed=signal.timeframe,
                confidence_score=signal.confidence,
                supporting_indicators=signal.supporting_indicators,
                confluence_factors=[
                    f"TTM Squeeze: {signal.squeeze_status}",
                    f"Momentum: {signal.momentum_direction}",
                    f"Trend Alignment: {signal.trend_alignment}"
                ],
                max_profit_dollars=max_profit_dollars,
                max_profit_percent=max_profit_percent,
                max_loss_dollars=max_loss_dollars,
                max_loss_percent=max_loss_percent,
                risk_reward_ratio=risk_reward_ratio,
                success_probability=signal.confidence,
                recommended_entry_time=now_ct + timedelta(minutes=5),
                market_session=market_session,
                trade_duration_type="swing" if signal.timeframe == "daily" else "day",
                estimated_hold_days=3 if signal.timeframe == "daily" else 1
            )
            
            return opportunity
            
        except Exception as e:
            logger.error(f"[TRADING_PLAN_ENGINE] Opportunity creation failed for {signal.symbol}: {e}")
            return None

    async def _analyze_portfolio_integration(
        self, opportunities: List[TradingOpportunity], target: TradingPlanTarget
    ) -> PortfolioIntegration:
        """Analyze how opportunities integrate with overall portfolio strategy"""
        try:
            # Calculate total portfolio risk
            total_risk = sum(opp.max_loss_dollars for opp in opportunities)
            portfolio_risk_percent = (total_risk / target.starting_capital) * 100

            # Analyze sector diversification
            sector_allocation = {}
            for opp in opportunities:
                sector = await self._get_sector(opp.symbol)
                sector_allocation[sector] = sector_allocation.get(sector, 0) + opp.capital_allocation

            # Calculate diversification score (higher is better)
            num_sectors = len(sector_allocation)
            max_sector_weight = max(sector_allocation.values()) / sum(sector_allocation.values()) if sector_allocation else 1.0
            diversification_score = min(100, (num_sectors * 20) * (1 - max_sector_weight))

            # Analyze correlations (simplified - in practice would use historical correlation data)
            correlation_analysis = {}
            for i, opp1 in enumerate(opportunities):
                for j, opp2 in enumerate(opportunities[i+1:], i+1):
                    pair = f"{opp1.symbol}-{opp2.symbol}"
                    # Simplified correlation based on sector similarity
                    sector1 = await self._get_sector(opp1.symbol)
                    sector2 = await self._get_sector(opp2.symbol)
                    correlation_analysis[pair] = 0.7 if sector1 == sector2 else 0.3

            # Risk concentration analysis
            risk_concentration = {}
            for opp in opportunities:
                risk_concentration[opp.symbol] = (opp.max_loss_dollars / total_risk) * 100 if total_risk > 0 else 0

            # Generate recommendations
            recommendations = []
            if portfolio_risk_percent > target.risk_tolerance_percent:
                recommendations.append(f"Reduce position sizes - portfolio risk ({portfolio_risk_percent:.1f}%) exceeds tolerance")
            if diversification_score < 60:
                recommendations.append("Consider adding positions from different sectors for better diversification")
            if max(risk_concentration.values()) > 30:
                recommendations.append("Reduce concentration in highest-risk position")

            return PortfolioIntegration(
                total_portfolio_risk=portfolio_risk_percent,
                correlation_analysis=correlation_analysis,
                diversification_score=diversification_score,
                sector_allocation=sector_allocation,
                risk_concentration=risk_concentration,
                recommended_adjustments=recommendations
            )

        except Exception as e:
            logger.error(f"[TRADING_PLAN_ENGINE] Portfolio integration analysis failed: {e}")
            return PortfolioIntegration(
                total_portfolio_risk=0.0,
                correlation_analysis={},
                diversification_score=0.0,
                sector_allocation={},
                risk_concentration={},
                recommended_adjustments=["Analysis failed - manual review required"]
            )

    async def _generate_alternative_scenarios(
        self, target: TradingPlanTarget, opportunities: List[TradingOpportunity]
    ) -> List[TradingPlanScenario]:
        """Generate alternative scenarios for the trading plan"""
        scenarios = []

        # Optimistic scenario (80% success rate)
        optimistic_return = sum(opp.max_profit_dollars * 0.8 for opp in opportunities)
        scenarios.append(TradingPlanScenario(
            scenario_name="Optimistic",
            scenario_type="optimistic",
            modified_targets={"expected_return": optimistic_return, "success_rate": 80},
            probability=25.0,
            required_adjustments=["Monitor for early profit-taking opportunities", "Scale out positions gradually"]
        ))

        # Pessimistic scenario (40% success rate)
        pessimistic_return = sum(opp.max_profit_dollars * 0.4 - opp.max_loss_dollars * 0.6 for opp in opportunities)
        scenarios.append(TradingPlanScenario(
            scenario_name="Pessimistic",
            scenario_type="pessimistic",
            modified_targets={"expected_return": pessimistic_return, "success_rate": 40},
            probability=25.0,
            required_adjustments=["Tighten stop losses", "Reduce position sizes", "Consider defensive positions"]
        ))

        # Market volatility scenario
        scenarios.append(TradingPlanScenario(
            scenario_name="High Volatility",
            scenario_type="alternative",
            modified_targets={"volatility_adjustment": True, "wider_stops": True},
            probability=30.0,
            required_adjustments=["Widen stop losses by 50%", "Reduce position sizes by 25%", "Monitor VIX levels"]
        ))

        return scenarios

    async def _create_monitoring_framework(
        self, opportunities: List[TradingOpportunity], target: TradingPlanTarget
    ) -> TradingPlanMonitoring:
        """Create monitoring and adjustment framework"""

        # Daily checkpoints
        daily_checkpoints = [
            "Review overnight news and pre-market activity",
            "Check position P&L and adjust stops if needed",
            "Monitor volume and volatility patterns",
            "Assess market sentiment and sector rotation",
            "Review Lee Method signals for new opportunities"
        ]

        # Adjustment triggers
        adjustment_triggers = [
            {"condition": "portfolio_loss_exceeds", "threshold": target.max_drawdown_percent, "action": "reduce_positions"},
            {"condition": "individual_position_loss", "threshold": 5.0, "action": "review_stop_loss"},
            {"condition": "market_volatility_spike", "threshold": "VIX > 25", "action": "tighten_risk_management"},
            {"condition": "sector_rotation", "threshold": "sector_underperformance", "action": "consider_rebalancing"}
        ]

        # Performance metrics to track
        performance_metrics = {
            "total_return": 0.0,
            "win_rate": 0.0,
            "average_win": 0.0,
            "average_loss": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0
        }

        # Alert conditions
        alert_conditions = [
            {"condition": "stop_loss_hit", "priority": "high", "action": "immediate_review"},
            {"condition": "target_reached", "priority": "medium", "action": "consider_profit_taking"},
            {"condition": "unusual_volume", "priority": "medium", "action": "investigate_catalyst"},
            {"condition": "news_impact", "priority": "high", "action": "reassess_position"}
        ]

        # Review schedule
        review_schedule = {
            "daily": "9:00 AM CT - Pre-market review",
            "midday": "12:00 PM CT - Mid-session check",
            "close": "4:00 PM CT - End-of-day review",
            "weekly": "Sunday 6:00 PM CT - Weekly performance review"
        }

        return TradingPlanMonitoring(
            daily_checkpoints=daily_checkpoints,
            adjustment_triggers=adjustment_triggers,
            performance_metrics=performance_metrics,
            alert_conditions=alert_conditions,
            review_schedule=review_schedule
        )

    async def _calculate_plan_confidence(self, opportunities: List[TradingOpportunity]) -> float:
        """Calculate overall plan confidence score"""
        if not opportunities:
            return 0.0

        # Weight confidence by capital allocation
        total_capital = sum(opp.capital_allocation for opp in opportunities)
        weighted_confidence = sum(
            opp.confidence_score * (opp.capital_allocation / total_capital)
            for opp in opportunities
        ) if total_capital > 0 else 0.0

        # Apply diversification bonus (up to 10 points)
        num_opportunities = len(opportunities)
        diversification_bonus = min(10, num_opportunities * 2)

        # Apply risk management penalty if portfolio risk is too high
        risk_penalty = 0  # Would calculate based on portfolio integration

        final_confidence = min(100, weighted_confidence + diversification_bonus - risk_penalty)
        return final_confidence

    def _determine_market_session(self, timestamp: datetime) -> str:
        """Determine market session based on Central Time"""
        hour = timestamp.hour

        if 4 <= hour < 9:
            return "pre-market"
        elif 9 <= hour < 16:
            return "regular"
        elif 16 <= hour < 20:
            return "after-hours"
        else:
            return "closed"

    async def _get_company_name(self, symbol: str) -> str:
        """Get company name for symbol (simplified implementation)"""
        # In practice, this would query a financial data provider
        company_names = {
            "AAPL": "Apple Inc.",
            "MSFT": "Microsoft Corporation",
            "GOOGL": "Alphabet Inc.",
            "AMZN": "Amazon.com Inc.",
            "TSLA": "Tesla Inc.",
            "NVDA": "NVIDIA Corporation",
            "META": "Meta Platforms Inc."
        }
        return company_names.get(symbol, f"{symbol} Corporation")

    async def _get_sector(self, symbol: str) -> str:
        """Get sector for symbol (simplified implementation)"""
        # In practice, this would query a financial data provider
        sectors = {
            "AAPL": "Technology",
            "MSFT": "Technology",
            "GOOGL": "Technology",
            "AMZN": "Consumer Discretionary",
            "TSLA": "Consumer Discretionary",
            "NVDA": "Technology",
            "META": "Technology",
            "JPM": "Financial Services",
            "JNJ": "Healthcare",
            "PG": "Consumer Staples"
        }
        return sectors.get(symbol, "Technology")  # Default to Technology

    async def get_active_plan(self, plan_id: str) -> Optional[ComprehensiveTradingPlan]:
        """Retrieve an active trading plan by ID"""
        return self.active_plans.get(plan_id)

    async def update_plan_execution(self, plan_id: str, execution: TradingPlanExecution):
        """Update plan with execution details"""
        if plan_id in self.active_plans:
            # In practice, would store executions in database
            logger.info(f"[TRADING_PLAN_ENGINE] Updated execution for plan {plan_id}: {execution.symbol}")

    async def generate_plan_alert(self, plan_id: str, alert_type: str, message: str) -> TradingPlanAlert:
        """Generate alert for trading plan"""
        alert = TradingPlanAlert(
            alert_id=f"ALERT_{uuid.uuid4().hex[:8].upper()}",
            plan_id=plan_id,
            alert_type=alert_type,
            priority=AlertPriority.HIGH if alert_type in ["risk", "stop_loss"] else AlertPriority.MEDIUM,
            message=message,
            action_required=alert_type in ["risk", "stop_loss", "entry"],
            suggested_actions=[f"Review {alert_type} condition for plan {plan_id}"]
        )

        # Send alert through alert manager
        if self.alert_manager:
            await self.alert_manager.send_alert(alert)

        return alert
