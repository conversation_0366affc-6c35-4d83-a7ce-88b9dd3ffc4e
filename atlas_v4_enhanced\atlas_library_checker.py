
def check_library_availability():
    """Check availability of all external libraries"""
    libraries = {
        'news_insights': ['newspaper', 'feedparser', 'bs4', 'nltk', 'textblob'],
        'causal_reasoning': ['networkx', 'pgmpy', 'sklearn', 'scipy'],
        'video_processing': ['cv2', 'moviepy', 'imageio', 'PIL'],
        'image_analysis': ['PIL', 'cv2', 'skimage', 'matplotlib'],
        'alternative_data': ['tweepy', 'praw', 'selenium', 'scrapy'],
        'explainable_ai': ['shap', 'lime', 'eli5', 'interpret'],
        'quantum_optimization': ['qiskit', 'cirq', 'pennylane', 'dimod'],
        'global_markets': ['forex_python', 'cryptocompare', 'ccxt', 'pytz']
    }
    
    availability = {}
    for category, libs in libraries.items():
        available = []
        for lib in libs:
            try:
                __import__(lib)
                available.append(lib)
            except ImportError:
                pass
        availability[category] = {
            'available': available,
            'total': len(libs),
            'percentage': (len(available) / len(libs)) * 100
        }
    
    return availability

# Global availability checker
LIBRARY_AVAILABILITY = check_library_availability()
