"""
A.T.L.A.S Education - Consolidated Educational Engine
Combines Education Engine and Beginner Trading Mentor
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus
from atlas_web_search_service import web_search_service, SearchContext, SearchQuery

logger = logging.getLogger(__name__)


# ============================================================================
# EDUCATION ENGINE
# ============================================================================

class AtlasEducationEngine:
    """Consolidated educational engine with beginner trading mentorship"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.educational_content = {}
        self.beginner_lessons = {}
        self.user_progress = {}
        
        # Educational categories
        self.categories = [
            'basics', 'technical_analysis', 'fundamental_analysis',
            'risk_management', 'options', 'portfolio_management',
            'psychology', 'market_structure'
        ]

        # Web search service
        self.web_search_service = web_search_service
        self.web_search_enabled = settings.WEB_SEARCH_EDUCATION_ENABLED

        logger.info("[LIBRARY] Education Engine created - ChromaDB will load on demand")

    async def initialize(self):
        """Initialize education engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load educational content
            await self._load_educational_content()
            
            # Initialize beginner mentor
            await self._initialize_beginner_mentor()

            # Initialize web search service
            if self.web_search_enabled and self.web_search_service.is_available():
                await self.web_search_service.initialize()
                logger.info("[OK] Web search service initialized for education engine")
            else:
                logger.info("[INFO] Web search disabled or not available for education engine")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Education Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Education Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_educational_content(self):
        """Load basic educational content"""
        try:
            self.educational_content = {
                'basics': {
                    'what_is_stock': {
                        'title': 'What is a Stock?',
                        'content': '''Think of a stock as owning a tiny piece of a company! When you buy stock in Apple (AAPL), you literally become a part-owner of Apple Inc.

**How Stocks Make Money:**
1. **Price Appreciation**: If the company does well, more people want to buy the stock, driving the price up
2. **Dividends**: Some companies share their profits with stockholders through dividend payments

**Simple Example:**
- You buy 1 share of Apple for $175
- Apple releases a great new iPhone
- More people want Apple stock
- Your share is now worth $180
- You made $5 profit!

**Key Point**: Stocks represent real ownership in real companies. When companies succeed, stockholders can benefit from that success.''',
                        'difficulty': 'beginner',
                        'estimated_time': '5 minutes'
                    },
                    'what_is_trading': {
                        'title': 'What is Trading?',
                        'content': '''Trading is buying and selling stocks to make a profit from price movements.

**Types of Trading:**
1. **Day Trading**: Buy and sell within the same day
2. **Swing Trading**: Hold for days to weeks
3. **Position Trading**: Hold for months to years

**Key Concepts:**
- **Buy Low, Sell High**: The basic principle
- **Market Orders**: Buy/sell immediately at current price
- **Limit Orders**: Buy/sell only at your specified price
- **Stop Loss**: Automatic sell order to limit losses

**Remember**: Trading involves risk. Never invest more than you can afford to lose.''',
                        'difficulty': 'beginner',
                        'estimated_time': '7 minutes'
                    }
                },
                'risk_management': {
                    'position_sizing': {
                        'title': 'Position Sizing and the 2% Rule',
                        'content': '''The #1 rule in trading: **Never risk more than 2% of your account on any single trade.**

**Why the 2% Rule Works:**
- If you have $10,000, risk only $200 per trade
- Even if you lose 10 trades in a row, you still have $8,000 left
- Protects you from devastating losses

**How to Calculate Risk:**
1. Decide your position size
2. Set your stop loss
3. Calculate potential loss
4. Make sure it's ≤ 2% of your account

**Example:**
- Account: $10,000
- Max risk: $200 (2%)
- Stock price: $100
- Stop loss: $95
- Risk per share: $5
- Max shares: 40 ($200 ÷ $5)

Risk management isn't exciting, but it's what separates successful traders from those who lose everything.''',
                        'difficulty': 'beginner',
                        'estimated_time': '10 minutes'
                    }
                },
                'technical_analysis': {
                    'support_resistance': {
                        'title': 'Support and Resistance Levels',
                        'content': '''Support and resistance are key price levels where stocks tend to bounce or reverse.

**Support**: A price level where a stock tends to stop falling and bounce back up
**Resistance**: A price level where a stock tends to stop rising and fall back down

**How to Identify:**
- Look for areas where price has bounced multiple times
- Previous highs often become resistance
- Previous lows often become support

**Trading Strategy:**
- Buy near support levels
- Sell near resistance levels
- Wait for breakouts above resistance or below support

**Remember**: These levels aren't exact - think of them as zones rather than precise prices.''',
                        'difficulty': 'intermediate',
                        'estimated_time': '12 minutes'
                    }
                }
            }
            
            logger.info("[BOOK] Basic educational content loaded")
            
        except Exception as e:
            logger.error(f"Failed to load educational content: {e}")
            raise

    async def _initialize_beginner_mentor(self):
        """Initialize beginner trading mentor"""
        try:
            self.beginner_lessons = {
                'lesson_1': {
                    'title': 'Your First Steps in Trading',
                    'objectives': [
                        'Understand what stocks are',
                        'Learn basic trading terminology',
                        'Set up a paper trading account'
                    ],
                    'content': 'Start with the basics and build a strong foundation',
                    'next_lesson': 'lesson_2'
                },
                'lesson_2': {
                    'title': 'Risk Management Fundamentals',
                    'objectives': [
                        'Learn the 2% rule',
                        'Understand position sizing',
                        'Practice setting stop losses'
                    ],
                    'content': 'Master risk management before making any trades',
                    'next_lesson': 'lesson_3'
                },
                'lesson_3': {
                    'title': 'Reading Charts and Patterns',
                    'objectives': [
                        'Identify support and resistance',
                        'Recognize basic chart patterns',
                        'Use technical indicators'
                    ],
                    'content': 'Learn to read the language of charts',
                    'next_lesson': None
                }
            }
            
            logger.info("[MENTOR] Beginner trading mentor initialized")
            
        except Exception as e:
            logger.error(f"Beginner mentor initialization failed: {e}")
            raise

    async def get_educational_content(self, topic: str, difficulty: str = None) -> Dict[str, Any]:
        """Get educational content for a topic"""
        try:
            # Search through categories
            for category, content in self.educational_content.items():
                if topic.lower() in content:
                    lesson = content[topic.lower()]
                    if difficulty is None or lesson.get('difficulty') == difficulty:
                        return {
                            'found': True,
                            'category': category,
                            'topic': topic,
                            'lesson': lesson
                        }
            
            # If not found, provide general guidance
            return {
                'found': False,
                'topic': topic,
                'suggestion': f"I don't have specific content for '{topic}' yet, but I can help explain trading concepts. Try asking about 'stocks', 'risk management', or 'technical analysis'."
            }
            
        except Exception as e:
            logger.error(f"Error getting educational content: {e}")
            return {'found': False, 'error': str(e)}

    async def provide_beginner_guidance(self, user_question: str, user_level: str = 'beginner') -> str:
        """Provide beginner-friendly guidance"""
        try:
            question_lower = user_question.lower()
            
            # Stock basics
            if any(word in question_lower for word in ['what is stock', 'what are stocks', 'explain stock']):
                return self.educational_content['basics']['what_is_stock']['content']
            
            # Trading basics
            elif any(word in question_lower for word in ['what is trading', 'how to trade', 'trading basics']):
                return self.educational_content['basics']['what_is_trading']['content']
            
            # Risk management
            elif any(word in question_lower for word in ['risk', 'position size', '2% rule', 'money management']):
                return self.educational_content['risk_management']['position_sizing']['content']
            
            # Technical analysis
            elif any(word in question_lower for word in ['support', 'resistance', 'chart', 'technical']):
                return self.educational_content['technical_analysis']['support_resistance']['content']
            
            # General beginner advice
            else:
                return '''📚 **Welcome to Trading Education!**

I'm here to help you learn trading step by step. Here are some great topics to start with:

**Beginner Topics:**
- "What is a stock?" - Learn the basics
- "Risk management" - Protect your money
- "Support and resistance" - Read charts
- "Position sizing" - How much to invest

**Getting Started:**
1. Start with paper trading (fake money)
2. Learn risk management first
3. Practice with small amounts
4. Never stop learning

What would you like to learn about first?'''
            
        except Exception as e:
            logger.error(f"Error providing beginner guidance: {e}")
            return "I'm here to help you learn trading! Ask me about stocks, risk management, or technical analysis."

    async def get_lesson_plan(self, user_id: str, current_level: str = 'beginner') -> Dict[str, Any]:
        """Get personalized lesson plan"""
        try:
            # Get user progress
            progress = self.user_progress.get(user_id, {
                'completed_lessons': [],
                'current_lesson': 'lesson_1',
                'level': current_level
            })
            
            # Get current lesson
            current_lesson_id = progress['current_lesson']
            current_lesson = self.beginner_lessons.get(current_lesson_id)
            
            if not current_lesson:
                return {
                    'status': 'completed',
                    'message': 'Congratulations! You have completed all beginner lessons.',
                    'next_steps': 'Consider practicing with paper trading or exploring intermediate topics.'
                }
            
            return {
                'status': 'active',
                'current_lesson': current_lesson,
                'lesson_id': current_lesson_id,
                'progress': {
                    'completed': len(progress['completed_lessons']),
                    'total': len(self.beginner_lessons),
                    'percentage': (len(progress['completed_lessons']) / len(self.beginner_lessons)) * 100
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting lesson plan: {e}")
            return {'status': 'error', 'message': str(e)}

    async def complete_lesson(self, user_id: str, lesson_id: str) -> Dict[str, Any]:
        """Mark lesson as completed and advance user"""
        try:
            # Initialize user progress if not exists
            if user_id not in self.user_progress:
                self.user_progress[user_id] = {
                    'completed_lessons': [],
                    'current_lesson': 'lesson_1',
                    'level': 'beginner'
                }
            
            progress = self.user_progress[user_id]
            
            # Mark lesson as completed
            if lesson_id not in progress['completed_lessons']:
                progress['completed_lessons'].append(lesson_id)
            
            # Advance to next lesson
            current_lesson = self.beginner_lessons.get(lesson_id)
            if current_lesson and current_lesson.get('next_lesson'):
                progress['current_lesson'] = current_lesson['next_lesson']
            else:
                progress['current_lesson'] = None  # All lessons completed
            
            return {
                'success': True,
                'message': f'Lesson {lesson_id} completed!',
                'next_lesson': progress['current_lesson']
            }
            
        except Exception as e:
            logger.error(f"Error completing lesson: {e}")
            return {'success': False, 'error': str(e)}

    async def search_educational_content(self, query: str) -> List[Dict[str, Any]]:
        """Search educational content with web search enhancement"""
        try:
            # Search local educational content first
            local_results = []
            query_lower = query.lower()

            for category, content in self.educational_content.items():
                for topic, lesson in content.items():
                    # Search in title and content
                    if (query_lower in lesson['title'].lower() or
                        query_lower in lesson['content'].lower()):
                        local_results.append({
                            'category': category,
                            'topic': topic,
                            'title': lesson['title'],
                            'difficulty': lesson.get('difficulty', 'unknown'),
                            'estimated_time': lesson.get('estimated_time', 'unknown'),
                            'relevance_score': self._calculate_relevance(query_lower, lesson),
                            'source': 'local'
                        })

            # Enhance with web search if enabled and local results are limited
            if (self.web_search_enabled and self.web_search_service.is_available() and
                len(local_results) < 3):

                web_results = await self.web_search_service.search_for_context(
                    f"{query} trading education tutorial guide example",
                    SearchContext.EDUCATION,
                    max_results=3
                )

                # Convert web results to educational format
                for result in web_results:
                    local_results.append({
                        'category': 'web_content',
                        'topic': 'external_resource',
                        'title': result.title,
                        'difficulty': 'unknown',
                        'estimated_time': 'varies',
                        'relevance_score': result.relevance_score,
                        'source': 'web',
                        'url': result.url,
                        'snippet': result.snippet
                    })

            # Sort by relevance
            local_results.sort(key=lambda x: x['relevance_score'], reverse=True)

            return local_results[:5]  # Return top 5 results
            
        except Exception as e:
            logger.error(f"Error searching educational content: {e}")
            return []

    def _calculate_relevance(self, query: str, lesson: Dict[str, Any]) -> float:
        """Calculate relevance score for search results"""
        try:
            score = 0.0
            
            # Title match (higher weight)
            if query in lesson['title'].lower():
                score += 2.0
            
            # Content match
            content_lower = lesson['content'].lower()
            query_words = query.split()
            for word in query_words:
                if word in content_lower:
                    score += 1.0
            
            return score
            
        except Exception:
            return 0.0

    def get_education_status(self) -> Dict[str, Any]:
        """Get education engine status"""
        return {
            'status': self.status.value,
            'categories': len(self.categories),
            'total_lessons': sum(len(content) for content in self.educational_content.values()),
            'beginner_lessons': len(self.beginner_lessons),
            'active_users': len(self.user_progress)
        }


# ============================================================================
# BEGINNER TRADING MENTOR
# ============================================================================

class BeginnerTradingMentor:
    """Specialized mentor for beginner traders"""
    
    def __init__(self):
        self.education_engine = AtlasEducationEngine()
        logger.info("[MENTOR] Beginner Trading Mentor initialized")

    async def initialize(self):
        """Initialize mentor"""
        await self.education_engine.initialize()
        logger.info("[OK] Beginner Trading Mentor ready")

    async def provide_guidance(self, question: str, user_level: str = 'beginner') -> str:
        """Provide mentorship guidance"""
        return await self.education_engine.provide_beginner_guidance(question, user_level)

    async def create_learning_path(self, user_id: str, goals: List[str] = None) -> Dict[str, Any]:
        """Create personalized learning path"""
        try:
            if goals is None:
                goals = ['learn_basics', 'risk_management', 'technical_analysis']
            
            learning_path = {
                'user_id': user_id,
                'goals': goals,
                'recommended_sequence': [
                    'lesson_1',  # Basics
                    'lesson_2',  # Risk Management
                    'lesson_3'   # Technical Analysis
                ],
                'estimated_duration': '2-3 weeks',
                'difficulty': 'beginner'
            }
            
            return learning_path
            
        except Exception as e:
            logger.error(f"Error creating learning path: {e}")
            return {'error': str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasEducationEngine",
    "BeginnerTradingMentor"
]
