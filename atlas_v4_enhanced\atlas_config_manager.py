"""
A.T.L.A.S. Configuration Manager for Executable Distribution
Handles secure configuration loading and validation for distributed executables
"""

import os
import sys
import logging
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
import tkinter as tk
from tkinter import messagebox, simpledialog, filedialog
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class AtlasConfigManager:
    """Manages configuration for A.T.L.A.S. executable distribution"""
    
    def __init__(self):
        self.app_dir = self._get_app_directory()
        self.config_file = self.app_dir / ".env"
        self.template_file = self._get_template_path()
        
    def _get_app_directory(self) -> Path:
        """Get the application directory for storing configuration"""
        if getattr(sys, 'frozen', False):
            # Running as executable
            app_dir = Path(sys.executable).parent / "atlas_config"
        else:
            # Running as script
            app_dir = Path(__file__).parent / "atlas_config"
        
        app_dir.mkdir(exist_ok=True)
        return app_dir
    
    def _get_template_path(self) -> Path:
        """Get path to configuration template"""
        if getattr(sys, 'frozen', False):
            # Running as executable - template is bundled
            return Path(sys._MEIPASS) / "config_template.env"
        else:
            # Running as script
            return Path(__file__).parent / "config_template.env"
    
    def setup_configuration(self) -> bool:
        """Set up configuration for first-time users"""
        try:
            if self.config_file.exists():
                # Configuration already exists, ask if user wants to reconfigure
                root = tk.Tk()
                root.withdraw()  # Hide main window
                
                result = messagebox.askyesno(
                    "A.T.L.A.S. Configuration",
                    "Configuration file already exists. Do you want to reconfigure your API keys?",
                    icon="question"
                )
                
                if not result:
                    return True
            
            return self._run_configuration_wizard()
            
        except Exception as e:
            logger.error(f"Configuration setup failed: {e}")
            return False
    
    def _run_configuration_wizard(self) -> bool:
        """Run the configuration wizard GUI"""
        try:
            root = tk.Tk()
            root.title("A.T.L.A.S. Trading System - Configuration Setup")
            root.geometry("600x500")
            
            # Welcome message
            welcome_text = """
Welcome to A.T.L.A.S. Trading System!

To get started, you'll need to provide your API keys for market data and AI services.
Don't worry - your keys are stored securely on your local machine only.

Required APIs:
• Alpaca (Paper Trading) - Free trading simulation
• FMP (Financial Modeling Prep) - Market data
• Grok AI (X.AI) - Primary AI provider

Optional APIs:
• OpenAI - Fallback AI provider
• Google Search - Enhanced web search
            """
            
            tk.Label(root, text=welcome_text, justify=tk.LEFT, wraplength=550).pack(pady=10)
            
            # API key inputs
            api_keys = {}
            
            # Required APIs
            tk.Label(root, text="Required API Keys:", font=("Arial", 12, "bold")).pack(pady=(20, 5))
            
            required_apis = [
                ("ALPACA_API_KEY", "Alpaca API Key"),
                ("ALPACA_SECRET_KEY", "Alpaca Secret Key"),
                ("FMP_API_KEY", "FMP API Key"),
                ("GROK_API_KEY", "Grok AI API Key")
            ]
            
            for key, label in required_apis:
                frame = tk.Frame(root)
                frame.pack(fill=tk.X, padx=20, pady=2)
                tk.Label(frame, text=f"{label}:", width=20, anchor="w").pack(side=tk.LEFT)
                entry = tk.Entry(frame, width=50, show="*")
                entry.pack(side=tk.RIGHT, expand=True, fill=tk.X)
                api_keys[key] = entry
            
            # Optional APIs
            tk.Label(root, text="Optional API Keys:", font=("Arial", 12, "bold")).pack(pady=(20, 5))
            
            optional_apis = [
                ("OPENAI_API_KEY", "OpenAI API Key"),
                ("GOOGLE_SEARCH_API_KEY", "Google Search API Key")
            ]
            
            for key, label in optional_apis:
                frame = tk.Frame(root)
                frame.pack(fill=tk.X, padx=20, pady=2)
                tk.Label(frame, text=f"{label}:", width=20, anchor="w").pack(side=tk.LEFT)
                entry = tk.Entry(frame, width=50, show="*")
                entry.pack(side=tk.RIGHT, expand=True, fill=tk.X)
                api_keys[key] = entry
            
            # Buttons
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            result = {"success": False}
            
            def save_config():
                # Validate required keys
                missing_keys = []
                for key in ["ALPACA_API_KEY", "ALPACA_SECRET_KEY", "FMP_API_KEY", "GROK_API_KEY"]:
                    if not api_keys[key].get().strip():
                        missing_keys.append(key)
                
                if missing_keys:
                    messagebox.showerror("Missing Required Keys", 
                                       f"Please provide the following required API keys:\n" + 
                                       "\n".join(missing_keys))
                    return
                
                # Save configuration
                if self._save_configuration({k: v.get().strip() for k, v in api_keys.items()}):
                    result["success"] = True
                    root.quit()
                else:
                    messagebox.showerror("Error", "Failed to save configuration. Please try again.")
            
            def load_from_file():
                file_path = filedialog.askopenfilename(
                    title="Select .env file",
                    filetypes=[("Environment files", "*.env"), ("All files", "*.*")]
                )
                if file_path:
                    try:
                        with open(file_path, 'r') as f:
                            for line in f:
                                if '=' in line and not line.strip().startswith('#'):
                                    key, value = line.strip().split('=', 1)
                                    if key in api_keys:
                                        api_keys[key].delete(0, tk.END)
                                        api_keys[key].insert(0, value)
                        messagebox.showinfo("Success", "Configuration loaded from file!")
                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to load file: {e}")
            
            tk.Button(button_frame, text="Save Configuration", command=save_config, 
                     bg="green", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Load from File", command=load_from_file).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Cancel", command=root.quit).pack(side=tk.LEFT, padx=5)
            
            # Help button
            def show_help():
                help_text = """
API Key Setup Instructions:

1. Alpaca (Paper Trading):
   - Visit: https://app.alpaca.markets/paper/dashboard/overview
   - Sign up for free paper trading account
   - Generate API keys in dashboard

2. FMP (Financial Modeling Prep):
   - Visit: https://financialmodelingprep.com/developer/docs
   - Sign up for free account (250 requests/day)
   - Get API key from dashboard

3. Grok AI (X.AI):
   - Visit: https://console.x.ai/
   - Sign up and get API key
   - Primary AI provider for analysis

4. OpenAI (Optional):
   - Visit: https://platform.openai.com/api-keys
   - Fallback AI provider

All keys are stored locally and never shared.
                """
                messagebox.showinfo("API Key Setup Help", help_text)
            
            tk.Button(root, text="Help - How to get API keys", command=show_help, 
                     bg="blue", fg="white").pack(pady=10)
            
            root.mainloop()
            root.destroy()
            
            return result["success"]
            
        except Exception as e:
            logger.error(f"Configuration wizard failed: {e}")
            return False
    
    def _save_configuration(self, api_keys: Dict[str, str]) -> bool:
        """Save configuration to file"""
        try:
            # Copy template and update with user values
            if self.template_file.exists():
                shutil.copy2(self.template_file, self.config_file)
            
            # Read template content
            with open(self.config_file, 'r') as f:
                content = f.read()
            
            # Replace placeholder values
            for key, value in api_keys.items():
                if value:  # Only replace if value is provided
                    placeholder = f"YOUR_{key}_HERE"
                    content = content.replace(placeholder, value)
            
            # Write updated content
            with open(self.config_file, 'w') as f:
                f.write(content)
            
            logger.info(f"Configuration saved to {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def load_configuration(self) -> bool:
        """Load configuration into environment"""
        try:
            if not self.config_file.exists():
                logger.warning("Configuration file not found, running setup...")
                if not self.setup_configuration():
                    return False
            
            # Load environment variables
            load_dotenv(self.config_file)
            logger.info("Configuration loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def validate_configuration(self) -> Dict[str, bool]:
        """Validate that required configuration is present"""
        required_keys = [
            "ALPACA_API_KEY",
            "ALPACA_SECRET_KEY", 
            "FMP_API_KEY",
            "GROK_API_KEY"
        ]
        
        validation_results = {}
        for key in required_keys:
            value = os.getenv(key)
            validation_results[key] = bool(value and value != f"YOUR_{key}_HERE")
        
        return validation_results

# Global configuration manager instance
config_manager = AtlasConfigManager()

def ensure_configuration() -> bool:
    """Ensure configuration is set up and loaded"""
    return config_manager.load_configuration()

if __name__ == "__main__":
    # Test the configuration manager
    if ensure_configuration():
        print("Configuration loaded successfully!")
        validation = config_manager.validate_configuration()
        print("Validation results:", validation)
    else:
        print("Configuration setup failed!")
