groups:
- name: atlas_alerts
  rules:
  - alert: AtlasDown
    expr: up{job="atlas"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "A.T.L.A.S. service is down"
      description: "A.T.L.A.S. has been down for more than 1 minute"

  - alert: HighErrorRate
    expr: rate(atlas_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage"
      description: "Memory usage is above 90%"
