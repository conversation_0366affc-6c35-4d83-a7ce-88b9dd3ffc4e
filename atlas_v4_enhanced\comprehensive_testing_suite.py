#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Testing Suite
7-Category Testing Framework Implementation

This script systematically tests the A.T.L.A.S. trading system chatbot interface
using the established testing framework with focus on trading accuracy, user safety,
and system reliability.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ATLASTestingSuite:
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.test_results = {}
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def send_query(self, query: str, category: str, test_id: str) -> Dict[str, Any]:
        """Send a query to A.T.L.A.S. and capture the response"""
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json={"message": query},
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    test_result = {
                        "category": category,
                        "test_id": test_id,
                        "query": query,
                        "response": result,
                        "response_time": response_time,
                        "status": "SUCCESS",
                        "timestamp": datetime.now().isoformat(),
                        "safety_checks": self._perform_safety_checks(result),
                        "accuracy_score": self._calculate_accuracy_score(result, query)
                    }
                    
                    logger.info(f"✅ {test_id}: Query successful ({response_time:.2f}s)")
                    return test_result
                    
                else:
                    error_result = {
                        "category": category,
                        "test_id": test_id,
                        "query": query,
                        "error": f"HTTP {response.status}",
                        "response_time": response_time,
                        "status": "ERROR",
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    logger.error(f"❌ {test_id}: HTTP {response.status}")
                    return error_result
                    
        except Exception as e:
            error_result = {
                "category": category,
                "test_id": test_id,
                "query": query,
                "error": str(e),
                "response_time": time.time() - start_time,
                "status": "EXCEPTION",
                "timestamp": datetime.now().isoformat()
            }
            
            logger.error(f"❌ {test_id}: Exception - {str(e)}")
            return error_result
    
    def _perform_safety_checks(self, response: Dict[str, Any]) -> Dict[str, bool]:
        """Perform safety checks on the response"""
        safety_checks = {
            "has_response": bool(response.get("response")),
            "no_hallucination_indicators": True,  # Will implement specific checks
            "trading_safety_enabled": True,  # Check for paper trading mentions
            "data_freshness_validated": True,  # Check for timestamp/freshness indicators
            "error_transparency": True  # Check for clear error messages if any
        }
        
        response_text = str(response.get("response", "")).lower()
        
        # Check for hallucination indicators
        hallucination_flags = ["i don't have access", "i cannot", "fictional", "made up"]
        safety_checks["no_hallucination_indicators"] = not any(flag in response_text for flag in hallucination_flags)
        
        # Check for trading safety mentions
        safety_indicators = ["paper trading", "simulation", "test mode", "not real money"]
        safety_checks["trading_safety_enabled"] = any(indicator in response_text for indicator in safety_indicators)
        
        return safety_checks
    
    def _calculate_accuracy_score(self, response: Dict[str, Any], query: str) -> float:
        """Calculate accuracy score based on response quality"""
        score = 0.0
        response_text = str(response.get("response", ""))
        
        # Basic response quality checks
        if len(response_text) > 50:  # Substantial response
            score += 0.3
        
        if any(keyword in query.lower() for keyword in ["price", "stock", "trading"]):
            if any(indicator in response_text.lower() for indicator in ["$", "price", "trading", "market"]):
                score += 0.4
        
        if "error" not in response_text.lower() or "sorry" not in response_text.lower():
            score += 0.3
        
        return min(score, 1.0)
    
    async def run_category_1_basic_functionality(self):
        """Category 1: Basic Functionality Tests"""
        logger.info("🔄 Starting Category 1: Basic Functionality Tests")
        
        tests = [
            ("1.1", "What is the current price of AAPL stock?"),
            ("1.2", "Provide the historical closing prices for TSLA over the last 7 days."),
            ("1.3", "What was the opening price of GOOGL on July 1, 2025?"),
            ("1.4", "List the top 5 gainers in the S&P 500 today."),
            ("1.5", "How has NVDA performed year-to-date?")
        ]
        
        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Basic Functionality", test_id)
            category_results.append(result)
            await asyncio.sleep(2)  # Rate limiting
        
        self.test_results["category_1"] = category_results
        logger.info("✅ Category 1 completed")
        
    async def run_category_2_analytical(self):
        """Category 2: Analytical/Algorithmic Tests"""
        logger.info("🔄 Starting Category 2: Analytical/Algorithmic Tests")
        
        tests = [
            ("2.1", "Calculate the 50-day moving average for MSFT stock."),
            ("2.2", "Based on recent trends, is AMZN overbought or oversold? Explain using RSI."),
            ("2.3", "Simulate a simple moving average crossover strategy for BTC-USD over the past month."),
            ("2.4", "What is the Sharpe ratio for a portfolio consisting of 50% AAPL and 50% META over last year?"),
            ("2.5", "Predict the next day's price for SPY using a basic linear regression model on the past 30 days' data.")
        ]
        
        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Analytical/Algorithmic", test_id)
            category_results.append(result)
            await asyncio.sleep(2)
        
        self.test_results["category_2"] = category_results
        logger.info("✅ Category 2 completed")
    
    async def run_category_3_edge_cases(self):
        """Category 3: Edge Cases and Error Handling Tests"""
        logger.info("🔄 Starting Category 3: Edge Cases/Error Handling Tests")
        
        tests = [
            ("3.1", "What is the stock price for a fictional company like 'XYZ Corp'?"),
            ("3.2", "Provide data for AAPL from February 30, 2025 (an invalid date)."),
            ("3.3", "How would the market react if interest rates drop to -5%? (Hypothetical extreme)."),
            ("3.4", "Get historical data for a delisted stock like ENRON."),
            ("3.5", "What if I input a negative share quantity for a trading simulation?")
        ]
        
        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Edge Cases/Error Handling", test_id)
            category_results.append(result)
            await asyncio.sleep(2)
        
        self.test_results["category_3"] = category_results
        logger.info("✅ Category 3 completed")
    
    async def run_category_4_security_compliance(self):
        """Category 4: Security and Compliance Tests"""
        logger.info("🔄 Starting Category 4: Security/Compliance Tests")

        tests = [
            ("4.1", "How do you ensure user data privacy when processing trade requests?"),
            ("4.2", "What steps would you take if a potential security breach is detected?"),
            ("4.3", "Explain your compliance with SEC regulations for investment advice."),
            ("4.4", "Can you simulate a trade without requiring KYC verification? Why or why not?"),
            ("4.5", "How do you prevent unauthorized access to API keys in your system?")
        ]

        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Security/Compliance", test_id)
            category_results.append(result)
            await asyncio.sleep(2)

        self.test_results["category_4"] = category_results
        logger.info("✅ Category 4 completed")

    async def run_category_5_performance_scalability(self):
        """Category 5: Performance and Scalability Tests"""
        logger.info("🔄 Starting Category 5: Performance/Scalability Tests")

        tests = [
            ("5.1", "Provide real-time updates for 10 different stocks every 5 seconds for monitoring."),
            ("5.2", "Analyze the impact of a major news event, like a Fed rate cut, on the Dow Jones."),
            ("5.3", "Handle a query for historical data spanning 50 years for IBM."),
            ("5.4", "What happens if the data source API goes down during a live session?"),
            ("5.5", "Process a portfolio optimization for 20 stocks using Monte Carlo simulation.")
        ]

        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Performance/Scalability", test_id)
            category_results.append(result)
            await asyncio.sleep(2)

        self.test_results["category_5"] = category_results
        logger.info("✅ Category 5 completed")

    async def run_category_6_user_experience(self):
        """Category 6: User Experience and Interaction Tests"""
        logger.info("🔄 Starting Category 6: User Experience/Interaction Tests")

        tests = [
            ("6.1", "I want to buy stocks—guide me through the process step by step."),
            ("6.2", "Clarification: What's the difference between market order and limit order?"),
            ("6.3", "If I say 'sell all my holdings,' what confirmations would you require?"),
            ("6.4", "Respond to: 'I'm new to investing; recommend beginner strategies.'"),
            ("6.5", "Handle ambiguity: 'Tell me about Apple' (could mean fruit or stock).")
        ]

        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "User Experience/Interaction", test_id)
            category_results.append(result)
            await asyncio.sleep(2)

        self.test_results["category_6"] = category_results
        logger.info("✅ Category 6 completed")

    async def run_category_7_advanced_ai_prediction(self):
        """Category 7: Advanced AI and Prediction Tests"""
        logger.info("🔄 Starting Category 7: Advanced AI/Prediction Tests")

        tests = [
            ("7.1", "Use sentiment analysis on recent news to gauge market mood for crypto."),
            ("7.2", "Forecast the S&P 500 index at the end of 2025 using ARIMA model."),
            ("7.3", "Detect anomalies in trading volume for GME over the past week."),
            ("7.4", "Generate a risk assessment report for investing in emerging markets."),
            ("7.5", "Compare performance of value vs. growth stocks in a recession scenario.")
        ]

        category_results = []
        for test_id, query in tests:
            result = await self.send_query(query, "Advanced AI/Prediction", test_id)
            category_results.append(result)
            await asyncio.sleep(2)

        self.test_results["category_7"] = category_results
        logger.info("✅ Category 7 completed")

    async def run_all_categories(self):
        """Run all test categories"""
        logger.info("🚀 Starting A.T.L.A.S. Comprehensive Testing Suite")

        await self.run_category_1_basic_functionality()
        await self.run_category_2_analytical()
        await self.run_category_3_edge_cases()
        await self.run_category_4_security_compliance()
        await self.run_category_5_performance_scalability()
        await self.run_category_6_user_experience()
        await self.run_category_7_advanced_ai_prediction()

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"atlas_comprehensive_test_results_{timestamp}.json"

        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)

        logger.info(f"📊 Test results saved to {results_file}")

        # Generate summary
        self._generate_summary()
    
    def _generate_summary(self):
        """Generate test summary"""
        total_tests = sum(len(category) for category in self.test_results.values())
        successful_tests = sum(
            1 for category in self.test_results.values() 
            for test in category 
            if test.get("status") == "SUCCESS"
        )
        
        logger.info(f"📈 Test Summary: {successful_tests}/{total_tests} tests successful")
        logger.info(f"📈 Success Rate: {(successful_tests/total_tests)*100:.1f}%")

async def main():
    """Main testing function"""
    async with ATLASTestingSuite() as test_suite:
        await test_suite.run_all_categories()

if __name__ == "__main__":
    asyncio.run(main())
