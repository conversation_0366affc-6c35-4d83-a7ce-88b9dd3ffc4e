# A.T.L.A.S. Multi-Agent System - Production Requirements
# Additional production-specific dependencies

# Production WSGI/ASGI servers
gunicorn==21.2.0
uvicorn[standard]==0.24.0

# Production monitoring and observability
prometheus-client==0.19.0
psutil==5.9.6
structlog==23.2.0

# Security and encryption
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Database and caching (if needed)
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# Production utilities
python-multipart==0.0.6
email-validator==2.1.0
python-dotenv==1.0.0

# Health checks and diagnostics
httpx==0.25.2
aiofiles==23.2.1

# Logging and error tracking
sentry-sdk[fastapi]==1.38.0
loguru==0.7.2

# Performance optimization
orjson==3.9.10
ujson==5.8.0

# Container and orchestration
kubernetes==28.1.0
docker==6.1.3

# Load balancing and service discovery
consul-python==1.1.0
etcd3==0.12.0

# Message queuing (for agent communication)
celery==5.3.4
kombu==5.3.4

# Distributed computing
dask[complete]==2023.11.0

# Production configuration management
pydantic-settings==2.1.0
dynaconf==3.2.4

# API rate limiting
slowapi==0.1.9

# CORS and security headers
python-cors==1.0.0

# Production testing
locust==2.17.0
