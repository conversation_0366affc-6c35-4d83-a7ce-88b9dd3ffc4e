"""
A.T.L.A.S Monitoring - Comprehensive System Monitoring, Metrics, and Alerting
Enhanced with real-time monitoring, intelligent alerting, and production-ready operations
"""

import asyncio
import logging
import json
import sys
import os
import psutil
import smtplib
import sqlite3
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from pathlib import Path

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


@dataclass
class MetricData:
    """Enhanced metric data structure"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    tags: Dict[str, str] = None
    source: str = "atlas_monitoring"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "timestamp": self.timestamp.isoformat(),
            "tags": self.tags or {},
            "source": self.source
        }


@dataclass
class Alert:
    """Enhanced alert structure"""
    id: str
    severity: str  # CRITICAL, WARNING, INFO
    title: str
    description: str
    timestamp: datetime
    source: str
    metric_name: str = None
    metric_value: float = None
    threshold: float = None
    resolved: bool = False

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


# ============================================================================
# ENHANCED ALERT MANAGEMENT
# ============================================================================

class EnhancedAlertManager:
    """Production-ready alert management with email notifications and persistence"""

    def __init__(self, smtp_config: Dict[str, str] = None):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.smtp_config = smtp_config or {}
        self.active_alerts = {}
        self.alert_history = []
        self.alert_rules = self._load_default_alert_rules()
        self.notification_callbacks = []

        # Initialize alert storage
        self.db_path = Path("atlas_alerts.db")
        self._init_alert_database()

        self.logger.info("[ALERT] Enhanced Alert Manager initialized")

    def _init_alert_database(self):
        """Initialize SQLite database for alert storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    severity TEXT,
                    title TEXT,
                    description TEXT,
                    timestamp TEXT,
                    source TEXT,
                    metric_name TEXT,
                    metric_value REAL,
                    threshold REAL,
                    resolved BOOLEAN
                )
            ''')
            conn.commit()
            conn.close()
            self.logger.info("[ALERT] Alert database initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize alert database: {e}")

    def _load_default_alert_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load production-ready alert rules"""
        return {
            "system.cpu.usage": {
                "warning_threshold": 80.0,
                "critical_threshold": 95.0,
                "comparison": "greater_than"
            },
            "system.memory.usage": {
                "warning_threshold": 80.0,
                "critical_threshold": 95.0,
                "comparison": "greater_than"
            },
            "system.disk.usage": {
                "warning_threshold": 85.0,
                "critical_threshold": 95.0,
                "comparison": "greater_than"
            },
            "atlas.api.status": {
                "critical_threshold": 0.5,
                "comparison": "less_than"
            },
            "atlas.api.response_time": {
                "warning_threshold": 1000.0,
                "critical_threshold": 5000.0,
                "comparison": "greater_than"
            },
            "atlas.scanner.active": {
                "critical_threshold": 0.5,
                "comparison": "less_than"
            },
            "atlas.trading.errors": {
                "warning_threshold": 5.0,
                "critical_threshold": 10.0,
                "comparison": "greater_than"
            }
        }

    def add_notification_callback(self, callback: Callable[[Alert], None]):
        """Add a notification callback function"""
        self.notification_callbacks.append(callback)

    def evaluate_metrics(self, metrics: List[MetricData]) -> List[Alert]:
        """Evaluate metrics against alert rules"""
        new_alerts = []

        for metric in metrics:
            if metric.name in self.alert_rules:
                rule = self.alert_rules[metric.name]
                alert = self._check_metric_against_rule(metric, rule)
                if alert:
                    new_alerts.append(alert)

        return new_alerts

    def _check_metric_against_rule(self, metric: MetricData, rule: Dict[str, Any]) -> Optional[Alert]:
        """Check a metric against an alert rule"""
        try:
            comparison = rule.get("comparison", "greater_than")

            # Check critical threshold
            if "critical_threshold" in rule:
                threshold = rule["critical_threshold"]
                if self._compare_values(metric.value, threshold, comparison):
                    alert_id = f"{metric.name}_critical_{int(metric.timestamp.timestamp())}"

                    # Check if this alert is already active
                    if alert_id not in self.active_alerts:
                        alert = Alert(
                            id=alert_id,
                            severity="CRITICAL",
                            title=f"🔴 CRITICAL: {metric.name}",
                            description=f"{metric.name} is {metric.value}{metric.unit} (threshold: {threshold}{metric.unit})",
                            timestamp=metric.timestamp,
                            source="atlas_monitoring",
                            metric_name=metric.name,
                            metric_value=metric.value,
                            threshold=threshold
                        )
                        self.active_alerts[alert_id] = alert
                        return alert

            # Check warning threshold
            if "warning_threshold" in rule:
                threshold = rule["warning_threshold"]
                if self._compare_values(metric.value, threshold, comparison):
                    alert_id = f"{metric.name}_warning_{int(metric.timestamp.timestamp())}"

                    if alert_id not in self.active_alerts:
                        alert = Alert(
                            id=alert_id,
                            severity="WARNING",
                            title=f"🟡 WARNING: {metric.name}",
                            description=f"{metric.name} is {metric.value}{metric.unit} (threshold: {threshold}{metric.unit})",
                            timestamp=metric.timestamp,
                            source="atlas_monitoring",
                            metric_name=metric.name,
                            metric_value=metric.value,
                            threshold=threshold
                        )
                        self.active_alerts[alert_id] = alert
                        return alert

            return None

        except Exception as e:
            self.logger.error(f"Failed to check metric against rule: {e}")
            return None

    def _compare_values(self, value: float, threshold: float, comparison: str) -> bool:
        """Compare values based on comparison type"""
        if comparison == "greater_than":
            return value > threshold
        elif comparison == "less_than":
            return value < threshold
        elif comparison == "equal":
            return abs(value - threshold) < 0.001
        else:
            return False

    async def send_alert(self, alert: Alert):
        """Send alert notification with multiple channels"""
        try:
            # Store alert in database
            self._store_alert(alert)

            # Add to history
            self.alert_history.append(alert)

            # Send email notification if configured
            if self.smtp_config:
                await self._send_email_alert(alert)

            # Call notification callbacks
            for callback in self.notification_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Notification callback failed: {e}")

            self.logger.info(f"Alert sent: {alert.severity} - {alert.title}")

        except Exception as e:
            self.logger.error(f"Failed to send alert: {e}")

    def _store_alert(self, alert: Alert):
        """Store alert in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO alerts
                (id, severity, title, description, timestamp, source, metric_name, metric_value, threshold, resolved)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.id, alert.severity, alert.title, alert.description,
                alert.timestamp.isoformat(), alert.source, alert.metric_name,
                alert.metric_value, alert.threshold, alert.resolved
            ))
            conn.commit()
            conn.close()
        except Exception as e:
            self.logger.error(f"Failed to store alert in database: {e}")

    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())

    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """Get alert history for specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.timestamp >= cutoff_time]

    def resolve_alert(self, alert_id: str):
        """Mark an alert as resolved"""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].resolved = True
            self._store_alert(self.active_alerts[alert_id])
            del self.active_alerts[alert_id]
            self.logger.info(f"Alert resolved: {alert_id}")

    async def _send_email_alert(self, alert: Alert):
        """Send email alert notification"""
        try:
            if not all(k in self.smtp_config for k in ['server', 'port', 'username', 'password', 'from_email', 'to_emails']):
                self.logger.debug("SMTP configuration incomplete, skipping email alert")
                return

            msg = MIMEMultipart()
            msg['From'] = self.smtp_config['from_email']
            msg['To'] = ', '.join(self.smtp_config['to_emails'])
            msg['Subject'] = f"A.T.L.A.S. Alert: {alert.severity} - {alert.title}"

            body = f"""
A.T.L.A.S. Trading System Alert

Severity: {alert.severity}
Title: {alert.title}
Description: {alert.description}
Timestamp: {alert.timestamp}
Source: {alert.source}

Metric Details:
- Name: {alert.metric_name}
- Value: {alert.metric_value}
- Threshold: {alert.threshold}

Please investigate and take appropriate action.

A.T.L.A.S. Monitoring System
            """

            msg.attach(MIMEText(body, 'plain'))

            server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
            server.starttls()
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            text = msg.as_string()
            server.sendmail(self.smtp_config['from_email'], self.smtp_config['to_emails'], text)
            server.quit()

            self.logger.info(f"Email alert sent for: {alert.title}")

        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")


# ============================================================================
# ENHANCED SYSTEM MONITORING
# ============================================================================

class AtlasSystemMonitor:
    """Enhanced system performance and health monitoring with production features"""

    def __init__(self, alert_manager: EnhancedAlertManager = None):
        self.status = EngineStatus.INITIALIZING
        self.metrics_history = []
        self.alerts = []
        self.monitoring_enabled = True
        self.alert_manager = alert_manager or EnhancedAlertManager()
        self.max_history = 10000  # Keep last 10k metrics

        logger.info("[MONITOR] Enhanced System Monitor initialized")

    async def initialize(self):
        """Initialize enhanced system monitoring"""
        try:
            self.status = EngineStatus.INITIALIZING

            # Initialize monitoring thresholds (production-ready)
            self.thresholds = {
                'cpu_usage': 80.0,  # %
                'memory_usage': 85.0,  # %
                'disk_usage': 90.0,  # %
                'response_time': 5.0,  # seconds
                'api_error_rate': 0.1,  # %
                'scanner_response_time': 2.0,  # seconds
                'database_query_time': 0.1,  # seconds
                'error_rate': 5.0  # %
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] System Monitor ready")
            
        except Exception as e:
            logger.error(f"System Monitor initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect current system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            
            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                bytes_sent = network.bytes_sent
                bytes_recv = network.bytes_recv
            except:
                bytes_sent = bytes_recv = 0
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'core_count': cpu_count,
                    'status': 'normal' if cpu_percent < self.thresholds['cpu_usage'] else 'high'
                },
                'memory': {
                    'usage_percent': memory_percent,
                    'available_gb': round(memory_available, 2),
                    'total_gb': round(memory.total / (1024**3), 2),
                    'status': 'normal' if memory_percent < self.thresholds['memory_usage'] else 'high'
                },
                'disk': {
                    'usage_percent': disk_percent,
                    'free_gb': round(disk_free, 2),
                    'total_gb': round(disk.total / (1024**3), 2),
                    'status': 'normal' if disk_percent < self.thresholds['disk_usage'] else 'high'
                },
                'network': {
                    'bytes_sent': bytes_sent,
                    'bytes_received': bytes_recv
                }
            }
            
            # Store metrics history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > 1000:  # Keep last 1000 entries
                self.metrics_history = self.metrics_history[-1000:]
            
            # Check for alerts
            await self._check_system_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")
            return {'error': str(e)}

    async def collect_enhanced_metrics(self) -> List[MetricData]:
        """Collect enhanced metrics for production monitoring"""
        try:
            metrics = []
            timestamp = datetime.now()

            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics.append(MetricData("system.cpu.usage", cpu_percent, "percent", timestamp))

            memory = psutil.virtual_memory()
            metrics.append(MetricData("system.memory.usage", memory.percent, "percent", timestamp))
            metrics.append(MetricData("system.memory.available", memory.available / 1024**3, "GB", timestamp))

            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            metrics.append(MetricData("system.disk.usage", disk_percent, "percent", timestamp))

            # Application metrics
            try:
                async with aiohttp.ClientSession() as session:
                    # API health check
                    start_time = time.time()
                    try:
                        async with session.get('http://localhost:8000/api/health', timeout=5) as response:
                            response_time = (time.time() - start_time) * 1000
                            metrics.append(MetricData("atlas.api.response_time", response_time, "ms", timestamp))
                            metrics.append(MetricData("atlas.api.status", 1 if response.status == 200 else 0, "boolean", timestamp))
                    except:
                        metrics.append(MetricData("atlas.api.status", 0, "boolean", timestamp))

                    # Scanner status
                    try:
                        async with session.get('http://localhost:8000/api/scanner/status', timeout=5) as response:
                            if response.status == 200:
                                data = await response.json()
                                metrics.append(MetricData("atlas.scanner.active", 1 if data.get('active') else 0, "boolean", timestamp))
                                metrics.append(MetricData("atlas.scanner.signals_count", data.get('signals_count', 0), "count", timestamp))
                    except:
                        metrics.append(MetricData("atlas.scanner.active", 0, "boolean", timestamp))
            except:
                pass  # Skip if aiohttp not available

            # Evaluate alerts
            if self.alert_manager:
                new_alerts = self.alert_manager.evaluate_metrics(metrics)
                for alert in new_alerts:
                    await self.alert_manager.send_alert(alert)

            return metrics

        except Exception as e:
            logger.error(f"Enhanced metrics collection failed: {e}")
            return []


# ============================================================================
# PRODUCTION MONITORING ORCHESTRATOR
# ============================================================================

class AtlasProductionMonitoring:
    """Production-ready monitoring orchestrator for A.T.L.A.S."""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Initialize components
        smtp_config = self.config.get('smtp', {})
        self.alert_manager = EnhancedAlertManager(smtp_config)
        self.system_monitor = AtlasSystemMonitor(self.alert_manager)

        # Monitoring state
        self.is_running = False
        self.monitoring_task = None
        self.collection_interval = self.config.get('collection_interval', 30)  # seconds

        # Performance tracking
        self.monitoring_stats = {
            'collections_count': 0,
            'alerts_sent': 0,
            'last_collection_time': None,
            'average_collection_time': 0.0,
            'uptime_start': datetime.now()
        }

        self.logger.info("[PRODUCTION] A.T.L.A.S. Production Monitoring initialized")

    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """Add alert notification callback"""
        self.alert_manager.add_notification_callback(callback)

    async def start_monitoring(self):
        """Start the production monitoring system"""
        if self.is_running:
            self.logger.warning("Monitoring system is already running")
            return

        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("🚀 A.T.L.A.S. production monitoring started")

    async def stop_monitoring(self):
        """Stop the monitoring system"""
        if not self.is_running:
            return

        self.is_running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        self.logger.info("⏹️ A.T.L.A.S. production monitoring stopped")

    async def _monitoring_loop(self):
        """Main production monitoring loop"""
        while self.is_running:
            try:
                start_time = time.time()

                # Collect enhanced metrics
                enhanced_metrics = await self.system_monitor.collect_enhanced_metrics()

                # Collect standard metrics for compatibility
                standard_metrics = await self.system_monitor.collect_system_metrics()

                # Update statistics
                collection_time = time.time() - start_time
                self.monitoring_stats['collections_count'] += 1
                self.monitoring_stats['last_collection_time'] = datetime.now()

                # Update average collection time
                count = self.monitoring_stats['collections_count']
                current_avg = self.monitoring_stats['average_collection_time']
                self.monitoring_stats['average_collection_time'] = (
                    (current_avg * (count - 1) + collection_time) / count
                )

                self.logger.debug(f"Monitoring cycle completed in {collection_time:.2f}s, "
                                f"collected {len(enhanced_metrics)} enhanced metrics")

                # Wait for next collection
                await asyncio.sleep(self.collection_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Short delay before retrying

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        uptime = datetime.now() - self.monitoring_stats['uptime_start']

        return {
            "monitoring_active": self.is_running,
            "collection_interval": self.collection_interval,
            "uptime_seconds": uptime.total_seconds(),
            "uptime_formatted": str(uptime),
            "statistics": self.monitoring_stats,
            "active_alerts_count": len(self.alert_manager.get_active_alerts()),
            "system_status": self.system_monitor.status.value,
            "last_metrics": self.get_latest_metrics_summary()
        }

    def get_latest_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of latest metrics"""
        try:
            if not self.system_monitor.metrics_history:
                return {}

            latest = self.system_monitor.metrics_history[-1]
            return {
                "timestamp": latest.get('timestamp'),
                "cpu_usage": latest.get('cpu', {}).get('usage_percent', 0),
                "memory_usage": latest.get('memory', {}).get('usage_percent', 0),
                "disk_usage": latest.get('disk', {}).get('usage_percent', 0),
                "api_status": "healthy" if latest.get('api_status') else "unhealthy"
            }
        except Exception as e:
            self.logger.error(f"Failed to get metrics summary: {e}")
            return {}

    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts"""
        return [alert.to_dict() for alert in self.alert_manager.get_active_alerts()]

    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alert history"""
        return [alert.to_dict() for alert in self.alert_manager.get_alert_history(hours)]

    async def _check_system_alerts(self, metrics: Dict[str, Any]):
        """Check for system alerts based on metrics"""
        try:
            alerts = []
            
            # CPU alert
            if metrics['cpu']['usage_percent'] > self.thresholds['cpu_usage']:
                alerts.append({
                    'type': 'high_cpu',
                    'message': f"High CPU usage: {metrics['cpu']['usage_percent']:.1f}%",
                    'severity': 'warning',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Memory alert
            if metrics['memory']['usage_percent'] > self.thresholds['memory_usage']:
                alerts.append({
                    'type': 'high_memory',
                    'message': f"High memory usage: {metrics['memory']['usage_percent']:.1f}%",
                    'severity': 'warning',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Disk alert
            if metrics['disk']['usage_percent'] > self.thresholds['disk_usage']:
                alerts.append({
                    'type': 'high_disk',
                    'message': f"High disk usage: {metrics['disk']['usage_percent']:.1f}%",
                    'severity': 'critical',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Add alerts to history
            self.alerts.extend(alerts)
            if len(self.alerts) > 100:  # Keep last 100 alerts
                self.alerts = self.alerts[-100:]
            
            # Log critical alerts
            for alert in alerts:
                if alert['severity'] == 'critical':
                    logger.warning(f"[ALERT] {alert['message']}")
                    
        except Exception as e:
            logger.error(f"System alert check failed: {e}")

    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            if not self.metrics_history:
                return {'status': 'unknown', 'message': 'No metrics available'}
            
            latest_metrics = self.metrics_history[-1]
            
            # Calculate health score
            health_score = 100
            
            if latest_metrics['cpu']['status'] == 'high':
                health_score -= 20
            if latest_metrics['memory']['status'] == 'high':
                health_score -= 25
            if latest_metrics['disk']['status'] == 'high':
                health_score -= 30
            
            # Recent alerts impact
            recent_alerts = [
                alert for alert in self.alerts
                if (datetime.now() - datetime.fromisoformat(alert['timestamp'])).minutes < 30
            ]
            health_score -= len(recent_alerts) * 5
            
            # Determine status
            if health_score >= 90:
                status = 'excellent'
            elif health_score >= 70:
                status = 'good'
            elif health_score >= 50:
                status = 'fair'
            else:
                status = 'poor'
            
            return {
                'status': status,
                'health_score': max(0, health_score),
                'latest_metrics': latest_metrics,
                'recent_alerts': len(recent_alerts),
                'monitoring_enabled': self.monitoring_enabled
            }
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            return {'status': 'error', 'error': str(e)}


# ============================================================================
# PROACTIVE ASSISTANT
# ============================================================================

class AtlasProactiveAssistant:
    """Proactive trading assistant with intelligent suggestions"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.suggestions = []
        self.user_context = {}
        self.market_context = {}
        
        logger.info("[BOT] Proactive Assistant initialized")

    async def initialize(self):
        """Initialize proactive assistant"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize suggestion templates
            self.suggestion_templates = {
                'market_close': "Market is closing soon - would you like a summary of today's activity?",
                'portfolio_review': "It's been a week since your last portfolio review - shall we analyze performance?",
                'risk_check': "Your portfolio risk has increased - would you like me to suggest adjustments?",
                'news_alert': "Important news detected for your watchlist - would you like an analysis?",
                'learning_opportunity': "Based on recent trades, here's a learning opportunity for you."
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Proactive Assistant ready")
            
        except Exception as e:
            logger.error(f"Proactive Assistant initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def generate_suggestions(self, user_context: Dict[str, Any] = None, 
                                 market_context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Generate proactive suggestions"""
        try:
            suggestions = []
            current_time = datetime.now()
            
            # Update context
            if user_context:
                self.user_context.update(user_context)
            if market_context:
                self.market_context.update(market_context)
            
            # Time-based suggestions
            if 15 <= current_time.hour <= 16:  # Market closing time
                suggestions.append({
                    'type': 'market_close',
                    'message': self.suggestion_templates['market_close'],
                    'priority': 'medium',
                    'timestamp': current_time.isoformat()
                })
            
            # Portfolio-based suggestions
            if self.user_context.get('portfolio_value'):
                last_review = self.user_context.get('last_portfolio_review')
                if not last_review or (current_time - datetime.fromisoformat(last_review)).days >= 7:
                    suggestions.append({
                        'type': 'portfolio_review',
                        'message': self.suggestion_templates['portfolio_review'],
                        'priority': 'low',
                        'timestamp': current_time.isoformat()
                    })
            
            # Risk-based suggestions
            portfolio_risk = self.user_context.get('portfolio_risk', 0)
            if portfolio_risk > 0.15:  # 15% risk threshold
                suggestions.append({
                    'type': 'risk_check',
                    'message': self.suggestion_templates['risk_check'],
                    'priority': 'high',
                    'timestamp': current_time.isoformat()
                })
            
            # Market-based suggestions
            if self.market_context.get('volatility', 0) > 0.25:  # High volatility
                suggestions.append({
                    'type': 'volatility_alert',
                    'message': "Market volatility is elevated - consider reviewing your positions.",
                    'priority': 'medium',
                    'timestamp': current_time.isoformat()
                })
            
            # Learning suggestions for beginners
            if self.user_context.get('experience_level') == 'beginner':
                suggestions.append({
                    'type': 'learning_opportunity',
                    'message': self.suggestion_templates['learning_opportunity'],
                    'priority': 'low',
                    'timestamp': current_time.isoformat()
                })
            
            # Store suggestions
            self.suggestions.extend(suggestions)
            if len(self.suggestions) > 50:  # Keep last 50 suggestions
                self.suggestions = self.suggestions[-50:]
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Suggestion generation failed: {e}")
            return []

    def get_recent_suggestions(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent suggestions"""
        return self.suggestions[-limit:] if self.suggestions else []


# ============================================================================
# GURU SCORING METRICS
# ============================================================================

class AtlasGuruScoringMetrics:
    """Advanced scoring metrics for trading performance"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.performance_history = []
        self.scoring_weights = {
            'win_rate': 0.25,
            'profit_factor': 0.25,
            'sharpe_ratio': 0.20,
            'max_drawdown': 0.15,
            'consistency': 0.15
        }
        
        logger.info("[GURU] Guru Scoring Metrics initialized")

    async def initialize(self):
        """Initialize guru scoring system"""
        try:
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Guru Scoring Metrics ready")
            
        except Exception as e:
            logger.error(f"Guru Scoring initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def calculate_guru_score(self, trading_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive guru score"""
        try:
            # Extract performance metrics
            total_trades = trading_performance.get('total_trades', 0)
            winning_trades = trading_performance.get('winning_trades', 0)
            total_profit = trading_performance.get('total_profit', 0)
            total_loss = trading_performance.get('total_loss', 0)
            max_drawdown = trading_performance.get('max_drawdown', 0)
            
            if total_trades == 0:
                return {
                    'guru_score': 0,
                    'grade': 'N/A',
                    'message': 'No trading history available'
                }
            
            # Calculate individual metrics
            win_rate = (winning_trades / total_trades) * 100
            profit_factor = abs(total_profit / total_loss) if total_loss != 0 else 0
            
            # Simplified Sharpe ratio calculation
            avg_return = (total_profit + total_loss) / total_trades if total_trades > 0 else 0
            return_std = abs(avg_return) * 0.5  # Simplified volatility
            sharpe_ratio = avg_return / return_std if return_std != 0 else 0
            
            # Consistency score (simplified)
            consistency = min(100, win_rate * 1.2) if win_rate > 0 else 0
            
            # Calculate weighted score
            scores = {
                'win_rate': min(100, win_rate * 2),  # Scale to 0-100
                'profit_factor': min(100, profit_factor * 50),  # Scale to 0-100
                'sharpe_ratio': min(100, (sharpe_ratio + 2) * 25),  # Scale to 0-100
                'max_drawdown': max(0, 100 - (max_drawdown * 100 * 5)),  # Penalty for drawdown
                'consistency': consistency
            }
            
            # Calculate weighted guru score
            guru_score = sum(
                scores[metric] * weight
                for metric, weight in self.scoring_weights.items()
            )
            
            # Determine grade
            if guru_score >= 90:
                grade = 'A+'
            elif guru_score >= 80:
                grade = 'A'
            elif guru_score >= 70:
                grade = 'B'
            elif guru_score >= 60:
                grade = 'C'
            elif guru_score >= 50:
                grade = 'D'
            else:
                grade = 'F'
            
            return {
                'guru_score': round(guru_score, 1),
                'grade': grade,
                'individual_scores': scores,
                'metrics': {
                    'win_rate': round(win_rate, 1),
                    'profit_factor': round(profit_factor, 2),
                    'sharpe_ratio': round(sharpe_ratio, 2),
                    'max_drawdown': round(max_drawdown * 100, 1),
                    'consistency': round(consistency, 1)
                },
                'total_trades': total_trades,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Guru score calculation failed: {e}")
            return {'error': str(e)}


# ============================================================================
# MARKET CONTEXT ANALYZER
# ============================================================================

class AtlasMarketContextAnalyzer:
    """Market context and regime analysis"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.market_regimes = ['bull', 'bear', 'sideways', 'volatile']
        self.context_history = []
        
        logger.info("[CONTEXT] Market Context Analyzer initialized")

    async def initialize(self):
        """Initialize market context analyzer"""
        try:
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Market Context Analyzer ready")
            
        except Exception as e:
            logger.error(f"Market Context Analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def analyze_market_context(self, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze current market context"""
        try:
            # Simulate market context analysis
            # In production, this would analyze real market data
            
            import random
            
            context = {
                'timestamp': datetime.now().isoformat(),
                'market_regime': random.choice(self.market_regimes),
                'volatility_level': random.choice(['low', 'medium', 'high']),
                'trend_strength': random.uniform(0.3, 0.9),
                'sector_rotation': random.choice(['growth', 'value', 'defensive']),
                'risk_sentiment': random.choice(['risk_on', 'risk_off', 'neutral']),
                'key_levels': {
                    'support': 4200,
                    'resistance': 4400,
                    'current': 4300
                },
                'economic_indicators': {
                    'inflation_trend': 'declining',
                    'employment_trend': 'stable',
                    'gdp_growth': 'moderate'
                }
            }
            
            # Store context history
            self.context_history.append(context)
            if len(self.context_history) > 100:
                self.context_history = self.context_history[-100:]
            
            return context
            
        except Exception as e:
            logger.error(f"Market context analysis failed: {e}")
            return {'error': str(e)}


# ============================================================================
# MONITORING ORCHESTRATOR
# ============================================================================

class AtlasMonitoringOrchestrator:
    """Main monitoring orchestrator"""
    
    def __init__(self):
        self.system_monitor = AtlasSystemMonitor()
        self.proactive_assistant = AtlasProactiveAssistant()
        self.guru_scoring = AtlasGuruScoringMetrics()
        self.market_context = AtlasMarketContextAnalyzer()
        self.status = EngineStatus.INITIALIZING
        
        logger.info("[ORCHESTRATOR] Monitoring Orchestrator initialized")

    async def initialize(self):
        """Initialize all monitoring components"""
        try:
            await self.system_monitor.initialize()
            await self.proactive_assistant.initialize()
            await self.guru_scoring.initialize()
            await self.market_context.initialize()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Monitoring Orchestrator fully initialized")
            
        except Exception as e:
            logger.error(f"Monitoring Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def comprehensive_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        try:
            # Collect all monitoring data
            system_metrics = await self.system_monitor.collect_system_metrics()
            system_health = self.system_monitor.get_system_health()
            suggestions = await self.proactive_assistant.generate_suggestions()
            market_context = await self.market_context.analyze_market_context()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system_metrics': system_metrics,
                'system_health': system_health,
                'proactive_suggestions': suggestions,
                'market_context': market_context,
                'monitoring_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"Comprehensive monitoring report failed: {e}")
            return {'error': str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasSystemMonitor",
    "AtlasProactiveAssistant",
    "AtlasGuruScoringMetrics",
    "AtlasMarketContextAnalyzer",
    "AtlasMonitoringOrchestrator"
]
