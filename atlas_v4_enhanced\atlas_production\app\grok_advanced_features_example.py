"""
A.T.L.A.S. Grok Advanced Features Usage Examples
Demonstrates all the new advanced Grok API capabilities integrated into A.T.L.A.S.
"""

import asyncio
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

# Import enhanced Grok integration
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine,
    GrokRequest, GrokTaskType, GrokCapability,
    TradingSignal, MarketAnalysis, NewsAnalysis,
    ATLAS_TRADING_TOOLS, MARKET_SEARCH_CONFIGS,
    PYDANTIC_AVAILABLE
)

logger = logging.getLogger(__name__)

class GrokAdvancedFeaturesDemo:
    """Demonstration of all advanced Grok features in A.T.L.A.S."""
    
    def __init__(self):
        self.grok_engine = AtlasGrokIntegrationEngine()
        
    async def initialize(self):
        """Initialize the Grok engine"""
        success = await self.grok_engine.initialize()
        if success:
            logger.info("✅ Grok Advanced Features Demo initialized successfully")
        else:
            logger.warning("⚠️ Grok engine initialization failed - running in fallback mode")
        return success

    async def demo_live_search_integration(self, symbol: str = "AAPL"):
        """Demonstrate live search capabilities for real-time market data"""
        print(f"\n🔍 LIVE SEARCH DEMO - {symbol}")
        print("=" * 50)
        
        try:
            # Real-time news search
            news_result = await self.grok_engine.grok_client.make_live_search_request(
                query=f"Latest breaking news and market analysis for {symbol} stock",
                search_config="real_time_news",
                symbol=symbol
            )
            
            print(f"📰 News Search Results:")
            print(f"   Success: {news_result.success}")
            print(f"   Sources Used: {news_result.search_sources_used}")
            print(f"   Citations: {len(news_result.citations) if news_result.citations else 0}")
            if news_result.citations:
                print(f"   Top Sources: {news_result.citations[:3]}")
            
            # Social sentiment search
            sentiment_result = await self.grok_engine.grok_client.make_live_search_request(
                query=f"Social media sentiment and trader discussions about {symbol}",
                search_config="social_sentiment",
                symbol=symbol
            )
            
            print(f"\n📱 Social Sentiment Results:")
            print(f"   Success: {sentiment_result.success}")
            print(f"   Confidence: {sentiment_result.confidence:.2f}")
            
            return news_result, sentiment_result
            
        except Exception as e:
            logger.error(f"Live search demo failed: {e}")
            return None, None

    async def demo_structured_outputs(self, symbol: str = "TSLA"):
        """Demonstrate structured output capabilities"""
        print(f"\n📊 STRUCTURED OUTPUTS DEMO - {symbol}")
        print("=" * 50)
        
        if not PYDANTIC_AVAILABLE:
            print("⚠️ Pydantic not available - structured outputs disabled")
            return None
        
        try:
            # Generate structured trading signal
            signal_result = await self.grok_engine.grok_client.make_structured_analysis_request(
                symbol=symbol,
                analysis_type="trading_signal"
            )
            
            print(f"🎯 Trading Signal Results:")
            print(f"   Success: {signal_result.success}")
            print(f"   Has Structured Output: {signal_result.structured_output is not None}")
            
            if signal_result.structured_output:
                signal = signal_result.structured_output
                print(f"   Action: {signal.get('action', 'N/A')}")
                print(f"   Confidence: {signal.get('confidence', 0):.2f}")
                print(f"   Risk Level: {signal.get('risk_level', 'N/A')}")
                print(f"   Target Price: ${signal.get('target_price', 'N/A')}")
            
            # Generate structured market analysis
            analysis_result = await self.grok_engine.grok_client.make_structured_analysis_request(
                symbol=symbol,
                analysis_type="market_analysis"
            )
            
            print(f"\n📈 Market Analysis Results:")
            print(f"   Success: {analysis_result.success}")
            print(f"   Processing Time: {analysis_result.processing_time:.2f}s")
            
            return signal_result, analysis_result
            
        except Exception as e:
            logger.error(f"Structured outputs demo failed: {e}")
            return None, None

    async def demo_function_calling(self):
        """Demonstrate function calling capabilities"""
        print(f"\n🔧 FUNCTION CALLING DEMO")
        print("=" * 50)
        
        try:
            # Test function calling with trading tools
            query = """
            I want to analyze NVDA and potentially make a trade. 
            First get the current market data, then analyze the portfolio impact, 
            and finally scan for similar opportunities in the semiconductor sector.
            """
            
            result = await self.grok_engine.grok_client.make_function_calling_request(
                query=query,
                available_tools=["get_market_data", "analyze_portfolio", "scan_market_opportunities"]
            )
            
            print(f"🛠️ Function Calling Results:")
            print(f"   Success: {result.success}")
            print(f"   Has Tool Calls: {result.tool_calls is not None}")
            
            if result.tool_calls:
                print(f"   Number of Tool Calls: {len(result.tool_calls)}")
                for i, tool_call in enumerate(result.tool_calls):
                    print(f"   Tool {i+1}: {tool_call.get('function', {}).get('name', 'Unknown')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Function calling demo failed: {e}")
            return None

    async def demo_enhanced_reasoning(self, symbol: str = "SPY"):
        """Demonstrate enhanced reasoning capabilities"""
        print(f"\n🧠 ENHANCED REASONING DEMO - {symbol}")
        print("=" * 50)
        
        try:
            # Market scenario reasoning
            scenario = f"""
            The Federal Reserve is expected to announce interest rate decisions next week.
            Current market conditions show high volatility in {symbol} with mixed economic indicators.
            Inflation data came in higher than expected, but employment numbers are strong.
            How should traders position themselves for the Fed announcement?
            """
            
            reasoning_result = await self.grok_engine.enhanced_market_reasoning(
                market_scenario=scenario,
                symbol=symbol,
                reasoning_effort="high"
            )
            
            print(f"🎯 Market Reasoning Results:")
            print(f"   Success: {reasoning_result.grok_enhancement.success}")
            print(f"   Combined Confidence: {reasoning_result.combined_confidence:.2f}")
            print(f"   Has Reasoning Trace: {reasoning_result.grok_enhancement.reasoning_content is not None}")
            print(f"   Reasoning Steps: {len(reasoning_result.reasoning_chain)}")
            
            # What-if scenario analysis
            interventions = [
                "Fed raises rates by 0.75%",
                "Fed holds rates steady", 
                "Fed cuts rates by 0.25%",
                "Fed provides dovish guidance",
                "Unexpected hawkish stance"
            ]
            
            whatif_result = await self.grok_engine.what_if_scenario_analysis(
                base_scenario=scenario,
                interventions=interventions,
                symbol=symbol
            )
            
            print(f"\n🔮 What-If Analysis Results:")
            print(f"   Success: {whatif_result.grok_enhancement.success}")
            print(f"   Scenarios Analyzed: {whatif_result.original_result['interventions_count']}")
            print(f"   Decision Support Score: {whatif_result.improvement_metrics.get('decision_support', 0):.2f}")
            
            return reasoning_result, whatif_result
            
        except Exception as e:
            logger.error(f"Enhanced reasoning demo failed: {e}")
            return None, None

    async def demo_image_analysis(self):
        """Demonstrate advanced image analysis capabilities"""
        print(f"\n📸 IMAGE ANALYSIS DEMO")
        print("=" * 50)
        
        try:
            # Note: In a real implementation, you would load actual chart images
            # For demo purposes, we'll simulate with placeholder data
            print("📊 Chart Analysis Capabilities:")
            print("   ✅ Single chart technical analysis")
            print("   ✅ Multi-timeframe chart analysis") 
            print("   ✅ Earnings document analysis")
            print("   ✅ High-detail image processing")
            print("   ✅ Pattern recognition and trend analysis")
            
            # Simulate chart analysis result
            print(f"\n🎯 Simulated Chart Analysis Results:")
            print(f"   Image Detail Level: High")
            print(f"   Analysis Type: Technical")
            print(f"   Pattern Recognition: Active")
            print(f"   Support/Resistance Detection: Active")
            print(f"   Multi-timeframe Synthesis: Available")
            
            return True
            
        except Exception as e:
            logger.error(f"Image analysis demo failed: {e}")
            return False

    async def demo_comprehensive_integration(self, symbol: str = "AMZN"):
        """Demonstrate comprehensive integration of all features"""
        print(f"\n🚀 COMPREHENSIVE INTEGRATION DEMO - {symbol}")
        print("=" * 60)
        
        try:
            # Step 1: Live search for current market context
            print("Step 1: Gathering real-time market intelligence...")
            news_result, _ = await self.demo_live_search_integration(symbol)
            
            # Step 2: Generate structured analysis
            print("\nStep 2: Generating structured trading analysis...")
            signal_result, analysis_result = await self.demo_structured_outputs(symbol)
            
            # Step 3: Enhanced reasoning for decision making
            print("\nStep 3: Applying enhanced reasoning...")
            reasoning_result, _ = await self.demo_enhanced_reasoning(symbol)
            
            # Step 4: Function calling for execution planning
            print("\nStep 4: Planning execution strategy...")
            function_result = await self.demo_function_calling()
            
            # Comprehensive summary
            print(f"\n📋 COMPREHENSIVE ANALYSIS SUMMARY")
            print("=" * 50)
            print(f"Symbol: {symbol}")
            print(f"Analysis Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Live Data Sources: {'✅' if news_result and news_result.success else '❌'}")
            print(f"Structured Outputs: {'✅' if signal_result and signal_result.success else '❌'}")
            print(f"Enhanced Reasoning: {'✅' if reasoning_result and reasoning_result.grok_enhancement.success else '❌'}")
            print(f"Function Calling: {'✅' if function_result and function_result.success else '❌'}")
            
            return True
            
        except Exception as e:
            logger.error(f"Comprehensive integration demo failed: {e}")
            return False

async def main():
    """Main demo function"""
    print("🎯 A.T.L.A.S. GROK ADVANCED FEATURES DEMONSTRATION")
    print("=" * 60)
    
    demo = GrokAdvancedFeaturesDemo()
    
    # Initialize
    await demo.initialize()
    
    # Run all demos
    await demo.demo_live_search_integration("AAPL")
    await demo.demo_structured_outputs("TSLA") 
    await demo.demo_function_calling()
    await demo.demo_enhanced_reasoning("SPY")
    await demo.demo_image_analysis()
    await demo.demo_comprehensive_integration("AMZN")
    
    print(f"\n✅ All demos completed successfully!")
    print("🚀 A.T.L.A.S. is now enhanced with advanced Grok capabilities!")

if __name__ == "__main__":
    asyncio.run(main())
