"""
A.T.L.A.S. Real-time Performance Monitoring System
Advanced monitoring, alerting, and performance optimization
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid
import time
from collections import deque, defaultdict

# Core imports
from models import EngineStatus

# Monitoring and metrics imports (with graceful fallbacks)
try:
    import psutil
    import threading
    from concurrent.futures import ThreadPoolExecutor
    MONITORING_LIBS_AVAILABLE = True
except ImportError:
    MONITORING_LIBS_AVAILABLE = False

# Performance profiling imports
try:
    import cProfile
    import pstats
    import tracemalloc
    PROFILING_AVAILABLE = True
except ImportError:
    PROFILING_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# MONITORING MODELS
# ============================================================================

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """Types of metrics to monitor"""
    PERFORMANCE = "performance"
    ACCURACY = "accuracy"
    LATENCY = "latency"
    THROUGHPUT = "throughput"
    RESOURCE_USAGE = "resource_usage"
    ERROR_RATE = "error_rate"
    PREDICTION_QUALITY = "prediction_quality"

class MonitoringMode(Enum):
    """Monitoring operation modes"""
    PASSIVE = "passive"
    ACTIVE = "active"
    PREDICTIVE = "predictive"
    ADAPTIVE = "adaptive"

@dataclass
class PerformanceMetric:
    """Performance metric data point"""
    metric_id: str
    metric_type: MetricType
    component: str
    value: float
    unit: str
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Alert:
    """System alert"""
    alert_id: str
    severity: AlertSeverity
    component: str
    message: str
    metric_value: float
    threshold: float
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class SystemHealth:
    """Overall system health status"""
    overall_score: float  # 0.0 to 1.0
    component_scores: Dict[str, float]
    active_alerts: List[Alert]
    performance_trends: Dict[str, List[float]]
    resource_utilization: Dict[str, float]
    timestamp: datetime

@dataclass
class PerformanceProfile:
    """Performance profiling result"""
    profile_id: str
    component: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    function_calls: Dict[str, int]
    bottlenecks: List[str]
    recommendations: List[str]
    timestamp: datetime

# ============================================================================
# REAL-TIME MONITORING ENGINE
# ============================================================================

class AtlasRealtimeMonitor:
    """Real-time performance monitoring and alerting system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.monitoring_libs_available = MONITORING_LIBS_AVAILABLE
        self.profiling_available = PROFILING_AVAILABLE
        
        # Monitoring components
        self.metrics_buffer = defaultdict(lambda: deque(maxlen=1000))
        self.alerts = {}
        self.alert_handlers = {}
        self.performance_profiles = {}
        
        # Monitoring configuration
        self.monitoring_mode = MonitoringMode.ACTIVE
        self.collection_interval = 1.0  # seconds
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'error_rate': 0.05,
            'latency_ms': 1000.0,
            'accuracy_drop': 0.1
        }
        
        # Real-time tracking
        self.component_performance = {}
        self.system_health_history = deque(maxlen=100)
        self.prediction_quality_tracker = {}
        
        # Threading for continuous monitoring
        self.monitoring_thread = None
        self.monitoring_active = False
        self.executor = ThreadPoolExecutor(max_workers=4) if MONITORING_LIBS_AVAILABLE else None
        
        # Performance optimization
        self.optimization_suggestions = []
        self.auto_optimization_enabled = True
        
        logger.info(f"[MONITOR] Real-time Monitor initialized - libs: {self.monitoring_libs_available}, profiling: {self.profiling_available}")

    async def initialize(self):
        """Initialize real-time monitoring system"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if self.monitoring_libs_available:
                await self._initialize_system_monitoring()
                logger.info("[OK] System monitoring initialized")
            
            if self.profiling_available:
                await self._initialize_profiling()
                logger.info("[OK] Performance profiling initialized")
            
            # Initialize alert handlers
            await self._initialize_alert_handlers()
            
            # Start continuous monitoring
            await self._start_monitoring()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Real-time Monitor fully initialized")
            
        except Exception as e:
            logger.error(f"Real-time monitor initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_system_monitoring(self):
        """Initialize system resource monitoring"""
        try:
            # Initialize system metrics tracking
            self.system_metrics = {
                'cpu_percent': 0.0,
                'memory_percent': 0.0,
                'disk_usage': 0.0,
                'network_io': {'bytes_sent': 0, 'bytes_recv': 0},
                'process_count': 0
            }
            
            logger.info("[SYSTEM] System monitoring initialized")
            
        except Exception as e:
            logger.error(f"System monitoring initialization failed: {e}")
            self.monitoring_libs_available = False

    async def _initialize_profiling(self):
        """Initialize performance profiling"""
        try:
            # Start memory tracing
            if self.profiling_available:
                tracemalloc.start()
            
            # Initialize profiling components
            self.profiler = cProfile.Profile() if self.profiling_available else None
            
            logger.info("[PROFILING] Performance profiling initialized")
            
        except Exception as e:
            logger.error(f"Profiling initialization failed: {e}")
            self.profiling_available = False

    async def _initialize_alert_handlers(self):
        """Initialize alert handling system"""
        try:
            # Register default alert handlers
            self.alert_handlers = {
                AlertSeverity.INFO: self._handle_info_alert,
                AlertSeverity.WARNING: self._handle_warning_alert,
                AlertSeverity.ERROR: self._handle_error_alert,
                AlertSeverity.CRITICAL: self._handle_critical_alert
            }
            
            logger.info("[ALERTS] Alert handlers initialized")
            
        except Exception as e:
            logger.error(f"Alert handler initialization failed: {e}")
            raise

    async def _start_monitoring(self):
        """Start continuous monitoring thread"""
        try:
            if not self.monitoring_libs_available:
                logger.warning("[MONITOR] Monitoring libraries not available - using fallback mode")
                return
            
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            logger.info("[MONITOR] Continuous monitoring started")
            
        except Exception as e:
            logger.error(f"Monitoring thread start failed: {e}")

    def _monitoring_loop(self):
        """Main monitoring loop (runs in separate thread)"""
        try:
            while self.monitoring_active:
                try:
                    # Collect system metrics
                    self._collect_system_metrics()
                    
                    # Check alert conditions
                    self._check_alert_conditions()
                    
                    # Update system health
                    self._update_system_health()
                    
                    # Sleep until next collection
                    time.sleep(self.collection_interval)
                    
                except Exception as e:
                    logger.error(f"Monitoring loop error: {e}")
                    time.sleep(self.collection_interval)
                    
        except Exception as e:
            logger.error(f"Monitoring loop failed: {e}")

    def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            if not self.monitoring_libs_available:
                return
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self._record_metric('cpu_usage', MetricType.RESOURCE_USAGE, 'system', cpu_percent, '%')
            
            # Memory usage
            memory = psutil.virtual_memory()
            self._record_metric('memory_usage', MetricType.RESOURCE_USAGE, 'system', memory.percent, '%')
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self._record_metric('disk_usage', MetricType.RESOURCE_USAGE, 'system', disk_percent, '%')
            
            # Network I/O
            net_io = psutil.net_io_counters()
            self._record_metric('network_bytes_sent', MetricType.THROUGHPUT, 'system', net_io.bytes_sent, 'bytes')
            self._record_metric('network_bytes_recv', MetricType.THROUGHPUT, 'system', net_io.bytes_recv, 'bytes')
            
            # Process count
            process_count = len(psutil.pids())
            self._record_metric('process_count', MetricType.RESOURCE_USAGE, 'system', process_count, 'count')
            
        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")

    def _record_metric(self, metric_id: str, metric_type: MetricType, component: str, 
                      value: float, unit: str, metadata: Dict[str, Any] = None):
        """Record a performance metric"""
        try:
            metric = PerformanceMetric(
                metric_id=metric_id,
                metric_type=metric_type,
                component=component,
                value=value,
                unit=unit,
                timestamp=datetime.now(),
                metadata=metadata or {}
            )
            
            # Add to buffer
            self.metrics_buffer[metric_id].append(metric)
            
            # Update component performance tracking
            if component not in self.component_performance:
                self.component_performance[component] = {}
            
            self.component_performance[component][metric_id] = {
                'current_value': value,
                'last_updated': datetime.now(),
                'trend': self._calculate_trend(metric_id)
            }
            
        except Exception as e:
            logger.error(f"Metric recording failed: {e}")

    def _calculate_trend(self, metric_id: str) -> str:
        """Calculate trend for metric"""
        try:
            if metric_id not in self.metrics_buffer or len(self.metrics_buffer[metric_id]) < 5:
                return 'stable'
            
            recent_values = [m.value for m in list(self.metrics_buffer[metric_id])[-5:]]
            
            # Simple trend calculation
            if recent_values[-1] > recent_values[0] * 1.1:
                return 'increasing'
            elif recent_values[-1] < recent_values[0] * 0.9:
                return 'decreasing'
            else:
                return 'stable'
                
        except Exception as e:
            logger.error(f"Trend calculation failed: {e}")
            return 'unknown'

    def _check_alert_conditions(self):
        """Check for alert conditions"""
        try:
            current_time = datetime.now()
            
            # Check each metric against thresholds
            for metric_id, threshold in self.alert_thresholds.items():
                if metric_id in self.metrics_buffer and self.metrics_buffer[metric_id]:
                    latest_metric = self.metrics_buffer[metric_id][-1]
                    
                    # Check if threshold is exceeded
                    if self._should_alert(latest_metric.value, threshold, metric_id):
                        severity = self._determine_alert_severity(latest_metric.value, threshold, metric_id)
                        
                        alert = Alert(
                            alert_id=str(uuid.uuid4()),
                            severity=severity,
                            component=latest_metric.component,
                            message=f"{metric_id} exceeded threshold: {latest_metric.value:.2f} > {threshold}",
                            metric_value=latest_metric.value,
                            threshold=threshold,
                            timestamp=current_time
                        )
                        
                        # Store and handle alert
                        self.alerts[alert.alert_id] = alert
                        self._handle_alert(alert)
            
        except Exception as e:
            logger.error(f"Alert condition checking failed: {e}")

    def _should_alert(self, value: float, threshold: float, metric_id: str) -> bool:
        """Determine if an alert should be triggered"""
        try:
            # Different logic for different metrics
            if metric_id in ['cpu_usage', 'memory_usage', 'disk_usage']:
                return value > threshold
            elif metric_id == 'error_rate':
                return value > threshold
            elif metric_id == 'latency_ms':
                return value > threshold
            elif metric_id == 'accuracy_drop':
                return value > threshold
            else:
                return value > threshold
                
        except Exception as e:
            logger.error(f"Alert condition evaluation failed: {e}")
            return False

    def _determine_alert_severity(self, value: float, threshold: float, metric_id: str) -> AlertSeverity:
        """Determine alert severity based on how much threshold is exceeded"""
        try:
            if metric_id in ['cpu_usage', 'memory_usage']:
                if value > threshold * 1.2:  # 20% over threshold
                    return AlertSeverity.CRITICAL
                elif value > threshold * 1.1:  # 10% over threshold
                    return AlertSeverity.ERROR
                else:
                    return AlertSeverity.WARNING
            else:
                # Default severity logic
                if value > threshold * 1.5:
                    return AlertSeverity.CRITICAL
                elif value > threshold * 1.2:
                    return AlertSeverity.ERROR
                else:
                    return AlertSeverity.WARNING
                    
        except Exception as e:
            logger.error(f"Alert severity determination failed: {e}")
            return AlertSeverity.WARNING

    def _handle_alert(self, alert: Alert):
        """Handle an alert based on its severity"""
        try:
            handler = self.alert_handlers.get(alert.severity)
            if handler:
                handler(alert)
            else:
                logger.warning(f"No handler for alert severity: {alert.severity}")
                
        except Exception as e:
            logger.error(f"Alert handling failed: {e}")

    async def _handle_info_alert(self, alert: Alert):
        """Handle info-level alerts"""
        logger.info(f"[INFO ALERT] {alert.component}: {alert.message}")

    async def _handle_warning_alert(self, alert: Alert):
        """Handle warning-level alerts"""
        logger.warning(f"[WARNING ALERT] {alert.component}: {alert.message}")
        
        # Auto-optimization for warnings
        if self.auto_optimization_enabled:
            await self._suggest_optimization(alert)

    async def _handle_error_alert(self, alert: Alert):
        """Handle error-level alerts"""
        logger.error(f"[ERROR ALERT] {alert.component}: {alert.message}")
        
        # More aggressive optimization for errors
        if self.auto_optimization_enabled:
            await self._apply_optimization(alert)

    async def _handle_critical_alert(self, alert: Alert):
        """Handle critical-level alerts"""
        logger.critical(f"[CRITICAL ALERT] {alert.component}: {alert.message}")
        
        # Emergency optimization for critical alerts
        if self.auto_optimization_enabled:
            await self._emergency_optimization(alert)

    async def _suggest_optimization(self, alert: Alert):
        """Suggest optimization for warning alerts"""
        try:
            suggestion = f"Consider optimizing {alert.component} - {alert.message}"
            self.optimization_suggestions.append({
                'suggestion': suggestion,
                'alert_id': alert.alert_id,
                'timestamp': datetime.now(),
                'applied': False
            })
            
        except Exception as e:
            logger.error(f"Optimization suggestion failed: {e}")

    async def _apply_optimization(self, alert: Alert):
        """Apply optimization for error alerts"""
        try:
            # Implement specific optimizations based on alert type
            if 'memory_usage' in alert.message:
                await self._optimize_memory()
            elif 'cpu_usage' in alert.message:
                await self._optimize_cpu()
            elif 'latency' in alert.message:
                await self._optimize_latency()
                
        except Exception as e:
            logger.error(f"Optimization application failed: {e}")

    async def _emergency_optimization(self, alert: Alert):
        """Apply emergency optimization for critical alerts"""
        try:
            logger.critical(f"Applying emergency optimization for: {alert.message}")
            
            # Emergency measures
            await self._reduce_system_load()
            await self._clear_caches()
            await self._restart_problematic_components()
            
        except Exception as e:
            logger.error(f"Emergency optimization failed: {e}")

    async def _optimize_memory(self):
        """Optimize memory usage"""
        try:
            # Clear metric buffers if too large
            for metric_id in self.metrics_buffer:
                if len(self.metrics_buffer[metric_id]) > 500:
                    # Keep only recent 250 entries
                    recent_entries = list(self.metrics_buffer[metric_id])[-250:]
                    self.metrics_buffer[metric_id].clear()
                    self.metrics_buffer[metric_id].extend(recent_entries)
            
            logger.info("[OPTIMIZATION] Memory optimization applied")
            
        except Exception as e:
            logger.error(f"Memory optimization failed: {e}")

    async def _optimize_cpu(self):
        """Optimize CPU usage"""
        try:
            # Reduce monitoring frequency temporarily
            original_interval = self.collection_interval
            self.collection_interval = min(self.collection_interval * 2, 5.0)
            
            logger.info(f"[OPTIMIZATION] CPU optimization applied - monitoring interval: {original_interval} -> {self.collection_interval}")
            
        except Exception as e:
            logger.error(f"CPU optimization failed: {e}")

    async def _optimize_latency(self):
        """Optimize system latency"""
        try:
            # Switch to passive monitoring mode temporarily
            original_mode = self.monitoring_mode
            self.monitoring_mode = MonitoringMode.PASSIVE
            
            logger.info(f"[OPTIMIZATION] Latency optimization applied - mode: {original_mode} -> {self.monitoring_mode}")
            
        except Exception as e:
            logger.error(f"Latency optimization failed: {e}")

    async def _reduce_system_load(self):
        """Reduce overall system load"""
        try:
            # Reduce monitoring frequency significantly
            self.collection_interval = 10.0
            
            # Switch to passive mode
            self.monitoring_mode = MonitoringMode.PASSIVE
            
            logger.critical("[EMERGENCY] System load reduction applied")
            
        except Exception as e:
            logger.error(f"System load reduction failed: {e}")

    async def _clear_caches(self):
        """Clear system caches"""
        try:
            # Clear metrics buffer
            for metric_id in self.metrics_buffer:
                self.metrics_buffer[metric_id].clear()
            
            # Clear performance profiles
            self.performance_profiles.clear()
            
            # Clear optimization suggestions
            self.optimization_suggestions.clear()
            
            logger.critical("[EMERGENCY] System caches cleared")
            
        except Exception as e:
            logger.error(f"Cache clearing failed: {e}")

    async def _restart_problematic_components(self):
        """Restart problematic components"""
        try:
            # This would restart specific components in a real implementation
            logger.critical("[EMERGENCY] Component restart initiated")
            
        except Exception as e:
            logger.error(f"Component restart failed: {e}")

    def _update_system_health(self):
        """Update overall system health status"""
        try:
            current_time = datetime.now()
            
            # Calculate component scores
            component_scores = {}
            for component, metrics in self.component_performance.items():
                score = self._calculate_component_health_score(component, metrics)
                component_scores[component] = score
            
            # Calculate overall score
            if component_scores:
                overall_score = np.mean(list(component_scores.values()))
            else:
                overall_score = 1.0  # Default healthy score
            
            # Get active alerts
            active_alerts = [alert for alert in self.alerts.values() if not alert.resolved]
            
            # Calculate performance trends
            performance_trends = {}
            for metric_id in self.metrics_buffer:
                if len(self.metrics_buffer[metric_id]) >= 5:
                    recent_values = [m.value for m in list(self.metrics_buffer[metric_id])[-5:]]
                    performance_trends[metric_id] = recent_values
            
            # Get resource utilization
            resource_utilization = {}
            if self.monitoring_libs_available:
                try:
                    resource_utilization = {
                        'cpu': psutil.cpu_percent(),
                        'memory': psutil.virtual_memory().percent,
                        'disk': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100
                    }
                except:
                    resource_utilization = {'cpu': 0, 'memory': 0, 'disk': 0}
            
            # Create system health status
            health_status = SystemHealth(
                overall_score=overall_score,
                component_scores=component_scores,
                active_alerts=active_alerts,
                performance_trends=performance_trends,
                resource_utilization=resource_utilization,
                timestamp=current_time
            )
            
            # Add to history
            self.system_health_history.append(health_status)
            
        except Exception as e:
            logger.error(f"System health update failed: {e}")

    def _calculate_component_health_score(self, component: str, metrics: Dict[str, Any]) -> float:
        """Calculate health score for a component"""
        try:
            scores = []
            
            for metric_id, metric_data in metrics.items():
                value = metric_data['current_value']
                
                # Score based on metric type and value
                if metric_id in ['cpu_usage', 'memory_usage']:
                    # Lower is better for resource usage
                    score = max(0.0, 1.0 - (value / 100.0))
                elif metric_id == 'error_rate':
                    # Lower is better for error rate
                    score = max(0.0, 1.0 - (value * 10))  # Assuming error rate is 0-1
                elif metric_id.endswith('_latency'):
                    # Lower is better for latency (assuming ms)
                    score = max(0.0, 1.0 - (value / 1000.0))
                else:
                    # Default scoring
                    score = 0.8
                
                scores.append(score)
            
            return np.mean(scores) if scores else 1.0
            
        except Exception as e:
            logger.error(f"Component health score calculation failed: {e}")
            return 0.5

    async def record_prediction_quality(self, component: str, prediction_id: str, 
                                      accuracy: float, confidence: float):
        """Record prediction quality metrics"""
        try:
            if component not in self.prediction_quality_tracker:
                self.prediction_quality_tracker[component] = deque(maxlen=100)
            
            quality_record = {
                'prediction_id': prediction_id,
                'accuracy': accuracy,
                'confidence': confidence,
                'timestamp': datetime.now()
            }
            
            self.prediction_quality_tracker[component].append(quality_record)
            
            # Record as metric
            self._record_metric(f'{component}_accuracy', MetricType.ACCURACY, component, accuracy, 'score')
            self._record_metric(f'{component}_confidence', MetricType.PREDICTION_QUALITY, component, confidence, 'score')
            
        except Exception as e:
            logger.error(f"Prediction quality recording failed: {e}")

    async def profile_component_performance(self, component: str, function: Callable, 
                                          *args, **kwargs) -> PerformanceProfile:
        """Profile performance of a component function"""
        try:
            if not self.profiling_available:
                # Fallback timing
                start_time = time.time()
                result = await function(*args, **kwargs) if asyncio.iscoroutinefunction(function) else function(*args, **kwargs)
                execution_time = time.time() - start_time
                
                return PerformanceProfile(
                    profile_id=str(uuid.uuid4()),
                    component=component,
                    execution_time=execution_time,
                    memory_usage=0.0,
                    cpu_usage=0.0,
                    function_calls={},
                    bottlenecks=[],
                    recommendations=[],
                    timestamp=datetime.now()
                )
            
            # Full profiling with cProfile
            profiler = cProfile.Profile()
            
            # Start profiling
            profiler.enable()
            start_memory = tracemalloc.get_traced_memory()[0]
            start_time = time.time()
            
            # Execute function
            result = await function(*args, **kwargs) if asyncio.iscoroutinefunction(function) else function(*args, **kwargs)
            
            # Stop profiling
            execution_time = time.time() - start_time
            end_memory = tracemalloc.get_traced_memory()[0]
            profiler.disable()
            
            # Analyze profiling results
            stats = pstats.Stats(profiler)
            
            # Extract function calls
            function_calls = {}
            for func_info, (cc, nc, tt, ct, callers) in stats.stats.items():
                func_name = f"{func_info[2]}:{func_info[1]}"
                function_calls[func_name] = nc
            
            # Identify bottlenecks (top 3 time-consuming functions)
            sorted_funcs = sorted(stats.stats.items(), key=lambda x: x[1][3], reverse=True)
            bottlenecks = [f"{func[0][2]}:{func[0][1]}" for func in sorted_funcs[:3]]
            
            # Generate recommendations
            recommendations = []
            if execution_time > 1.0:
                recommendations.append("Consider optimizing execution time")
            if end_memory - start_memory > 100 * 1024 * 1024:  # 100MB
                recommendations.append("Consider optimizing memory usage")
            
            profile = PerformanceProfile(
                profile_id=str(uuid.uuid4()),
                component=component,
                execution_time=execution_time,
                memory_usage=(end_memory - start_memory) / (1024 * 1024),  # MB
                cpu_usage=0.0,  # Would need additional measurement
                function_calls=function_calls,
                bottlenecks=bottlenecks,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            self.performance_profiles[profile.profile_id] = profile
            
            return profile
            
        except Exception as e:
            logger.error(f"Performance profiling failed for {component}: {e}")
            raise

    def get_system_health(self) -> Optional[SystemHealth]:
        """Get current system health status"""
        try:
            if self.system_health_history:
                return self.system_health_history[-1]
            return None
            
        except Exception as e:
            logger.error(f"System health retrieval failed: {e}")
            return None

    def get_component_metrics(self, component: str, hours_back: int = 1) -> Dict[str, List[PerformanceMetric]]:
        """Get metrics for a specific component"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            component_metrics = {}
            
            for metric_id, metrics in self.metrics_buffer.items():
                filtered_metrics = [
                    m for m in metrics 
                    if m.component == component and m.timestamp >= cutoff_time
                ]
                if filtered_metrics:
                    component_metrics[metric_id] = filtered_metrics
            
            return component_metrics
            
        except Exception as e:
            logger.error(f"Component metrics retrieval failed: {e}")
            return {}

    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get active alerts, optionally filtered by severity"""
        try:
            active_alerts = [alert for alert in self.alerts.values() if not alert.resolved]
            
            if severity:
                active_alerts = [alert for alert in active_alerts if alert.severity == severity]
            
            return sorted(active_alerts, key=lambda x: x.timestamp, reverse=True)
            
        except Exception as e:
            logger.error(f"Active alerts retrieval failed: {e}")
            return []

    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an alert"""
        try:
            if alert_id in self.alerts:
                self.alerts[alert_id].resolved = True
                self.alerts[alert_id].resolution_time = datetime.now()
                return True
            return False
            
        except Exception as e:
            logger.error(f"Alert resolution failed: {e}")
            return False

    def get_optimization_suggestions(self) -> List[Dict[str, Any]]:
        """Get optimization suggestions"""
        try:
            return self.optimization_suggestions.copy()
            
        except Exception as e:
            logger.error(f"Optimization suggestions retrieval failed: {e}")
            return []

    def get_engine_status(self) -> Dict[str, Any]:
        """Get monitoring engine status"""
        return {
            'status': self.status.value,
            'monitoring_libs_available': self.monitoring_libs_available,
            'profiling_available': self.profiling_available,
            'monitoring_active': self.monitoring_active,
            'monitoring_mode': self.monitoring_mode.value,
            'collection_interval': self.collection_interval,
            'metrics_tracked': len(self.metrics_buffer),
            'active_alerts_count': len([a for a in self.alerts.values() if not a.resolved]),
            'performance_profiles_count': len(self.performance_profiles),
            'auto_optimization_enabled': self.auto_optimization_enabled,
            'system_health_available': len(self.system_health_history) > 0
        }

    def stop_monitoring(self):
        """Stop continuous monitoring"""
        try:
            self.monitoring_active = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5.0)
            
            if self.executor:
                self.executor.shutdown(wait=True)
            
            logger.info("[MONITOR] Monitoring stopped")
            
        except Exception as e:
            logger.error(f"Monitoring stop failed: {e}")

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasRealtimeMonitor",
    "PerformanceMetric",
    "Alert",
    "SystemHealth",
    "PerformanceProfile",
    "AlertSeverity",
    "MetricType",
    "MonitoringMode"
]
