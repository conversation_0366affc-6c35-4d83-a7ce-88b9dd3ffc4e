"""
A.T.L.A.S AI Trading System - Data Models and Schemas
Pydantic models for type safety and validation
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator


# Enums for type safety
class OrderSide(str, Enum):
    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"  # OCO (One-Cancels-Other) bracket orders
    TRAILING_STOP = "trailing_stop"
    FILL_OR_KILL = "fill_or_kill"
    IMMEDIATE_OR_CANCEL = "immediate_or_cancel"


class OrderStatus(str, Enum):
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"


class EngineStatus(str, Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEGRADED = "degraded"
    FAILED = "failed"
    SHUTTING_DOWN = "shutting_down"


class SignalStrength(str, Enum):
    VERY_WEAK = "very_weak"
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


# Core Data Models
class Quote(BaseModel):
    """Real-time market quote"""
    symbol: str
    price: float
    change: Optional[float] = 0.0
    change_percent: Optional[float] = 0.0
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    timestamp: datetime

    @field_validator('price')
    @classmethod
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v


class Position(BaseModel):
    """Trading position"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float = 0.0
    side: OrderSide
    timestamp: datetime


class Order(BaseModel):
    """Trading order"""
    id: Optional[str] = None
    symbol: str
    quantity: float
    side: OrderSide
    type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.NEW
    filled_quantity: float = 0.0
    timestamp: datetime
    
    @field_validator('quantity')
    @classmethod
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('Quantity must be positive')
        return v


class TechnicalIndicators(BaseModel):
    """Technical analysis indicators"""
    symbol: str
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    atr: Optional[float] = None
    volume_sma: Optional[float] = None
    timestamp: datetime


class TTMSqueezeSignal(BaseModel):
    """TTM Squeeze trading signal"""
    symbol: str
    signal_strength: SignalStrength
    histogram_value: float
    squeeze_active: bool
    momentum_direction: str  # "bullish", "bearish", "neutral"
    confidence: float = Field(ge=0.0, le=1.0)
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    target_price: Optional[float] = None
    timestamp: datetime


class RiskAssessment(BaseModel):
    """Risk management assessment"""
    symbol: str
    risk_score: float = Field(ge=0.0, le=10.0)
    position_size: float
    stop_loss_price: float
    risk_amount: float
    risk_percentage: float
    confidence_level: float = Field(ge=0.0, le=1.0)
    risk_factors: List[str] = []
    recommendations: List[str] = []
    timestamp: datetime


class MarketContext(BaseModel):
    """Market context and sentiment"""
    overall_sentiment: str  # "bullish", "bearish", "neutral"
    vix_level: Optional[float] = None
    market_trend: Optional[str] = None
    sector_rotation: Optional[Dict[str, float]] = None
    news_sentiment: Optional[float] = None
    economic_indicators: Optional[Dict[str, Any]] = None
    timestamp: datetime


# API Request/Response Models
class ChatRequest(BaseModel):
    """Chat message request"""
    message: str = Field(min_length=1, max_length=2000)
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class AIResponse(BaseModel):
    """AI response model"""
    response: str
    type: str = "chat"  # chat, analysis, error, system_status
    confidence: float = Field(ge=0.0, le=1.0, default=0.8)
    context: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class AnalysisRequest(BaseModel):
    """Market analysis request"""
    symbol: str = Field(pattern=r'^[A-Z]{1,5}$')
    timeframe: str = Field(default="1day", pattern=r'^(1min|5min|15min|30min|1hour|1day)$')
    include_technical: bool = True
    include_sentiment: bool = True
    include_risk: bool = True


class EducationRequest(BaseModel):
    """Educational query request"""
    question: str = Field(min_length=1, max_length=1000)
    topic: Optional[str] = None
    difficulty_level: str = Field(default="beginner", pattern=r'^(beginner|intermediate|advanced)$')
    book_filter: Optional[str] = None


class SystemStatus(BaseModel):
    """System health and status"""
    status: str  # healthy, degraded, initializing, failed
    timestamp: datetime
    version: str = "4.0.0"
    engines: Dict[str, EngineStatus]
    initialization_progress: Optional[Dict[str, float]] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None


class PortfolioSummary(BaseModel):
    """Portfolio summary"""
    total_value: float
    cash_balance: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    day_change: float
    day_change_percent: float
    positions: List[Position]
    timestamp: datetime


class ScanResult(BaseModel):
    """Market scan result"""
    symbol: str
    signal_type: str
    signal_strength: SignalStrength
    score: float = Field(ge=0.0, le=10.0)
    price: float
    volume: Optional[int] = None
    indicators: Optional[Dict[str, float]] = None
    reasoning: Optional[str] = None
    timestamp: datetime


class PredictoForecast(BaseModel):
    """Predicto AI forecast"""
    symbol: str
    forecast_days: int
    predicted_price: float
    confidence: float = Field(ge=0.0, le=1.0)
    price_targets: Optional[Dict[str, float]] = None
    risk_factors: Optional[List[str]] = None
    sentiment_score: Optional[float] = None
    timestamp: datetime


# Configuration Models
class UserProfile(BaseModel):
    """User trading profile"""
    experience_level: str = Field(default="beginner", pattern=r'^(beginner|intermediate|advanced|expert)$')
    risk_tolerance: str = Field(default="moderate", pattern=r'^(conservative|moderate|aggressive)$')
    account_size: float = Field(default=10000.0, ge=1000.0)
    communication_style: str = Field(default="mentor", pattern=r'^(mentor|professional|casual)$')
    trading_goals: Optional[List[str]] = None
    preferred_timeframes: Optional[List[str]] = None


class InitializationStatus(BaseModel):
    """System initialization tracking"""
    component: str
    status: EngineStatus
    progress: float = Field(ge=0.0, le=1.0)
    message: Optional[str] = None
    error: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None


# Export all models
# Enhanced Models for Advanced Features

class AlertPriority(str, Enum):
    """Alert priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(str, Enum):
    """Types of proactive alerts"""
    MORNING_BRIEFING = "morning_briefing"
    OPPORTUNITY_NOTIFICATION = "opportunity_notification"
    MARKET_PROTECTION = "market_protection"
    TIME_SENSITIVE_SETUP = "time_sensitive_setup"
    EARNINGS_WARNING = "earnings_warning"
    VOLATILITY_ALERT = "volatility_alert"


class OptionType(str, Enum):
    """Option types"""
    CALL = "call"
    PUT = "put"


class StrategyType(str, Enum):
    """Options strategy types"""
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    COVERED_CALL = "covered_call"
    CASH_SECURED_PUT = "cash_secured_put"
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    IRON_CONDOR = "iron_condor"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    BUTTERFLY = "butterfly"


class CommunicationMode(str, Enum):
    """AI communication styles"""
    MENTOR = "mentor"
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    AGGRESSIVE = "aggressive"


class EmotionalState(str, Enum):
    """User emotional states"""
    CONFIDENT = "confident"
    ANXIOUS = "anxious"
    EXCITED = "excited"
    FRUSTRATED = "frustrated"
    NEUTRAL = "neutral"
    CURIOUS = "curious"


# Advanced Data Models

class ProactiveAlert(BaseModel):
    """Proactive alert data structure"""
    alert_type: AlertType
    priority: AlertPriority
    title: str
    message: str
    action_required: bool
    expiry_time: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)


class SentimentData(BaseModel):
    """Sentiment analysis result"""
    source: str  # 'news', 'reddit', 'twitter'
    symbol: str
    text: str
    sentiment_score: float  # -1 to 1
    confidence: float
    timestamp: datetime
    url: Optional[str] = None


class SentimentSignal(BaseModel):
    """Aggregated sentiment signal"""
    symbol: str
    overall_sentiment: float  # -1 to 1
    confidence: float
    signal_strength: SignalStrength
    source_count: int
    bullish_signals: int
    bearish_signals: int
    neutral_signals: int
    implied_volatility: Optional[float] = None
    should_hedge: bool = False
    hedge_symbol: Optional[str] = None
    timestamp: datetime


class MLPredictionResult(BaseModel):
    """ML prediction result"""
    symbol: str
    predicted_return: float
    confidence: float
    signal_strength: SignalStrength
    features_used: List[str]
    model_type: str = "LSTM"
    timestamp: datetime


class OptionContract(BaseModel):
    """Option contract details"""
    symbol: str
    underlying: str
    option_type: OptionType
    strike: float
    expiration: datetime
    bid: float
    ask: float
    last: float
    volume: int
    open_interest: int
    implied_volatility: float
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    rho: Optional[float] = None


class OptionsStrategy(BaseModel):
    """Options trading strategy"""
    strategy_type: StrategyType
    underlying_symbol: str
    contracts: List[OptionContract]
    max_profit: Optional[float] = None
    max_loss: Optional[float] = None
    breakeven_points: List[float] = Field(default_factory=list)
    probability_of_profit: Optional[float] = None
    risk_reward_ratio: Optional[float] = None
    margin_requirement: Optional[float] = None


class TradingGoal(BaseModel):
    """User trading goal with context"""
    goal_type: str  # 'profit_target', 'risk_limit', 'learning', 'strategy_test'
    target_amount: Optional[float] = None
    timeframe: Optional[str] = None  # 'today', 'this_week', 'by_friday'
    risk_tolerance: str = 'moderate'  # 'conservative', 'moderate', 'aggressive'
    priority: int = 1  # 1=highest, 5=lowest
    created_at: datetime = Field(default_factory=datetime.now)
    status: str = 'active'  # 'active', 'completed', 'cancelled'
    progress: float = 0.0  # 0.0 to 1.0


# ============================================================================
# COMPREHENSIVE TRADING PLAN MODELS
# ============================================================================

class TradingPlanTarget(BaseModel):
    """Financial target and timeline for trading plan"""
    target_profit: float = Field(description="Target profit amount in dollars")
    target_timeframe_days: int = Field(description="Target timeframe in days")
    risk_tolerance_percent: float = Field(ge=0.0, le=100.0, description="Risk tolerance as percentage of capital")
    starting_capital: float = Field(description="Starting capital amount")
    max_drawdown_percent: float = Field(ge=0.0, le=100.0, description="Maximum acceptable drawdown percentage")
    created_at: datetime = Field(default_factory=datetime.now)


class TradingOpportunity(BaseModel):
    """Individual trading opportunity within a plan"""
    symbol: str = Field(description="Stock symbol")
    company_name: str = Field(description="Company name")
    current_price: float = Field(description="Current market price")
    entry_price: float = Field(description="Recommended entry price")
    exit_target_price: float = Field(description="Primary exit target price")
    stop_loss_price: float = Field(description="Stop loss price")
    position_size_shares: int = Field(description="Number of shares to trade")
    capital_allocation: float = Field(description="Capital allocated to this trade")
    trade_type: str = Field(description="Type of trade (stock, options, etc.)")
    options_details: Optional[Dict[str, Any]] = Field(None, description="Options contract details if applicable")

    # Analysis & Methodology
    primary_method: str = Field(description="Primary analysis method (Lee Method, TTM Squeeze, etc.)")
    timeframe_analyzed: str = Field(description="Timeframe analyzed (daily, weekly, intraday)")
    confidence_score: float = Field(ge=0.0, le=100.0, description="Confidence score based on A.T.L.A.S. models")
    supporting_indicators: List[str] = Field(description="Supporting technical indicators")
    confluence_factors: List[str] = Field(description="Confluence factors supporting the trade")

    # Risk/Reward Analysis
    max_profit_dollars: float = Field(description="Maximum potential profit in dollars")
    max_profit_percent: float = Field(description="Maximum potential profit as percentage")
    max_loss_dollars: float = Field(description="Maximum potential loss in dollars")
    max_loss_percent: float = Field(description="Maximum potential loss as percentage")
    risk_reward_ratio: float = Field(description="Risk to reward ratio")
    success_probability: float = Field(ge=0.0, le=100.0, description="Probability of success from backtesting")

    # Timing & Execution
    recommended_entry_time: Optional[datetime] = Field(None, description="Recommended entry timing in Central Time")
    market_session: str = Field(description="Market session (pre-market, regular, after-hours)")
    trade_duration_type: str = Field(description="Duration type (day trade, swing, position)")
    estimated_hold_days: int = Field(description="Estimated holding period in days")

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class PortfolioIntegration(BaseModel):
    """Portfolio integration analysis for trading plan"""
    total_portfolio_risk: float = Field(description="Total portfolio risk exposure percentage")
    correlation_analysis: Dict[str, float] = Field(description="Correlation with other positions")
    diversification_score: float = Field(ge=0.0, le=100.0, description="Portfolio diversification score")
    sector_allocation: Dict[str, float] = Field(description="Sector allocation percentages")
    risk_concentration: Dict[str, float] = Field(description="Risk concentration by position")
    recommended_adjustments: List[str] = Field(description="Recommended portfolio adjustments")


class TradingPlanScenario(BaseModel):
    """Alternative scenario for trading plan"""
    scenario_name: str = Field(description="Name of the scenario")
    scenario_type: str = Field(description="Type (optimistic, pessimistic, alternative)")
    modified_targets: Dict[str, Any] = Field(description="Modified targets for this scenario")
    probability: float = Field(ge=0.0, le=100.0, description="Probability of this scenario")
    required_adjustments: List[str] = Field(description="Required adjustments for this scenario")


class TradingPlanMonitoring(BaseModel):
    """Monitoring and adjustment triggers for trading plan"""
    daily_checkpoints: List[str] = Field(description="Daily monitoring checkpoints")
    adjustment_triggers: List[Dict[str, Any]] = Field(description="Conditions that trigger plan adjustments")
    performance_metrics: Dict[str, float] = Field(description="Key performance metrics to track")
    alert_conditions: List[Dict[str, Any]] = Field(description="Conditions that trigger alerts")
    review_schedule: Dict[str, str] = Field(description="Scheduled review points")


class ComprehensiveTradingPlan(BaseModel):
    """Complete actionable trading plan"""
    plan_id: str = Field(description="Unique plan identifier")
    plan_name: str = Field(description="Descriptive name for the plan")

    # Financial Target & Timeline
    target: TradingPlanTarget = Field(description="Financial targets and timeline")

    # Trading Opportunities
    opportunities: List[TradingOpportunity] = Field(description="List of trading opportunities")

    # Portfolio Integration
    portfolio_integration: PortfolioIntegration = Field(description="Portfolio integration analysis")

    # Alternative Scenarios
    scenarios: List[TradingPlanScenario] = Field(description="Alternative scenarios")

    # Monitoring & Execution
    monitoring: TradingPlanMonitoring = Field(description="Monitoring and adjustment framework")

    # Plan Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    status: str = Field(default="active", description="Plan status (active, completed, cancelled)")
    success_rate: Optional[float] = Field(None, description="Historical success rate of similar plans")
    total_expected_return: float = Field(description="Total expected return in dollars")
    total_risk_amount: float = Field(description="Total amount at risk")
    plan_confidence: float = Field(ge=0.0, le=100.0, description="Overall plan confidence score")


class TradingPlanAlert(BaseModel):
    """Alert related to trading plan execution"""
    alert_id: str = Field(description="Unique alert identifier")
    plan_id: str = Field(description="Associated trading plan ID")
    opportunity_symbol: Optional[str] = Field(None, description="Associated symbol if applicable")
    alert_type: str = Field(description="Type of alert (entry, exit, adjustment, risk)")
    priority: AlertPriority = Field(description="Alert priority level")
    message: str = Field(description="Alert message")
    action_required: bool = Field(description="Whether immediate action is required")
    suggested_actions: List[str] = Field(description="Suggested actions to take")
    expires_at: Optional[datetime] = Field(None, description="Alert expiration time")
    created_at: datetime = Field(default_factory=datetime.now)
    acknowledged: bool = Field(default=False, description="Whether alert has been acknowledged")


class TradingPlanExecution(BaseModel):
    """Execution tracking for trading plan"""
    execution_id: str = Field(description="Unique execution identifier")
    plan_id: str = Field(description="Associated trading plan ID")
    opportunity_symbol: str = Field(description="Symbol being executed")
    execution_type: str = Field(description="Type of execution (entry, exit, adjustment)")
    executed_price: float = Field(description="Actual execution price")
    executed_quantity: int = Field(description="Actual quantity executed")
    execution_time: datetime = Field(description="Execution timestamp in Central Time")
    execution_status: str = Field(description="Status (pending, filled, partial, cancelled)")
    slippage: float = Field(description="Price slippage from target")
    commission: float = Field(description="Commission paid")
    net_result: Optional[float] = Field(None, description="Net result if position closed")
    notes: Optional[str] = Field(None, description="Execution notes")
    created_at: datetime = Field(default_factory=datetime.now)


class ContextMemory(BaseModel):
    """Enhanced context memory entry"""
    session_id: str
    context_type: str
    context_data: str
    emotional_state: Optional[EmotionalState] = None
    confidence_score: float = 0.5
    timestamp: datetime = Field(default_factory=datetime.now)


class PerformanceMetrics(BaseModel):
    """Performance metrics tracking"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


__all__ = [
    # Enums
    "OrderSide", "OrderType", "OrderStatus", "EngineStatus", "SignalStrength",
    "AlertPriority", "AlertType", "OptionType", "StrategyType", "CommunicationMode", "EmotionalState",

    # Core Models
    "Quote", "Position", "Order", "TechnicalIndicators", "TTMSqueezeSignal",
    "RiskAssessment", "MarketContext",

    # API Models
    "ChatRequest", "AIResponse", "AnalysisRequest", "EducationRequest",
    "SystemStatus", "PortfolioSummary", "ScanResult", "PredictoForecast",

    # Configuration Models
    "UserProfile", "InitializationStatus",

    # Advanced Models
    "ProactiveAlert", "SentimentData", "SentimentSignal", "MLPredictionResult",
    "OptionContract", "OptionsStrategy", "TradingGoal", "ContextMemory", "PerformanceMetrics",

    # Comprehensive Trading Plan Models
    "TradingPlanTarget", "TradingOpportunity", "PortfolioIntegration", "TradingPlanScenario",
    "TradingPlanMonitoring", "ComprehensiveTradingPlan", "TradingPlanAlert", "TradingPlanExecution"
]
