"""
Test script for the A.T.L.A.S. Real-Time Alert System
"""

import asyncio
import logging
import json
from datetime import datetime
from atlas_alert_engine import alert_engine, AlertSignal, SignalType, AlertPriority
from atlas_alert_delivery import AlertDeliveryManager
from atlas_signal_classifier import signal_classifier

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_alert_system():
    """Test the complete alert system"""
    
    print("🚀 Testing A.T.L.A.S. Real-Time Alert System")
    print("=" * 60)
    
    # Initialize alert delivery manager
    alert_delivery_manager = AlertDeliveryManager(alert_engine)
    
    # Test 1: Basic alert engine functionality
    print("\n📋 Test 1: Basic Alert Engine")
    print("-" * 30)
    
    # Test signal data
    test_signals = [
        {
            'symbol': 'AAPL',
            'signal_type': 'active_decline_reversal_opportunity',
            'confidence': 0.85,
            'timeframe': '1day',
            'current_price': 150.25,
            'percentage_change': -3.2,
            'ttm_squeeze_status': 'squeeze_released',
            'consecutive_bars': 3,
            'scanner_tier': 'Ultra Priority',
            'volume_analysis': {'relative_volume': 2.1},
            'technical_indicators': {
                'rsi_divergence': True,
                'macd_confirmation': True,
                'at_key_level': True
            }
        },
        {
            'symbol': 'TSLA',
            'signal_type': 'moderate_decline_opportunity',
            'confidence': 0.72,
            'timeframe': '1day',
            'current_price': 245.80,
            'percentage_change': -2.1,
            'ttm_squeeze_status': 'squeeze_active',
            'consecutive_bars': 2,
            'scanner_tier': 'High Priority',
            'volume_analysis': {'relative_volume': 1.5},
            'technical_indicators': {
                'rsi_divergence': False,
                'macd_confirmation': True,
                'at_key_level': False
            }
        },
        {
            'symbol': 'NVDA',
            'signal_type': 'bullish_momentum',
            'confidence': 0.78,
            'timeframe': '1day',
            'current_price': 420.15,
            'percentage_change': 2.8,
            'ttm_squeeze_status': 'squeeze_inactive',
            'consecutive_bars': 1,
            'scanner_tier': 'Medium Priority',
            'volume_analysis': {'relative_volume': 1.2},
            'technical_indicators': {
                'rsi_divergence': False,
                'macd_confirmation': False,
                'at_key_level': False
            }
        }
    ]
    
    # Process test signals
    processed_alerts = []
    for signal_data in test_signals:
        try:
            alert_signal = await alert_engine.process_lee_method_signal(signal_data)
            if alert_signal:
                processed_alerts.append(alert_signal)
                print(f"✅ Alert created for {signal_data['symbol']}: {alert_signal.priority.value}")
            else:
                print(f"❌ No alert created for {signal_data['symbol']}")
        except Exception as e:
            print(f"❌ Error processing {signal_data['symbol']}: {e}")
    
    print(f"\n📊 Processed {len(processed_alerts)} alerts from {len(test_signals)} signals")
    
    # Test 2: Signal Classification
    print("\n📋 Test 2: Signal Classification")
    print("-" * 30)
    
    for alert in processed_alerts:
        try:
            classified_signal, context = await signal_classifier.classify_signal(alert)
            print(f"✅ {alert.symbol}: {alert.priority.value} -> {classified_signal.priority.value}")
            print(f"   Confidence: {alert.confidence:.2f} -> {classified_signal.confidence:.2f}")
            print(f"   Market Condition: {context.market_condition.value}")
            print(f"   Technical Confluence: {context.technical_confluence}")
        except Exception as e:
            print(f"❌ Classification error for {alert.symbol}: {e}")
    
    # Test 3: Alert Statistics
    print("\n📋 Test 3: Alert Statistics")
    print("-" * 30)
    
    stats = alert_engine.get_alert_stats()
    print(f"Total Alerts: {stats['total_alerts']}")
    print(f"Alerts by Priority: {dict(stats['alerts_by_priority'])}")
    print(f"Alerts by Signal Type: {dict(stats['alerts_by_signal_type'])}")
    
    # Test 4: Active Alerts Management
    print("\n📋 Test 4: Active Alerts Management")
    print("-" * 30)
    
    active_alerts = alert_engine.get_active_alerts()
    print(f"Active Alerts: {len(active_alerts)}")
    
    for alert in active_alerts:
        print(f"  - {alert.symbol}: {alert.signal_type.value} ({alert.priority.value})")
    
    # Test 5: Alert History
    print("\n📋 Test 5: Alert History")
    print("-" * 30)
    
    history = alert_engine.get_alert_history(10)
    print(f"Alert History (last 10): {len(history)}")
    
    for alert in history[-3:]:  # Show last 3
        print(f"  - {alert.timestamp.strftime('%H:%M:%S')}: {alert.symbol} ({alert.priority.value})")
    
    # Test 6: Alert Management Operations
    print("\n📋 Test 6: Alert Management Operations")
    print("-" * 30)
    
    if active_alerts:
        # Test snooze
        first_alert = active_alerts[0]
        print(f"Snoozing alert for {first_alert.symbol}...")
        alert_engine.snooze_alert(first_alert.id, 15)  # 15 minutes
        print("✅ Alert snoozed")
        
        # Test dismiss
        if len(active_alerts) > 1:
            second_alert = active_alerts[1]
            print(f"Dismissing alert for {second_alert.symbol}...")
            alert_engine.dismiss_alert(second_alert.id)
            print("✅ Alert dismissed")
    
    # Test 7: Channel Testing
    print("\n📋 Test 7: Alert Channel Testing")
    print("-" * 30)
    
    try:
        await alert_delivery_manager.test_all_channels()
        print("✅ All alert channels tested")
    except Exception as e:
        print(f"❌ Channel testing error: {e}")
    
    # Test 8: Classification Rules
    print("\n📋 Test 8: Classification Rules")
    print("-" * 30)
    
    rules = signal_classifier.get_rules()
    print(f"Active Classification Rules: {len(rules)}")
    
    for rule in rules[:3]:  # Show first 3
        print(f"  - {rule.name}: {'Enabled' if rule.enabled else 'Disabled'}")
        print(f"    Priority Adjustment: {rule.priority_adjustment}")
        print(f"    Confidence Multiplier: {rule.confidence_multiplier}")
    
    # Test 9: Performance Metrics
    print("\n📋 Test 9: Performance Metrics")
    print("-" * 30)
    
    classification_stats = signal_classifier.get_classification_stats()
    print(f"Classification Stats:")
    print(f"  Total Classified: {classification_stats['total_classified']}")
    print(f"  Priority Upgrades: {classification_stats['priority_upgrades']}")
    print(f"  Priority Downgrades: {classification_stats['priority_downgrades']}")
    print(f"  Confidence Adjustments: {classification_stats['confidence_adjustments']}")
    
    # Test 10: Cleanup
    print("\n📋 Test 10: System Cleanup")
    print("-" * 30)
    
    try:
        await alert_engine.cleanup_old_alerts()
        print("✅ System cleanup completed")
    except Exception as e:
        print(f"❌ Cleanup error: {e}")
    
    print("\n🎉 Alert System Testing Completed!")
    print("=" * 60)
    
    # Final summary
    final_active = alert_engine.get_active_alerts()
    final_stats = alert_engine.get_alert_stats()
    
    print(f"\n📊 Final Summary:")
    print(f"   Active Alerts: {len(final_active)}")
    print(f"   Total Processed: {final_stats['total_alerts']}")
    print(f"   System Status: ✅ OPERATIONAL")

async def test_real_time_alerts():
    """Test real-time alert processing"""
    
    print("\n🔄 Testing Real-Time Alert Processing")
    print("-" * 40)
    
    # Simulate real-time signals
    real_time_signals = [
        {
            'symbol': 'SPY',
            'signal_type': 'active_decline_reversal_opportunity',
            'confidence': 0.92,
            'timeframe': '1day',
            'current_price': 445.20,
            'percentage_change': -1.8,
            'ttm_squeeze_status': 'squeeze_released',
            'consecutive_bars': 4,
            'scanner_tier': 'Ultra Priority'
        },
        {
            'symbol': 'QQQ',
            'signal_type': 'bullish_momentum',
            'confidence': 0.81,
            'timeframe': '1day',
            'current_price': 375.60,
            'percentage_change': 1.5,
            'ttm_squeeze_status': 'squeeze_inactive',
            'consecutive_bars': 2,
            'scanner_tier': 'High Priority'
        }
    ]
    
    print("Processing real-time signals...")
    
    for i, signal_data in enumerate(real_time_signals):
        print(f"\n⏱️  Processing signal {i+1}: {signal_data['symbol']}")
        
        start_time = datetime.now()
        
        try:
            # Process through alert engine
            alert_signal = await alert_engine.process_lee_method_signal(signal_data)
            
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            if alert_signal:
                print(f"✅ Alert processed in {processing_time:.2f}ms")
                print(f"   Priority: {alert_signal.priority.value}")
                print(f"   Confidence: {alert_signal.confidence:.2%}")
            else:
                print(f"❌ No alert generated ({processing_time:.2f}ms)")
                
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            print(f"❌ Error in {processing_time:.2f}ms: {e}")
        
        # Small delay to simulate real-time processing
        await asyncio.sleep(0.1)
    
    print("\n✅ Real-time alert processing test completed")

async def main():
    """Main test function"""
    try:
        # Run basic alert system tests
        await test_alert_system()
        
        # Run real-time processing tests
        await test_real_time_alerts()
        
        print("\n🎯 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
