"""
A.T.L.A.S. Production Environment Setup
Automated production environment configuration with security, monitoring, and backup systems
"""

import os
import sys
import asyncio
import logging
import json
import subprocess
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProductionEnvironmentSetup:
    """Handles A.T.L.A.S. production environment setup and configuration"""
    
    def __init__(self, production_dir: str = "atlas_production"):
        self.production_dir = Path(production_dir)
        self.source_dir = Path(".")
        self.setup_log = []
        self.start_time = datetime.now()
        
    def log_step(self, step: str, status: str = "INFO", details: str = ""):
        """Log setup step"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            "timestamp": timestamp,
            "step": step,
            "status": status,
            "details": details
        }
        self.setup_log.append(log_entry)
        
        if status == "ERROR":
            logger.error(f"{step}: {details}")
        elif status == "WARNING":
            logger.warning(f"{step}: {details}")
        else:
            logger.info(f"{step}: {details}")
    
    def create_production_directory(self) -> bool:
        """Create and prepare production directory structure"""
        try:
            self.log_step("Creating production directory structure", "INFO", f"Path: {self.production_dir}")
            
            # Remove existing production directory if it exists
            if self.production_dir.exists():
                shutil.rmtree(self.production_dir)
                self.log_step("Removed existing production directory", "INFO")
            
            # Create production directory structure
            directories = [
                self.production_dir,
                self.production_dir / "app",
                self.production_dir / "config",
                self.production_dir / "logs",
                self.production_dir / "data",
                self.production_dir / "backups",
                self.production_dir / "ssl",
                self.production_dir / "monitoring",
                self.production_dir / "scripts"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            self.log_step("Production directory structure created", "INFO", f"{len(directories)} directories")
            return True
            
        except Exception as e:
            self.log_step("Failed to create production directory", "ERROR", str(e))
            return False
    
    def copy_application_files(self) -> bool:
        """Copy application files to production directory"""
        try:
            self.log_step("Copying application files to production", "INFO")
            
            # Copy Python files
            python_files = list(self.source_dir.glob("*.py"))
            app_dir = self.production_dir / "app"
            
            for file_path in python_files:
                if file_path.name not in ["production_environment_setup.py", "test_*.py"]:
                    dest_path = app_dir / file_path.name
                    shutil.copy2(file_path, dest_path)
            
            # Copy configuration files
            config_files = [".env.example", "requirements.txt"]
            config_dir = self.production_dir / "config"
            
            for config_file in config_files:
                if Path(config_file).exists():
                    shutil.copy2(config_file, config_dir / config_file)
            
            # Copy helper directories if they exist
            helper_dirs = ["4_helper_tools", "static", "templates"]
            for helper_dir in helper_dirs:
                if Path(helper_dir).exists():
                    shutil.copytree(helper_dir, app_dir / helper_dir, dirs_exist_ok=True)
            
            files_copied = len(list(app_dir.glob("*.py")))
            self.log_step("Application files copied", "INFO", f"{files_copied} Python files")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to copy application files", "ERROR", str(e))
            return False
    
    def create_production_config(self) -> bool:
        """Create production-specific configuration"""
        try:
            self.log_step("Creating production configuration", "INFO")
            
            # Create production .env file
            production_env_content = """# A.T.L.A.S. Production Environment Configuration
# PRODUCTION ENVIRONMENT - SECURE CONFIGURATION REQUIRED

# System Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# CRITICAL: Trading Mode - PAPER TRADING ENFORCED FOR SAFETY
ATLAS_TRADING_MODE=PAPER
PAPER_TRADING=true

# Database Configuration (Production)
DATABASE_URL=postgresql://atlas_user:secure_password@localhost:5432/atlas_production

# API Keys (MUST BE CONFIGURED WITH REAL KEYS)
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=your_production_alpaca_api_key_here
ALPACA_SECRET_KEY=your_production_alpaca_secret_key_here

# Financial Modeling Prep API (Production Key Required)
FMP_API_KEY=your_production_fmp_api_key_here

# OpenAI API (Production Key Required)
OPENAI_API_KEY=your_production_openai_api_key_here

# Grok API (Production Key Required)
GROK_API_KEY=your_production_grok_api_key_here

# Email Configuration (Production SMTP)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_production_email_password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/atlas/atlas.log

# Performance Settings (Production Optimized)
MAX_CONCURRENT_REQUESTS=50
CACHE_TTL=60
REQUEST_TIMEOUT=30

# Security Settings (MUST BE CHANGED)
SECRET_KEY=change_this_to_a_secure_random_string_in_production
JWT_SECRET=change_this_to_a_secure_jwt_secret_in_production

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/atlas.crt
SSL_KEY_PATH=/etc/ssl/private/atlas.key

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
"""
            
            with open(self.production_dir / "config" / ".env.production", "w") as f:
                f.write(production_env_content)
            
            self.log_step("Production .env file created", "INFO")
            
            # Create production startup script
            startup_script = """#!/bin/bash
# A.T.L.A.S. Production Environment Startup Script

set -e

echo "Starting A.T.L.A.S. Production Environment..."
echo "Environment: PRODUCTION"
echo "Trading Mode: PAPER TRADING ONLY"
echo "Port: 8000"
echo "Debug: Disabled"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "WARNING: Running as root is not recommended for production"
fi

# Set production environment
export ENVIRONMENT=production

# Navigate to application directory
cd /opt/atlas/app

# Activate virtual environment
if [ -d "/opt/atlas/venv" ]; then
    source /opt/atlas/venv/bin/activate
    echo "Virtual environment activated"
else
    echo "ERROR: Virtual environment not found at /opt/atlas/venv"
    exit 1
fi

# Load production environment variables
if [ -f "/opt/atlas/config/.env.production" ]; then
    export $(cat /opt/atlas/config/.env.production | grep -v '^#' | xargs)
    echo "Production environment variables loaded"
else
    echo "ERROR: Production environment file not found"
    exit 1
fi

# Check required environment variables
required_vars=("ALPACA_API_KEY" "FMP_API_KEY" "SECRET_KEY" "JWT_SECRET")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ] || [ "${!var}" = "your_production_${var,,}_here" ]; then
        echo "ERROR: Required environment variable $var is not set or still has placeholder value"
        exit 1
    fi
done

# Install/update dependencies
echo "Installing production dependencies..."
pip install -r /opt/atlas/config/requirements.txt --no-cache-dir

# Run database migrations if needed
echo "Running database setup..."
python -c "
import asyncio
from atlas_database import AtlasDatabase
async def setup_db():
    db = AtlasDatabase()
    await db.initialize()
    print('Production database initialized')
try:
    asyncio.run(setup_db())
except Exception as e:
    print(f'Database setup error: {e}')
"

# Create log directory
mkdir -p /var/log/atlas
chown atlas:atlas /var/log/atlas

# Start the A.T.L.A.S. server with production settings
echo "Starting A.T.L.A.S. server in production mode..."
exec gunicorn --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker atlas_server:app
"""
            
            with open(self.production_dir / "scripts" / "start_production.sh", "w") as f:
                f.write(startup_script)
            
            # Make startup script executable
            os.chmod(self.production_dir / "scripts" / "start_production.sh", 0o755)
            
            self.log_step("Production startup script created", "INFO")
            
            return True
            
        except Exception as e:
            self.log_step("Failed to create production configuration", "ERROR", str(e))
            return False
    
    def create_security_config(self) -> bool:
        """Create security configuration files"""
        try:
            self.log_step("Creating security configuration", "INFO")
            
            # Create nginx configuration
            nginx_config = """# A.T.L.A.S. Production Nginx Configuration
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/atlas.crt;
    ssl_certificate_key /etc/ssl/private/atlas.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Proxy to A.T.L.A.S. application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Static files
    location /static/ {
        alias /opt/atlas/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
"""
            
            with open(self.production_dir / "config" / "nginx.conf", "w") as f:
                f.write(nginx_config)
            
            # Create systemd service file
            systemd_service = """[Unit]
Description=A.T.L.A.S. Trading System
After=network.target postgresql.service

[Service]
Type=exec
User=atlas
Group=atlas
WorkingDirectory=/opt/atlas/app
Environment=PATH=/opt/atlas/venv/bin
ExecStart=/opt/atlas/scripts/start_production.sh
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/atlas /opt/atlas/data /opt/atlas/backups

[Install]
WantedBy=multi-user.target
"""
            
            with open(self.production_dir / "config" / "atlas.service", "w") as f:
                f.write(systemd_service)
            
            self.log_step("Security configuration files created", "INFO")
            return True
            
        except Exception as e:
            self.log_step("Failed to create security configuration", "ERROR", str(e))
            return False
    
    def create_monitoring_config(self) -> bool:
        """Create monitoring and alerting configuration"""
        try:
            self.log_step("Creating monitoring configuration", "INFO")
            
            # Create Prometheus configuration
            prometheus_config = """global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "atlas_alerts.yml"

scrape_configs:
  - job_name: 'atlas'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
"""
            
            with open(self.production_dir / "monitoring" / "prometheus.yml", "w") as f:
                f.write(prometheus_config)
            
            # Create alert rules
            alert_rules = """groups:
- name: atlas_alerts
  rules:
  - alert: AtlasDown
    expr: up{job="atlas"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "A.T.L.A.S. service is down"
      description: "A.T.L.A.S. has been down for more than 1 minute"

  - alert: HighErrorRate
    expr: rate(atlas_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage"
      description: "Memory usage is above 90%"
"""
            
            with open(self.production_dir / "monitoring" / "atlas_alerts.yml", "w") as f:
                f.write(alert_rules)
            
            self.log_step("Monitoring configuration created", "INFO")
            return True
            
        except Exception as e:
            self.log_step("Failed to create monitoring configuration", "ERROR", str(e))
            return False
    
    def create_backup_scripts(self) -> bool:
        """Create backup and recovery scripts"""
        try:
            self.log_step("Creating backup scripts", "INFO")
            
            # Create backup script
            backup_script = """#!/bin/bash
# A.T.L.A.S. Production Backup Script

set -e

BACKUP_DIR="/opt/atlas/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="atlas_backup_$DATE"

echo "Starting A.T.L.A.S. backup: $BACKUP_NAME"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup database
echo "Backing up database..."
pg_dump atlas_production > "$BACKUP_DIR/$BACKUP_NAME/database.sql"

# Backup application data
echo "Backing up application data..."
tar -czf "$BACKUP_DIR/$BACKUP_NAME/app_data.tar.gz" /opt/atlas/data

# Backup configuration
echo "Backing up configuration..."
cp -r /opt/atlas/config "$BACKUP_DIR/$BACKUP_NAME/"

# Backup logs (last 7 days)
echo "Backing up recent logs..."
find /var/log/atlas -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/$BACKUP_NAME/" \;

# Create backup manifest
echo "Creating backup manifest..."
cat > "$BACKUP_DIR/$BACKUP_NAME/manifest.txt" << EOF
A.T.L.A.S. Backup Manifest
Backup Name: $BACKUP_NAME
Date: $(date)
Database: Included
App Data: Included
Configuration: Included
Logs: Last 7 days
EOF

# Compress backup
echo "Compressing backup..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

# Clean old backups (keep last 30 days)
find "$BACKUP_DIR" -name "atlas_backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
"""
            
            with open(self.production_dir / "scripts" / "backup.sh", "w") as f:
                f.write(backup_script)
            
            os.chmod(self.production_dir / "scripts" / "backup.sh", 0o755)
            
            # Create restore script
            restore_script = """#!/bin/bash
# A.T.L.A.S. Production Restore Script

set -e

if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup_file.tar.gz>"
    exit 1
fi

BACKUP_FILE="$1"
RESTORE_DIR="/tmp/atlas_restore_$(date +%s)"

echo "Starting A.T.L.A.S. restore from: $BACKUP_FILE"

# Extract backup
mkdir -p "$RESTORE_DIR"
tar -xzf "$BACKUP_FILE" -C "$RESTORE_DIR"

BACKUP_NAME=$(basename "$BACKUP_FILE" .tar.gz)
BACKUP_PATH="$RESTORE_DIR/$BACKUP_NAME"

# Stop A.T.L.A.S. service
echo "Stopping A.T.L.A.S. service..."
systemctl stop atlas

# Restore database
echo "Restoring database..."
dropdb atlas_production || true
createdb atlas_production
psql atlas_production < "$BACKUP_PATH/database.sql"

# Restore application data
echo "Restoring application data..."
rm -rf /opt/atlas/data/*
tar -xzf "$BACKUP_PATH/app_data.tar.gz" -C /

# Restore configuration (backup current first)
echo "Restoring configuration..."
cp -r /opt/atlas/config /opt/atlas/config.backup.$(date +%s)
cp -r "$BACKUP_PATH/config"/* /opt/atlas/config/

# Start A.T.L.A.S. service
echo "Starting A.T.L.A.S. service..."
systemctl start atlas

# Cleanup
rm -rf "$RESTORE_DIR"

echo "Restore completed successfully"
"""
            
            with open(self.production_dir / "scripts" / "restore.sh", "w") as f:
                f.write(restore_script)
            
            os.chmod(self.production_dir / "scripts" / "restore.sh", 0o755)
            
            self.log_step("Backup scripts created", "INFO")
            return True
            
        except Exception as e:
            self.log_step("Failed to create backup scripts", "ERROR", str(e))
            return False
    
    def create_deployment_report(self) -> bool:
        """Create production deployment report"""
        try:
            setup_duration = (datetime.now() - self.start_time).total_seconds()
            
            report = {
                "deployment_info": {
                    "timestamp": self.start_time.isoformat(),
                    "duration_seconds": setup_duration,
                    "production_directory": str(self.production_dir),
                    "environment": "production"
                },
                "setup_log": self.setup_log,
                "summary": {
                    "total_steps": len(self.setup_log),
                    "successful_steps": len([log for log in self.setup_log if log["status"] != "ERROR"]),
                    "errors": len([log for log in self.setup_log if log["status"] == "ERROR"]),
                    "warnings": len([log for log in self.setup_log if log["status"] == "WARNING"])
                },
                "production_checklist": [
                    "1. Configure real API keys in .env.production",
                    "2. Set up SSL certificates",
                    "3. Configure PostgreSQL database",
                    "4. Install and configure Nginx",
                    "5. Set up monitoring (Prometheus/Grafana)",
                    "6. Configure backup schedule",
                    "7. Create atlas user and set permissions",
                    "8. Install systemd service",
                    "9. Configure firewall rules",
                    "10. Test all systems before going live"
                ],
                "security_requirements": [
                    "Change all default passwords and secrets",
                    "Configure SSL/TLS certificates",
                    "Set up firewall rules",
                    "Enable fail2ban for SSH protection",
                    "Configure log rotation",
                    "Set up intrusion detection",
                    "Regular security updates",
                    "Backup encryption"
                ]
            }
            
            with open(self.production_dir / "production_deployment_report.json", "w") as f:
                json.dump(report, f, indent=2)
            
            self.log_step("Production deployment report created", "INFO")
            return True
            
        except Exception as e:
            self.log_step("Failed to create deployment report", "ERROR", str(e))
            return False
    
    async def setup_production_environment(self) -> bool:
        """Execute complete production environment setup"""
        try:
            logger.info("="*80)
            logger.info("A.T.L.A.S. PRODUCTION ENVIRONMENT SETUP STARTED")
            logger.info("="*80)
            
            # Step 1: Create production directory structure
            if not self.create_production_directory():
                return False
            
            # Step 2: Copy application files
            if not self.copy_application_files():
                return False
            
            # Step 3: Create production configuration
            if not self.create_production_config():
                return False
            
            # Step 4: Create security configuration
            if not self.create_security_config():
                return False
            
            # Step 5: Create monitoring configuration
            if not self.create_monitoring_config():
                return False
            
            # Step 6: Create backup scripts
            if not self.create_backup_scripts():
                return False
            
            # Step 7: Create deployment report
            if not self.create_deployment_report():
                return False
            
            # Success summary
            setup_duration = (datetime.now() - self.start_time).total_seconds()
            
            logger.info("="*80)
            logger.info("A.T.L.A.S. PRODUCTION ENVIRONMENT SETUP COMPLETED")
            logger.info(f"Duration: {setup_duration:.2f} seconds")
            logger.info(f"Production Directory: {self.production_dir}")
            logger.info("="*80)
            
            print("\n🎉 PRODUCTION ENVIRONMENT SETUP SUCCESSFUL!")
            print(f"📁 Production Directory: {self.production_dir}")
            print(f"⏱️  Setup Time: {setup_duration:.2f} seconds")
            print("\n📋 NEXT STEPS:")
            print("1. Configure real API keys in config/.env.production")
            print("2. Set up SSL certificates")
            print("3. Install and configure system services")
            print("4. Run security hardening")
            print("5. Test all systems before deployment")
            
            return True
            
        except Exception as e:
            self.log_step("Production environment setup failed", "ERROR", str(e))
            logger.error(f"PRODUCTION SETUP FAILED: {e}")
            return False


async def main():
    """Main setup function"""
    setup = ProductionEnvironmentSetup()
    success = await setup.setup_production_environment()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
