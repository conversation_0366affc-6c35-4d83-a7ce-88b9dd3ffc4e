"""
A.T.L<PERSON><PERSON><PERSON>S Lee Method - Advanced 5-Point TTM Squeeze Pattern Detection System
Implements TTM Squeeze rebound pattern detection algorithm branded as "Lee Method"
Detects momentum reversal signals using 5-point criteria for optimal entry timing
"""

import asyncio
import logging
import json
import sys
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
import numpy as np
import requests
import pytz
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols

# CRITICAL SECURITY: Import mathematical safeguards
from atlas_math_safeguards import math_safeguards, MathematicalError

# ENHANCED: Import advanced market data manager
from atlas_enhanced_market_data import enhanced_market_data, MarketQuote, HistoricalData

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))
try:
    from config import settings
    from atlas_web_search_service import web_search_service, SearchContext, SearchQuery
    WEB_SEARCH_AVAILABLE = True
except ImportError:
    # Fallback if config not available
    WEB_SEARCH_AVAILABLE = False
    class MockSettings:
        FMP_API_KEY = None  # SECURITY FIX: Remove hardcoded demo key
    settings = MockSettings()

logger = logging.getLogger(__name__)


# ============================================================================
# LEE METHOD DATA STRUCTURES
# ============================================================================

@dataclass
class LeeMethodSignal:
    """Lee Method signal detection result"""
    # Required fields (no defaults) - must come first
    symbol: str
    signal_type: str  # 'bullish_momentum', 'bearish_momentum', 'neutral'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    timeframe: str
    timestamp: datetime

    # Lee Method specific data (required fields)
    histogram_sequence: List[float]  # The decreasing + increasing sequence
    momentum_bars: List[float]  # Momentum values for confirmation
    momentum_confirmation: bool

    # TTM Squeeze specific fields
    histogram_current: float = 0.0  # Current histogram value
    histogram_previous: float = 0.0  # Previous histogram value

    # Additional signal fields
    signal_direction: str = 'bullish'  # Signal direction
    strength: str = 'MODERATE'  # Signal strength
    current_price: float = 0.0  # Current market price
    ema5_uptrend: bool = False  # EMA5 uptrend status
    ema8_uptrend: bool = False  # EMA8 uptrend status
    ema21_uptrend: bool = False  # EMA21 uptrend status
    squeeze_active: bool = False  # TTM Squeeze status

    # Multi-timeframe analysis (required fields) - moved to top to avoid dataclass error
    weekly_trend: str = 'bullish'  # 'bullish', 'bearish', 'neutral'
    daily_trend: str = 'bullish'
    trend_alignment: bool = True  # Weekly and daily trend confirmation

    # Risk metrics (with defaults to avoid dataclass error)
    risk_reward_ratio: float = 2.0
    position_size_percent: float = 1.0

    # Fields with defaults (must come last in dataclass)
    # Current market price (for UI display)
    current_price: float = 0.0
    price_change: float = 0.0
    price_change_percent: float = 0.0

    # Additional fields with defaults
    strength: str = "medium"  # 'weak', 'medium', 'strong'
    signal_direction: str = "bullish"  # 'bullish', 'bearish', 'neutral'
    histogram_current: float = 0.0  # Current histogram value
    ema5_uptrend: bool = False  # EMA5 uptrend status
    ema8_uptrend: bool = False  # EMA8 uptrend status
    momentum_uptrend: bool = False  # Momentum uptrend status
    squeeze_active: bool = False  # TTM Squeeze active status

    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'confidence': self.confidence,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            # Current price information for UI display
            'price': self.current_price,  # Frontend expects 'price' field
            'current_price': self.current_price,
            'price_change': self.price_change,
            'price_change_percent': self.price_change_percent,
            # Lee Method specific data
            'histogram_sequence': self.histogram_sequence,
            'momentum_bars': self.momentum_bars,
            'momentum_confirmation': self.momentum_confirmation,
            'weekly_trend': self.weekly_trend,
            'daily_trend': self.daily_trend,
            'trend_alignment': self.trend_alignment,
            'risk_reward_ratio': self.risk_reward_ratio,
            'position_size_percent': self.position_size_percent,
            # Additional fields for compatibility
            'strength': self.strength,
            'signal_direction': self.signal_direction
        }


# ============================================================================
# LEE METHOD SCANNER
# ============================================================================

class LeeMethodScanner:
    """Lee Method pattern scanner implementing the 5-point TTM Squeeze algorithm"""

    def __init__(self, fmp_api_key: str = None, market_engine=None, test_mode: bool = False):
        self.logger = logger
        # SECURITY FIX: Remove hardcoded demo key and validate
        self.fmp_api_key = fmp_api_key or settings.FMP_API_KEY

        # Validate API key is not a placeholder
        if self.fmp_api_key and self.fmp_api_key.lower() in ['demo', 'test', 'placeholder', 'your_api_key']:
            logger.warning("FMP API key appears to be a placeholder - some features may not work")
            self.fmp_api_key = None
        self.base_url = "https://financialmodelingprep.com/api/v3"

        # Test mode allows scanning during closed market hours for UAT testing
        self.test_mode = test_mode
        if test_mode:
            self.logger.info("Scanner initialized in TEST MODE - will scan during closed market hours")

        # Market engine for real-time price data
        self.market_engine = market_engine
        if not self.market_engine:
            # Import and initialize market engine if not provided
            try:
                from atlas_market_core import AtlasMarketEngine
                self.market_engine = AtlasMarketEngine()
            except ImportError:
                self.logger.warning("Market engine not available - prices will use historical data")
                self.market_engine = None

        # CRITICAL: ULTRA CONSERVATIVE request queuing to prevent 429 errors
        # Initialize semaphore as None, will be created per event loop
        self.request_semaphore = None
        self.last_request_time = 0
        self.min_request_interval = 5.0  # 5 seconds between requests
        self.backoff_multiplier = 1.0  # For exponential backoff

        # TTM Squeeze parameters (branded as Lee Method)
        self.macd_fast = 12  # MACD fast EMA
        self.macd_slow = 26  # MACD slow EMA
        self.macd_signal = 9  # MACD signal line
        self.min_declining_bars = 3  # Exactly 3 declining histogram bars

        # Web search service for contextual market intelligence
        if WEB_SEARCH_AVAILABLE:
            self.web_search_service = web_search_service
            self.web_search_enabled = getattr(settings, 'WEB_SEARCH_LEE_METHOD_ENABLED', True)
        else:
            self.web_search_service = None
            self.web_search_enabled = False
        self.max_lookback_bars = 10   # Maximum bars to look back for pattern

        # EMA trend confirmation periods
        self.ema_periods = [5, 8, 21, 50]  # Keep existing for compatibility
        self.ema5_period = 5  # EMA 5 for trend confirmation
        self.ema8_period = 8  # EMA 8 for trend confirmation

        # TTM Squeeze parameters
        self.bb_period = 20  # Bollinger Bands period
        self.bb_std = 2.0    # Bollinger Bands standard deviation
        self.kc_period = 20  # Keltner Channels period
        self.kc_multiplier = 1.5  # Keltner Channels multiplier

        # Configurable squeeze filter options
        self.require_squeeze = False  # Optional squeeze requirement
        self.squeeze_lookback = 0     # Lookback for squeeze state (0 = current bar)

        # Risk management
        self.default_risk_percent = 2.0  # 2% risk per trade
        self.default_reward_ratio = 2.0  # 2:1 reward:risk ratio

        # PRODUCTION: Strict pattern detection settings for accuracy
        self.use_flexible_patterns = False  # Disable relaxed criteria for production accuracy
        self.min_confidence_threshold = 0.65  # Higher threshold for production reliability
        self.pattern_sensitivity = 0.5  # Stricter sensitivity for fewer false positives
        self.allow_weak_signals = False  # Disable weak signals for production quality

    def configure_squeeze_filter(self, require_squeeze: bool = False, squeeze_lookback: int = 0):
        """Configure the optional TTM Squeeze filter

        Args:
            require_squeeze: Whether to require an active TTM Squeeze
            squeeze_lookback: Lookback period for squeeze state (0 = current bar)
        """
        self.require_squeeze = require_squeeze
        self.squeeze_lookback = squeeze_lookback
        self.logger.info(f"[CONFIG] TTM Squeeze filter: require={require_squeeze}, lookback={squeeze_lookback}")

    def configure_pattern_sensitivity(self,
                                    use_flexible_patterns: bool = False,
                                    min_confidence_threshold: float = 0.65,
                                    pattern_sensitivity: float = 0.5,
                                    allow_weak_signals: bool = False):
        """Configure pattern detection sensitivity

        Args:
            use_flexible_patterns: Enable relaxed pattern criteria
            min_confidence_threshold: Minimum confidence for pattern acceptance
            pattern_sensitivity: Overall sensitivity (0.0 = very strict, 1.0 = very loose)
            allow_weak_signals: Allow weak signal strength patterns
        """
        self.use_flexible_patterns = use_flexible_patterns
        self.min_confidence_threshold = max(0.1, min(0.9, min_confidence_threshold))
        self.pattern_sensitivity = max(0.1, min(1.0, pattern_sensitivity))
        self.allow_weak_signals = allow_weak_signals

        self.logger.info(f"[CONFIG] Pattern sensitivity: flexible={use_flexible_patterns}, "
                        f"min_confidence={self.min_confidence_threshold:.2f}, "
                        f"sensitivity={self.pattern_sensitivity:.2f}, "
                        f"allow_weak={allow_weak_signals}")

    def _is_market_hours(self) -> bool:
        """Check if current time is within market hours (9:30 AM - 4:00 PM ET)"""
        try:
            # If in test mode, always return True to allow UAT testing
            if getattr(self, 'test_mode', False):
                self.logger.debug("Test mode enabled - allowing scan during closed market hours")
                return True

            # Use Eastern Time for market hours (NYSE/NASDAQ standard)
            et_tz = pytz.timezone('US/Eastern')
            current_time_et = datetime.now(et_tz).time()

            # Market hours in Eastern Time
            from datetime import time as dt_time
            market_open_et = dt_time(9, 30)  # 9:30 AM ET
            market_close_et = dt_time(16, 0)  # 4:00 PM ET

            is_market_hours = market_open_et <= current_time_et <= market_close_et

            if not is_market_hours:
                self.logger.debug(f"Market closed - Current ET time: {current_time_et}, "
                                f"Market hours: {market_open_et}-{market_close_et}")

            return is_market_hours

        except Exception as e:
            self.logger.error(f"Error checking market hours: {e}")
            return False  # Default to closed if error

    async def enhance_signal_with_market_context(self, symbol: str, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance Lee Method signals with contextual market news and events
        """
        try:
            if not self.web_search_enabled or not self.web_search_service:
                return {"web_enhanced": False, "market_context": {}}

            # Search for momentum and pattern-related news
            if WEB_SEARCH_AVAILABLE:
                momentum_news = await self.web_search_service.search_for_context(
                    f"{symbol} momentum breakout technical analysis MACD histogram",
                    SearchContext.LEE_METHOD,
                    [symbol],
                    max_results=2
                )

                # Analyze market context for signal confirmation
                context_analysis = self._analyze_market_context_for_signal(momentum_news, signal_data)

                return {
                    "web_enhanced": True,
                    "market_context": {
                        "momentum_news": [result.__dict__ for result in momentum_news],
                        "context_analysis": context_analysis,
                        "total_sources": len(momentum_news)
                    },
                    "enhanced_confidence": self._calculate_enhanced_confidence(
                        signal_data.get("confidence", 0.5), context_analysis
                    )
                }
            else:
                return {"web_enhanced": False, "market_context": {}}

        except Exception as e:
            self.logger.error(f"[LEE_METHOD_WEB_SEARCH] Enhancement failed: {e}")
            return {"web_enhanced": False, "error": str(e)}

    def _analyze_market_context_for_signal(self, search_results: List, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market context to confirm or contradict Lee Method signals"""
        try:
            if not search_results:
                return {"confirmation": "neutral", "strength": 0.0}

            # Simple confirmation based on search result relevance
            total_relevance = sum(result.relevance_score for result in search_results)
            avg_relevance = total_relevance / len(search_results) if search_results else 0.0

            if avg_relevance > 7.0:
                confirmation = "strong_support"
            elif avg_relevance > 5.0:
                confirmation = "moderate_support"
            else:
                confirmation = "neutral"

            return {
                "confirmation": confirmation,
                "strength": avg_relevance / 10.0,  # Normalize to 0-1
                "sources_analyzed": len(search_results)
            }

        except Exception as e:
            self.logger.error(f"[LEE_METHOD_WEB_SEARCH] Context analysis failed: {e}")
            return {"confirmation": "neutral", "strength": 0.0}

    def _calculate_enhanced_confidence(self, base_confidence: float, context_analysis: Dict[str, Any]) -> float:
        """Calculate enhanced confidence incorporating market context"""
        try:
            confirmation = context_analysis.get("confirmation", "neutral")
            strength = context_analysis.get("strength", 0.0)

            # Adjust confidence based on market context
            if confirmation == "strong_support":
                enhanced_confidence = min(base_confidence + (strength * 0.1), 1.0)
            elif confirmation == "moderate_support":
                enhanced_confidence = min(base_confidence + (strength * 0.05), 1.0)
            else:
                enhanced_confidence = base_confidence

            return enhanced_confidence

        except Exception as e:
            self.logger.error(f"[LEE_METHOD_WEB_SEARCH] Enhanced confidence calculation failed: {e}")
            return base_confidence

    def get_ttm_squeeze_config(self) -> Dict[str, Any]:
        """Get current TTM Squeeze configuration"""
        return {
            'macd_fast': self.macd_fast,
            'macd_slow': self.macd_slow,
            'macd_signal': self.macd_signal,
            'min_declining_bars': self.min_declining_bars,
            'ema5_period': self.ema5_period,
            'ema8_period': self.ema8_period,
            'bb_period': self.bb_period,
            'bb_std': self.bb_std,
            'kc_period': self.kc_period,
            'kc_multiplier': self.kc_multiplier,
            'require_squeeze': self.require_squeeze,
            'squeeze_lookback': self.squeeze_lookback
        }

    def calculate_ttm_squeeze(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhanced TTM Squeeze calculation with ultra-responsive histogram monitoring
        Optimized for real-time detection of 'first less negative' patterns
        """
        try:
            if len(df) < max(self.macd_slow, self.bb_period, self.kc_period):
                return df

            # Calculate MACD components with enhanced precision
            ema_fast = df['close'].ewm(span=self.macd_fast, adjust=False).mean()
            ema_slow = df['close'].ewm(span=self.macd_slow, adjust=False).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal, adjust=False).mean()
            histogram = macd_line - signal_line

            # Add MACD components
            df['macd'] = macd_line
            df['signal'] = signal_line
            df['histogram'] = histogram

            # Calculate histogram momentum (rate of change)
            df['histogram_momentum'] = histogram.diff()
            df['histogram_acceleration'] = df['histogram_momentum'].diff()

            # Calculate EMAs for trend confirmation
            df['ema5'] = df['close'].ewm(span=5, adjust=False).mean()
            df['ema8'] = df['close'].ewm(span=8, adjust=False).mean()
            df['ema21'] = df['close'].ewm(span=21, adjust=False).mean()

            # Calculate EMA trends
            df['ema5_trend'] = df['ema5'] > df['ema5'].shift(1)
            df['ema8_trend'] = df['ema8'] > df['ema8'].shift(1)
            df['ema21_trend'] = df['ema21'] > df['ema21'].shift(1)

            # Calculate Bollinger Bands (SMA ± 1.5×STD for TTM Squeeze)
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 1.5)  # TTM uses 1.5 std
            df['bb_lower'] = df['bb_middle'] - (bb_std * 1.5)

            # Calculate Keltner Channels (SMA ± 2.0×ATR for TTM Squeeze)
            df['kc_middle'] = df['close'].rolling(window=20).mean()

            # True Range calculation
            df['high_low'] = df['high'] - df['low']
            df['high_close'] = abs(df['high'] - df['close'].shift(1))
            df['low_close'] = abs(df['low'] - df['close'].shift(1))
            df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
            atr = df['true_range'].rolling(window=20).mean()

            df['kc_upper'] = df['kc_middle'] + (atr * 2.0)  # TTM uses 2.0 multiplier
            df['kc_lower'] = df['kc_middle'] - (atr * 2.0)

            # Calculate TTM Squeeze state (BB inside KC)
            df['squeeze_active'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])

            # Calculate squeeze strength (how tight the squeeze is)
            bb_width = df['bb_upper'] - df['bb_lower']
            kc_width = df['kc_upper'] - df['kc_lower']
            df['squeeze_strength'] = (kc_width - bb_width) / kc_width

            # Clean up temporary columns
            df.drop(['high_low', 'high_close', 'low_close', 'true_range'], axis=1, inplace=True, errors='ignore')

            return df

        except Exception as e:
            self.logger.error(f"Error calculating enhanced TTM Squeeze: {e}")
            return df

    def detect_consecutive_declining_bars(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        SIMPLIFIED LEE METHOD: Detect 3+ consecutive declining/red bars
        Alert when a stock has 3 or more consecutive periods where close < previous close
        """
        try:
            if len(df) < 4:  # Need at least 4 bars to check 3 consecutive declines
                return None

            # Get recent data for pattern analysis
            recent_data = df.tail(10).copy()

            # Calculate consecutive declining bars
            recent_data['is_declining'] = recent_data['close'] < recent_data['close'].shift(1)

            # Count consecutive declining bars from the most recent bar backwards
            consecutive_count = 0
            for i in range(len(recent_data) - 1, -1, -1):  # Start from most recent
                if recent_data['is_declining'].iloc[i]:
                    consecutive_count += 1
                else:
                    break  # Stop counting when we hit a non-declining bar

            # SIGNAL CRITERIA: 3 or more consecutive declining bars
            if consecutive_count >= 3:
                current_price = recent_data['close'].iloc[-1]

                # Calculate decline percentage over the consecutive period
                start_price = recent_data['close'].iloc[-(consecutive_count + 1)]
                decline_percent = ((current_price - start_price) / start_price) * 100

                # Calculate confidence based on consecutive count and decline magnitude
                base_confidence = min(0.5 + (consecutive_count - 3) * 0.1, 0.9)  # Higher confidence for more consecutive bars
                decline_factor = min(abs(decline_percent) / 10.0, 0.2)  # Bonus for larger declines
                confidence = min(base_confidence + decline_factor, 0.95)

                return {
                    'pattern_found': True,
                    'signal_type': 'consecutive_declining_bars',
                    'consecutive_count': consecutive_count,
                    'decline_percent': decline_percent,
                    'confidence': confidence,
                    'current_price': current_price,
                    'signal_direction': 'bearish',  # Declining bars indicate bearish pattern
                    'pattern_description': f'{consecutive_count} consecutive declining bars ({decline_percent:.2f}% decline)',
                    'timestamp': datetime.now(),
                    'alert_priority': 'high' if consecutive_count >= 5 else 'normal'
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting consecutive declining bars: {e}")
            return None

    def detect_squeeze_pattern(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        DEPRECATED: Old TTM Squeeze pattern detection - replaced with simplified declining bars
        Kept for backward compatibility but not used in main scanning logic
        """
        try:
            # This method is now deprecated - use detect_consecutive_declining_bars instead
            return None

            # SECONDARY SIGNAL: Both 8-EMA and 21-EMA trending upward
            emas_rising = ema8_rising and ema21_rising
            if not emas_rising:
                return None

            # ENHANCED SIGNALS (Optional but increase confidence)

            # Check for 5+ consecutive down-squeeze bars followed by uptick
            consecutive_down = 0
            for i in range(2, min(7, len(recent_data))):  # Check up to 5 bars back
                if recent_data['histogram'].iloc[-i] < recent_data['histogram'].iloc[-(i+1)]:
                    consecutive_down += 1
                else:
                    break

            has_consecutive_decline = consecutive_down >= 3  # At least 3 declining bars

            # Check 12-period momentum rising (using histogram momentum as proxy)
            momentum_rising = current_momentum > 0

            # Calculate confidence based on signals met
            confidence = self._calculate_enhanced_confidence(
                histogram_rising, emas_rising, has_consecutive_decline,
                momentum_rising, ema5_rising, consecutive_down
            )

            # Determine signal strength
            signal_strength = self._determine_signal_strength(confidence, consecutive_down, current_momentum)

            return {
                'pattern_found': True,
                'signal_type': 'ttm_squeeze_momentum_shift',
                'signal_direction': 'bullish',
                'confidence': confidence,
                'signal_strength': signal_strength,
                'histogram_current': current_hist,
                'histogram_previous': prev_hist,
                'histogram_rising': histogram_rising,
                'ema8_rising': ema8_rising,
                'ema21_rising': ema21_rising,
                'ema5_rising': ema5_rising,
                'consecutive_decline_bars': consecutive_down,
                'momentum_rising': momentum_rising,
                'squeeze_active': recent_data['squeeze_active'].iloc[-1],
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error detecting squeeze pattern: {e}")
            return None

    def _calculate_enhanced_confidence(self, histogram_rising: bool, emas_rising: bool,
                                     has_consecutive_decline: bool, momentum_rising: bool,
                                     ema5_rising: bool, consecutive_down: int) -> float:
        """Calculate confidence score for enhanced TTM Squeeze pattern"""
        try:
            base_confidence = 0.0

            # PRIMARY SIGNALS (Required - 60% weight)
            if histogram_rising:
                base_confidence += 0.30  # 30% for histogram rising
            if emas_rising:
                base_confidence += 0.30  # 30% for both EMAs rising

            # ENHANCED SIGNALS (Optional - 40% weight)
            if has_consecutive_decline:
                base_confidence += 0.15  # 15% for proper decline pattern
                # Bonus for longer decline (more significant reversal)
                if consecutive_down >= 5:
                    base_confidence += 0.05

            if momentum_rising:
                base_confidence += 0.10  # 10% for momentum confirmation

            if ema5_rising:
                base_confidence += 0.10  # 10% for short-term trend confirmation

            return min(base_confidence, 0.95)  # Cap at 95%

        except Exception as e:
            self.logger.error(f"Error calculating enhanced confidence: {e}")
            return 0.5

    def _determine_signal_strength(self, confidence: float, consecutive_down: int, momentum: float) -> str:
        """Determine signal strength based on confidence and pattern characteristics"""
        try:
            if confidence >= 0.8:
                return "STRONG"
            elif confidence >= 0.65:
                return "MODERATE"
            elif confidence >= 0.5:
                return "WEAK"
            else:
                return "VERY_WEAK"

        except Exception as e:
            self.logger.error(f"Error determining signal strength: {e}")
            return "MODERATE"

    def detect_first_less_negative_pattern(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Specialized detection for the exact 'first less negative' histogram bar pattern
        This is the ultra-responsive yellow signal detection
        """
        try:
            if len(df) < 6:  # Need at least 6 bars for pattern
                return None

            recent_data = df.tail(6).copy()

            # Get histogram values
            current_hist = recent_data['histogram'].iloc[-1]
            prev_hist = recent_data['histogram'].iloc[-2]
            hist_2 = recent_data['histogram'].iloc[-3]
            hist_3 = recent_data['histogram'].iloc[-4]

            # CRITICAL PATTERN: First less negative bar after decline
            # 1. Current histogram > previous histogram (less negative/more positive)
            # 2. Previous bars were declining (more negative)
            # 3. Current bar is still negative but improving (the "yellow" signal)

            is_less_negative = current_hist > prev_hist
            if not is_less_negative:
                return None

            # Check for declining pattern in previous bars
            has_decline = prev_hist < hist_2 and hist_2 < hist_3
            if not has_decline:
                return None

            # Verify this is the "first" less negative bar (not a continuation)
            is_first_improvement = prev_hist < hist_2  # Previous was still declining

            # Calculate the improvement magnitude
            improvement_magnitude = abs(current_hist - prev_hist)
            relative_improvement = improvement_magnitude / abs(prev_hist) if prev_hist != 0 else 0

            # Get EMA confirmations
            ema8_rising = recent_data['ema8_trend'].iloc[-1] if 'ema8_trend' in recent_data.columns else False
            ema21_rising = recent_data['ema21_trend'].iloc[-1] if 'ema21_trend' in recent_data.columns else False

            # Calculate confidence for this specific pattern
            confidence = 0.5  # Base confidence
            if is_first_improvement:
                confidence += 0.2
            if ema8_rising:
                confidence += 0.15
            if ema21_rising:
                confidence += 0.15
            if relative_improvement > 0.1:  # Significant improvement
                confidence += 0.1

            return {
                'pattern_found': True,
                'signal_type': 'first_less_negative',
                'signal_direction': 'bullish_momentum_shift',
                'confidence': min(confidence, 0.95),
                'histogram_current': current_hist,
                'histogram_previous': prev_hist,
                'improvement_magnitude': improvement_magnitude,
                'relative_improvement': relative_improvement,
                'is_first_improvement': is_first_improvement,
                'ema8_rising': ema8_rising,
                'ema21_rising': ema21_rising,
                'timestamp': datetime.now().isoformat(),
                'alert_priority': 'HIGH'  # This is the critical signal
            }

        except Exception as e:
            self.logger.error(f"Error detecting first less negative pattern: {e}")
            return None

    def _create_enhanced_signal(self, symbol: str, df: pd.DataFrame, pattern_result: Dict[str, Any]) -> LeeMethodSignal:
        """Create enhanced LeeMethodSignal from pattern detection results"""
        try:
            current_data = df.iloc[-1]

            # Calculate target price and stop loss
            current_price = current_data['close']
            target_price = current_price * 1.02  # 2% target
            stop_loss = current_price * 0.98     # 2% stop loss

            # Create the signal
            signal = LeeMethodSignal(
                symbol=symbol,
                signal_type=pattern_result.get('signal_type', 'ttm_squeeze'),
                entry_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=pattern_result.get('confidence', 0.5),
                timeframe='daily',
                timestamp=datetime.now(),
                histogram_sequence=[],  # Will be populated later
                momentum_bars=[],  # Will be populated later
                momentum_confirmation=pattern_result.get('momentum_rising', False),
                histogram_current=pattern_result.get('histogram_current', 0.0),
                histogram_previous=pattern_result.get('histogram_previous', 0.0),
                signal_direction=pattern_result.get('signal_direction', 'bullish'),
                strength="STRONG" if pattern_result.get('confidence', 0.5) >= 0.8 else
                        "MODERATE" if pattern_result.get('confidence', 0.5) >= 0.6 else
                        "WEAK",
                current_price=current_price,
                ema5_uptrend=pattern_result.get('ema5_rising', False),
                ema8_uptrend=pattern_result.get('ema8_rising', False),
                ema21_uptrend=pattern_result.get('ema21_rising', False),
                squeeze_active=pattern_result.get('squeeze_active', False),
                weekly_trend='bullish',  # Default value
                daily_trend='bullish',   # Default value
                trend_alignment=True     # Default value
            )

            return signal

        except Exception as e:
            self.logger.error(f"Error creating enhanced signal for {symbol}: {e}")
            return None

    async def initialize(self) -> bool:
        """Initialize the Lee Method scanner"""
        try:
            # Test API connection
            test_symbol = "AAPL"
            df = await self.fetch_historical_data(test_symbol, limit=10)

            if df.empty:
                self.logger.warning("Lee Method Scanner: API test returned empty data (may be expected in test mode)")
            else:
                self.logger.info(f"[OK] Lee Method Scanner: API connection tested successfully with {test_symbol}")

            return True

        except Exception as e:
            self.logger.error(f"Lee Method Scanner initialization failed: {e}")
            return False

    async def fetch_historical_data(self, symbol: str, timeframe: str = "1day", limit: int = 100) -> pd.DataFrame:
        """Fetch historical data using enhanced market data manager with intelligent fallback"""
        try:
            self.logger.debug(f"Fetching historical data for {symbol} (timeframe: {timeframe}, limit: {limit})")

            # Use enhanced market data manager for intelligent data fetching
            historical_data = await enhanced_market_data.get_historical_data(symbol, timeframe='1d')

            if historical_data and historical_data.data is not None and not historical_data.data.empty:
                df = historical_data.data.copy()

                # Ensure we have the required columns
                required_columns = ['open', 'high', 'low', 'close', 'volume']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    self.logger.warning(f"Missing columns {missing_columns} for {symbol}, attempting to fill")
                    # Fill missing columns with reasonable defaults
                    for col in missing_columns:
                        if col == 'volume':
                            df[col] = 1000000  # Default volume
                        elif col in ['open', 'high', 'low']:
                            df[col] = df.get('close', 0)  # Use close price as fallback

                # Limit to requested number of periods
                if len(df) > limit:
                    df = df.tail(limit)

                # Ensure chronological order
                if 'date' in df.columns:
                    df = df.sort_values('date')
                elif df.index.name and 'date' in str(df.index.name).lower():
                    df = df.sort_index()

                # Validate data quality
                if len(df) < 20:  # Need minimum data for TTM Squeeze
                    self.logger.warning(f"Insufficient data for {symbol}: {len(df)} periods (need 20+)")
                    return pd.DataFrame()  # Return empty DataFrame

                self.logger.info(f"Successfully fetched {len(df)} periods for {symbol} from {historical_data.source}")
                return df

            else:
                self.logger.error(f"No historical data available for {symbol}")
                return pd.DataFrame()  # Return empty DataFrame instead of None

        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return pd.DataFrame()

    def calculate_lee_method_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate TTM Squeeze indicators (branded as Lee Method)"""
        try:
            if len(df) < max(self.macd_slow, self.bb_period, self.kc_period):
                return df

            # Calculate TTM Squeeze MACD components
            ema_fast = df['close'].ewm(span=self.macd_fast).mean()
            ema_slow = df['close'].ewm(span=self.macd_slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal).mean()
            histogram = macd_line - signal_line

            # Add MACD components to dataframe
            df['macd'] = macd_line
            df['signal'] = signal_line
            df['histogram'] = histogram

            # Calculate EMA 5 and EMA 8 for trend confirmation
            df['ema5'] = df['close'].ewm(span=self.ema5_period).mean()
            df['ema8'] = df['close'].ewm(span=self.ema8_period).mean()

            # Calculate all EMAs for compatibility
            for period in self.ema_periods:
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()

            # Calculate Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=self.bb_period).mean()
            bb_std = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_std)

            # Calculate Keltner Channels
            df['kc_middle'] = df['close'].rolling(window=self.kc_period).mean()
            # True Range calculation for Keltner Channels
            df['high_low'] = df['high'] - df['low']
            df['high_close'] = abs(df['high'] - df['close'].shift(1))
            df['low_close'] = abs(df['low'] - df['close'].shift(1))
            df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
            atr = df['true_range'].rolling(window=self.kc_period).mean()
            df['kc_upper'] = df['kc_middle'] + (atr * self.kc_multiplier)
            df['kc_lower'] = df['kc_middle'] - (atr * self.kc_multiplier)

            # Calculate TTM Squeeze state (BB inside KC)
            df['squeeze_active'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])

            # Clean up temporary columns
            df.drop(['high_low', 'high_close', 'low_close', 'true_range'], axis=1, inplace=True, errors='ignore')

            return df

        except Exception as e:
            self.logger.error(f"Error calculating TTM Squeeze indicators: {e}")
            return df

    def detect_lee_method_pattern_flexible(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Flexible Lee Method pattern detection with relaxed criteria
        Uses multiple pattern combinations and graduated confidence scoring
        """
        try:
            if len(df) < 10:  # Need at least 10 bars for pattern analysis
                return None

            # Check individual pattern components with new relaxed criteria
            decline_result = self._check_histogram_decline_pattern(df)
            rebound_result = self._check_histogram_rebound_signal(df)
            ema5_uptrend = self._check_ema5_uptrend(df)
            ema8_uptrend = self._check_ema8_uptrend(df)
            squeeze_ok = self._check_squeeze_filter(df)

            # Calculate base confidence from individual components
            base_confidence = 0.3  # Start with base confidence

            # Add confidence boosts from each component
            if decline_result['pattern_found']:
                base_confidence += decline_result.get('confidence_boost', 0.15)

            if rebound_result['rebound_found']:
                base_confidence += rebound_result.get('confidence_boost', 0.15)

            if ema5_uptrend:
                base_confidence += 0.10

            if ema8_uptrend:
                base_confidence += 0.10

            if squeeze_ok:
                base_confidence += 0.05

            # Determine if pattern is found based on flexible criteria
            pattern_found = False
            signal_strength = "weak"

            # Multiple ways to qualify as a pattern:

            # Strong pattern: Most components present
            strong_components = sum([
                decline_result['pattern_found'],
                rebound_result['rebound_found'],
                ema5_uptrend,
                ema8_uptrend
            ])

            if strong_components >= 3:
                pattern_found = True
                signal_strength = "strong"
                base_confidence += 0.10

            # Medium pattern: Key components present
            elif (decline_result['pattern_found'] and rebound_result['rebound_found']) or \
                 (rebound_result['rebound_found'] and ema5_uptrend and ema8_uptrend):
                pattern_found = True
                signal_strength = "medium"
                base_confidence += 0.05

            # Weak pattern: Minimum viable combination
            elif rebound_result['rebound_found'] and (ema5_uptrend or ema8_uptrend):
                pattern_found = True
                signal_strength = "weak"

            # Cap confidence at reasonable levels
            base_confidence = min(base_confidence, 0.95)

            if pattern_found:
                # Get histogram sequence for compatibility
                hist_sequence = df['histogram'].tail(6).tolist() if len(df) >= 6 else df['histogram'].tail(4).tolist()

                return {
                    'pattern_found': True,
                    'confidence': base_confidence,
                    'signal_direction': 'bullish_momentum',
                    'signal_strength': signal_strength,
                    'sequence': hist_sequence,  # For compatibility with original code
                    'histogram_sequence': hist_sequence,  # For compatibility
                    'momentum_confirmation': True,  # Assume true for flexible patterns
                    'trend_analysis': {
                        'weekly_trend': 'bullish',
                        'daily_trend': 'bullish',
                        'trend_alignment': True
                    },
                    'components': {
                        'histogram_decline': decline_result['pattern_found'],
                        'histogram_rebound': rebound_result['rebound_found'],
                        'ema5_uptrend': ema5_uptrend,
                        'ema8_uptrend': ema8_uptrend,
                        'squeeze_filter': squeeze_ok
                    },
                    'pattern_details': {
                        'decline_type': decline_result.get('pattern_type', 'none'),
                        'rebound_type': rebound_result.get('rebound_type', 'none'),
                        'strong_components': strong_components
                    }
                }
            else:
                return {
                    'pattern_found': False,
                    'confidence': base_confidence,
                    'reason': 'insufficient_components',
                    'components': {
                        'histogram_decline': decline_result['pattern_found'],
                        'histogram_rebound': rebound_result['rebound_found'],
                        'ema5_uptrend': ema5_uptrend,
                        'ema8_uptrend': ema8_uptrend,
                        'squeeze_filter': squeeze_ok
                    }
                }

        except Exception as e:
            self.logger.error(f"Error in flexible pattern detection: {e}")
            return None

    def detect_lee_method_pattern(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Detect Lee Method pattern based on modified 3+ bar TTM Squeeze criteria:

        Pattern Logic (modified for 3+ consecutive declining bars):
        MovAvgExponential("length" = 5)."AvgExp" is greater than MovAvgExponential("length" = 5)."AvgExp" from 1 bars ago;
        TTM_Squeeze()."Histogram" is greater than TTM_Squeeze()."Histogram" from 1 bars ago and
        TTM_Squeeze()."Histogram" from 1 bars ago is less than TTM_Squeeze()."Histogram" from 2 bars ago and
        TTM_Squeeze()."Histogram" from 2 bars ago is less than TTM_Squeeze()."Histogram" from 3 bars ago;
        MovAvgExponential("length" = 8)."AvgExp" is greater than MovAvgExponential("length" = 8)."AvgExp" from 3 bars ago;
        Momentum()."Momentum" is greater than Momentum()."Momentum" from 3 bars ago;
        def fiveDotsInARow = Sum(TTM_Squeeze().SqueezeAlert == 0, 3);

        MODIFIED ALGORITHM - 3+ Bar Decline Pattern:
        1. Initial Signal Bar: First yellow/less-negative histogram bar after decline
        2. Decline Pattern: MINIMUM 3 consecutive declining histogram bars (reduced from 5)
        3. Histogram Validation: Current histogram > previous AND both negative (rebound signal)
        4. EMA 5 Uptrend: EMA5[0] > EMA5[1] (current bar uptrend)
        5. EMA 8 Uptrend: EMA8[0] > EMA8[3] (3-bar uptrend confirmation)
        6. Momentum Confirmation: Momentum[0] > Momentum[3]
        7. Optional TTM Squeeze Filter: 3 consecutive squeeze dots (fiveDotsInARow)
        """
        try:
            if len(df) < 10:  # Need at least 10 bars for pattern analysis
                return None

            # Get recent data for pattern analysis
            recent_data = df.tail(10).copy()

            # Ensure we have all required indicators (using actual column names)
            required_cols = ['histogram', 'ema5', 'ema8', 'squeeze_active']
            if not all(col in recent_data.columns for col in required_cols):
                return None

            # Extract current values (most recent bar = index -1)
            current_hist = recent_data['histogram'].iloc[-1]
            hist_1 = recent_data['histogram'].iloc[-2]
            hist_2 = recent_data['histogram'].iloc[-3]
            hist_3 = recent_data['histogram'].iloc[-4]
            hist_4 = recent_data['histogram'].iloc[-5]
            hist_5 = recent_data['histogram'].iloc[-6]

            current_ema5 = recent_data['ema5'].iloc[-1]
            prev_ema5 = recent_data['ema5'].iloc[-2]

            current_ema8 = recent_data['ema8'].iloc[-1]
            ema8_3bars = recent_data['ema8'].iloc[-4]

            # Use histogram as momentum (MACD histogram represents momentum)
            current_momentum = current_hist
            momentum_3bars = hist_3

            # Check all conditions from the ThinkScript pattern

            # Condition 1: EMA 5 uptrend (current > previous)
            ema5_uptrend = current_ema5 > prev_ema5
            if not ema5_uptrend:
                return None

            # Condition 2: Histogram rebound pattern with 3+ consecutive declining bars
            # Current histogram > previous histogram (rebound signal)
            # AND minimum 3 consecutive declining bars: hist_1 < hist_2 < hist_3
            histogram_rebound = current_hist > hist_1
            if not histogram_rebound:
                return None

            # Condition 3: Minimum 3 consecutive declining bars (MODIFIED from 5 bars)
            # Only requires: hist_1 < hist_2 < hist_3 (3 consecutive declining bars)
            three_bar_decline = (hist_1 < hist_2 and hist_2 < hist_3)
            if not three_bar_decline:
                return None

            # Condition 4: EMA 8 uptrend over 3 bars
            ema8_uptrend = current_ema8 > ema8_3bars
            if not ema8_uptrend:
                return None

            # Condition 5: Momentum confirmation over 3 bars
            momentum_uptrend = current_momentum > momentum_3bars
            if not momentum_uptrend:
                return None

            # Condition 6: Optional TTM Squeeze filter (3 consecutive squeeze dots)
            squeeze_filter_ok = True
            if self.require_squeeze_confirmation:
                # Check for 3 consecutive squeeze states (squeeze_active == True)
                squeeze_count = recent_data['squeeze_active'].tail(3).sum()
                squeeze_filter_ok = squeeze_count >= 3
                if not squeeze_filter_ok:
                    return None

            # CRITICAL FIX: Validate histogram values before calculations
            histogram_values = [hist_5, hist_4, hist_3, hist_2, hist_1, current_hist]

            # Check for NaN or infinite values
            if any(pd.isna(val) or np.isinf(val) for val in histogram_values):
                logger.error("CRITICAL: NaN or infinite values detected in histogram sequence")
                return None

            # Check for extreme values that could indicate calculation errors
            if any(abs(val) > 1e6 for val in histogram_values):
                logger.error("CRITICAL: Extreme histogram values detected - possible calculation error")
                return None

            # Calculate pattern strength based on histogram sequence
            histogram_sequence = histogram_values
            decline_strength = self._calculate_decline_strength(histogram_sequence)

            # CRITICAL FIX: Use mathematical safeguards for rebound strength calculation
            rebound_strength = math_safeguards.safe_divide(
                abs(current_hist - hist_1),
                max(abs(hist_1), 1e-10),  # Prevent division by zero
                default=abs(current_hist)  # Fallback to absolute current value
            )

            # Additional validation
            if not math_safeguards.validate_calculation_inputs(rebound_strength):
                logger.error("Invalid rebound strength calculation result")
                return None

            # Calculate overall confidence
            confidence = self._calculate_pattern_confidence(
                ema5_uptrend, histogram_rebound, three_bar_decline,
                ema8_uptrend, momentum_uptrend, squeeze_filter_ok,
                decline_strength, rebound_strength
            )

            return {
                'pattern_found': True,
                'signal_type': 'lee_method_ttm_squeeze',
                'signal_direction': 'bullish_momentum',
                'histogram_sequence': histogram_sequence,
                'current_histogram': current_hist,
                'histogram_rebound': histogram_rebound,
                'three_bar_decline': three_bar_decline,  # Updated from extended_decline
                'ema5_uptrend': ema5_uptrend,
                'ema8_uptrend': ema8_uptrend,
                'momentum_uptrend': momentum_uptrend,
                'squeeze_filter_ok': squeeze_filter_ok,
                'decline_strength': decline_strength,
                'rebound_strength': rebound_strength,
                'confidence': confidence,
                'entry_index': len(recent_data) - 1,  # Current bar
                'timestamp': datetime.now().isoformat(),
                'pattern_type': '3_bar_ttm_squeeze'  # Updated to reflect 3-bar pattern
            }

        except Exception as e:
            self.logger.error(f"Error detecting TTM Squeeze pattern: {e}")
            return None

    def _calculate_decline_strength(self, histogram_sequence) -> float:
        """Calculate the strength of the histogram decline pattern"""
        try:
            # Convert to list if it's a numpy array
            if hasattr(histogram_sequence, 'tolist'):
                histogram_sequence = histogram_sequence.tolist()

            if len(histogram_sequence) < 2:
                return 0.0

            # Calculate the total decline from peak to trough
            decline_bars = histogram_sequence[:-1] if len(histogram_sequence) > 1 else histogram_sequence
            if not decline_bars:
                return 0.0

            # Find the steepest decline
            max_decline = 0.0
            for i in range(len(decline_bars) - 1):
                decline = abs(decline_bars[i] - decline_bars[i + 1])
                max_decline = max(max_decline, decline)

            # Normalize decline strength (0.0 to 1.0)
            return min(max_decline * 10, 1.0)  # Scale factor for normalization

        except Exception as e:
            self.logger.error(f"Error calculating decline strength: {e}")
            return 0.0

    def _calculate_pattern_confidence(self, ema5_uptrend: bool, histogram_rebound: bool,
                                    three_bar_decline: bool, ema8_uptrend: bool,
                                    momentum_uptrend: bool, squeeze_filter_ok: bool,
                                    decline_strength: float, rebound_strength: float) -> float:
        """Calculate overall pattern confidence score (0.0 to 1.0) - Updated for 3-bar decline pattern"""
        try:
            confidence = 0.0

            # Base confidence from required conditions (60% total)
            if ema5_uptrend:
                confidence += 0.15  # 15%
            if histogram_rebound:
                confidence += 0.20  # 20%
            if three_bar_decline:  # Updated from extended_decline
                confidence += 0.15  # 15%
            if ema8_uptrend:
                confidence += 0.10  # 10%

            # Momentum confirmation (20%)
            if momentum_uptrend:
                confidence += 0.20

            # Squeeze filter bonus (10%)
            if squeeze_filter_ok:
                confidence += 0.10

            # Pattern strength adjustments (10%)
            strength_bonus = (decline_strength + min(rebound_strength, 0.5)) * 0.05
            confidence += strength_bonus

            return min(confidence, 1.0)  # Cap at 100%

        except Exception as e:
            self.logger.error(f"Error calculating pattern confidence: {e}")
            return 0.5  # Default moderate confidence

    def _check_histogram_decline_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for histogram decline pattern with relaxed criteria"""
        try:
            if len(df) < 4:  # Need at least 4 bars to check patterns
                return {'pattern_found': False, 'reason': 'insufficient_data'}

            # Get the last 6 histogram values for more flexible pattern detection
            hist_values = df['histogram'].tail(6).values if len(df) >= 6 else df['histogram'].tail(4).values

            # RELAXED CRITERIA: Multiple decline patterns

            # Pattern 1: Original strict 3 consecutive declining bars
            if len(hist_values) >= 4:
                decline_1 = hist_values[-2] < hist_values[-3]  # 2nd to last < 3rd to last
                decline_2 = hist_values[-3] < hist_values[-4]  # 3rd to last < 4th to last
                strict_decline = decline_1 and decline_2
            else:
                strict_decline = False

            # Pattern 2: 2 out of 3 declining bars (more flexible)
            if len(hist_values) >= 4:
                declines = [
                    hist_values[-2] < hist_values[-3],
                    hist_values[-3] < hist_values[-4],
                    hist_values[-1] < hist_values[-2] if len(hist_values) >= 2 else False
                ]
                flexible_decline = sum(declines) >= 2
            else:
                flexible_decline = False

            # Pattern 3: Overall downward trend over 4-5 bars
            if len(hist_values) >= 5:
                trend_decline = hist_values[-1] < hist_values[-5] and hist_values[-2] < hist_values[-4]
            elif len(hist_values) >= 4:
                trend_decline = hist_values[-1] < hist_values[-4]
            else:
                trend_decline = False

            # Pattern 4: Recent weakness (current bar weaker than recent average)
            if len(hist_values) >= 4:
                recent_avg = np.mean(hist_values[-4:-1])  # Average of last 3 bars (excluding current)
                current_weakness = hist_values[-1] < recent_avg
            else:
                current_weakness = False

            # Determine pattern type and confidence
            pattern_found = False
            pattern_type = 'none'
            confidence_boost = 0.0

            if strict_decline:
                pattern_found = True
                pattern_type = 'strict_three_declining'
                confidence_boost = 0.25
            elif flexible_decline:
                pattern_found = True
                pattern_type = 'flexible_decline'
                confidence_boost = 0.20
            elif trend_decline:
                pattern_found = True
                pattern_type = 'trend_decline'
                confidence_boost = 0.15
            elif current_weakness:
                pattern_found = True
                pattern_type = 'current_weakness'
                confidence_boost = 0.10

            if pattern_found:
                return {
                    'pattern_found': True,
                    'sequence': hist_values.tolist(),
                    'pattern_type': pattern_type,
                    'confidence_boost': confidence_boost,
                    'decline_strength': self._calculate_decline_strength(hist_values)
                }
            else:
                return {
                    'pattern_found': False,
                    'reason': 'no_decline_pattern_detected',
                    'sequence': hist_values.tolist()
                }

        except Exception as e:
            self.logger.error(f"Error checking histogram decline pattern: {e}")
            return {'pattern_found': False, 'reason': f'error: {e}'}

    def _check_momentum_confirmation(self, momentum_values: np.ndarray, increase_index: int) -> bool:
        """Check if momentum is greater than prior momentum bar (legacy compatibility)"""
        try:
            if increase_index < 1 or increase_index >= len(momentum_values):
                return False

            current_momentum = momentum_values[increase_index]
            prior_momentum = momentum_values[increase_index - 1]

            return current_momentum > prior_momentum

        except Exception as e:
            self.logger.error(f"Error checking momentum confirmation: {e}")
            return False

    def _check_histogram_rebound_signal(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for histogram rebound signal with relaxed criteria"""
        try:
            if len(df) < 2:
                return {'rebound_found': False, 'reason': 'insufficient_data'}

            # Get current and previous histogram values
            current_hist = df['histogram'].iloc[-1]
            previous_hist = df['histogram'].iloc[-2]

            # RELAXED CRITERIA: Multiple rebound patterns
            hist_increased = current_hist > previous_hist

            # Pattern 1: Original strict rebound (both negative, current > previous)
            strict_rebound = hist_increased and current_hist < 0 and previous_hist < 0

            # Pattern 2: Momentum improvement (histogram moving toward positive)
            momentum_improvement = hist_increased and previous_hist < 0

            # Pattern 3: Trend reversal (histogram stopped declining significantly)
            if len(df) >= 3:
                hist_2bars = df['histogram'].iloc[-3]
                trend_reversal = (current_hist > previous_hist and
                                previous_hist < hist_2bars and
                                abs(current_hist - previous_hist) > 0.01)  # Minimum improvement
            else:
                trend_reversal = False

            # Pattern 4: Relative strength (histogram less negative than recent average)
            if len(df) >= 5:
                recent_avg = df['histogram'].tail(5).mean()
                relative_strength = current_hist > recent_avg and hist_increased
            else:
                relative_strength = False

            # Determine rebound type and strength
            rebound_found = False
            rebound_type = 'none'
            confidence_boost = 0.0

            if strict_rebound:
                rebound_found = True
                rebound_type = 'strict_negative_rebound'
                confidence_boost = 0.25
            elif momentum_improvement:
                rebound_found = True
                rebound_type = 'momentum_improvement'
                confidence_boost = 0.20
            elif trend_reversal:
                rebound_found = True
                rebound_type = 'trend_reversal'
                confidence_boost = 0.15
            elif relative_strength:
                rebound_found = True
                rebound_type = 'relative_strength'
                confidence_boost = 0.10

            if rebound_found:
                return {
                    'rebound_found': True,
                    'current_hist': current_hist,
                    'previous_hist': previous_hist,
                    'improvement': abs(current_hist - previous_hist),
                    'rebound_type': rebound_type,
                    'confidence_boost': confidence_boost
                }
            else:
                return {
                    'rebound_found': False,
                    'current_hist': current_hist,
                    'previous_hist': previous_hist,
                    'reason': f'No rebound pattern detected'
                }

        except Exception as e:
            self.logger.error(f"Error checking histogram rebound signal: {e}")
            return {'rebound_found': False, 'reason': f'error: {e}'}

    def _check_ema5_uptrend(self, df: pd.DataFrame) -> bool:
        """Check if EMA 5 is trending upward: ema5 > ema5[1]"""
        try:
            if len(df) < 2:
                return False

            current_ema5 = df['ema5'].iloc[-1]
            previous_ema5 = df['ema5'].iloc[-2]

            return current_ema5 > previous_ema5

        except Exception as e:
            self.logger.error(f"Error checking EMA5 uptrend: {e}")
            return False

    def _check_ema8_uptrend(self, df: pd.DataFrame) -> bool:
        """Check if EMA 8 is trending upward: ema8 > ema8[1]"""
        try:
            if len(df) < 2:
                return False

            current_ema8 = df['ema8'].iloc[-1]
            previous_ema8 = df['ema8'].iloc[-2]

            return current_ema8 > previous_ema8

        except Exception as e:
            self.logger.error(f"Error checking EMA8 uptrend: {e}")
            return False

    def _check_squeeze_filter(self, df: pd.DataFrame) -> bool:
        """Check optional TTM Squeeze state filter"""
        try:
            if not self.require_squeeze:
                return True  # Skip squeeze filter if not required

            # Check squeeze state at specified lookback
            if len(df) <= self.squeeze_lookback:
                return False

            squeeze_index = -(self.squeeze_lookback + 1)
            squeeze_active = df['squeeze_active'].iloc[squeeze_index]

            return squeeze_active

        except Exception as e:
            self.logger.error(f"Error checking squeeze filter: {e}")
            return not self.require_squeeze  # Default to True if not required

    def _analyze_multi_timeframe_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze multi-timeframe trends for confirmation"""
        try:
            if len(df) < 50:  # Need enough data for trend analysis
                return {
                    'daily_trend': 'neutral',
                    'weekly_trend': 'neutral',
                    'trend_alignment': False,
                    'ema_alignment': False
                }
            
            current = df.iloc[-1]
            
            # Daily trend (based on EMA alignment)
            daily_trend = 'bullish' if current['ema_5'] > current['ema_21'] else 'bearish'
            
            # Weekly trend (simplified - based on longer EMAs)
            weekly_trend = 'bullish' if current['ema_21'] > current['ema_50'] else 'bearish'
            
            # Trend alignment
            trend_alignment = (
                (daily_trend == 'bullish' and weekly_trend == 'bullish') or
                (daily_trend == 'bearish' and weekly_trend == 'bearish')
            )
            
            return {
                'daily_trend': daily_trend,
                'weekly_trend': weekly_trend,
                'trend_alignment': trend_alignment,
                'ema_alignment': current['ema_5'] > current['ema_8'] > current['ema_21']
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing multi-timeframe trends: {e}")
            return {
                'daily_trend': 'neutral',
                'weekly_trend': 'neutral',
                'trend_alignment': False,
                'ema_alignment': False
            }

    def _determine_signal_direction(self, pattern_result: Dict[str, Any], 
                                  momentum_values: np.ndarray, 
                                  trend_analysis: Dict[str, Any]) -> str:
        """Determine signal direction based on pattern and trends"""
        try:
            # Check if histogram is moving from negative to positive (bullish)
            histogram_sequence = pattern_result['sequence']
            
            if len(histogram_sequence) >= 2:
                if histogram_sequence[-1] > histogram_sequence[-2]:
                    if trend_analysis['trend_alignment'] and trend_analysis['daily_trend'] == 'bullish':
                        return 'bullish_momentum'
                    elif histogram_sequence[-1] > 0:
                        return 'bullish_momentum'
                    else:
                        return 'bearish_momentum'
            
            return 'neutral'
            
        except Exception as e:
            self.logger.error(f"Error determining signal direction: {e}")
            return 'neutral'

    def _calculate_confidence(self, pattern_result: Dict[str, Any],
                            trend_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the signal (legacy compatibility)"""
        try:
            confidence = 0.5  # Base confidence

            # Add confidence for strong pattern
            if pattern_result.get('decrease_count', 0) >= 4:
                confidence += 0.2

            # Add confidence for trend alignment
            if trend_analysis.get('trend_alignment', False):
                confidence += 0.2

            # Add confidence for EMA alignment
            if trend_analysis.get('ema_alignment', False):
                confidence += 0.1

            return min(1.0, confidence)

        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5

    def _calculate_ttm_confidence(self, histogram_decline: Dict[str, Any], rebound_signal: Dict[str, Any],
                                ema5_uptrend: bool, ema8_uptrend: bool, squeeze_ok: bool) -> float:
        """Calculate confidence score for TTM Squeeze 5-point pattern"""
        try:
            base_confidence = 0.5

            # Point 1: Histogram decline pattern (20% weight)
            if histogram_decline.get('pattern_found', False):
                base_confidence += 0.2

            # Point 2: Rebound signal strength (25% weight)
            if rebound_signal.get('rebound_found', False):
                base_confidence += 0.25
                # Bonus for stronger rebound
                improvement = rebound_signal.get('improvement', 0)
                if improvement > 0.1:  # Significant improvement
                    base_confidence += 0.05

            # Point 3: EMA 5 uptrend (15% weight)
            if ema5_uptrend:
                base_confidence += 0.15

            # Point 4: EMA 8 uptrend (15% weight)
            if ema8_uptrend:
                base_confidence += 0.15

            # Point 5: Squeeze filter (10% weight if required)
            if self.require_squeeze:
                if squeeze_ok:
                    base_confidence += 0.1
            else:
                base_confidence += 0.05  # Small bonus for not requiring squeeze

            return min(base_confidence, 0.95)  # Cap at 95%

        except Exception as e:
            self.logger.error(f"Error calculating TTM confidence: {e}")
            return 0.5

    async def scan_symbol(self, symbol: str) -> Optional[LeeMethodSignal]:
        """Scan a single symbol for Lee Method patterns with enhanced TTM Squeeze detection"""
        try:
            # Check market hours first - don't generate patterns when markets are closed
            if not self._is_market_hours():
                self.logger.debug(f"Skipping {symbol} scan - markets are closed")
                return None

            # Fetch historical data with graceful error handling
            df = await self.fetch_historical_data(symbol, limit=100)
            if df.empty:
                self.logger.warning(f"No historical data available for {symbol} - skipping scan")
                return None

            # Calculate enhanced TTM Squeeze indicators
            df_with_indicators = self.calculate_ttm_squeeze(df)

            # SIMPLIFIED LEE METHOD: Check for 3+ consecutive declining bars
            declining_pattern = self.detect_consecutive_declining_bars(df_with_indicators)
            if declining_pattern and declining_pattern['pattern_found']:
                # Create signal for consecutive declining bars pattern
                return self._create_enhanced_signal(symbol, df_with_indicators, declining_pattern)

            # Fallback to original Lee Method detection for compatibility
            if self.use_flexible_patterns:
                pattern_result = self.detect_lee_method_pattern_flexible(df_with_indicators)
            else:
                pattern_result = self.detect_lee_method_pattern(df_with_indicators)

            # Check if pattern meets minimum requirements
            if not pattern_result or not pattern_result['pattern_found']:
                return None

            # Check confidence threshold
            if pattern_result['confidence'] < self.min_confidence_threshold:
                return None

            # Check signal strength if weak signals are not allowed
            if not self.allow_weak_signals and pattern_result.get('signal_strength') == 'weak':
                return None
            
            # Get current market price for real-time data
            current_market_price = df_with_indicators['close'].iloc[-1]  # Default to historical
            price_change = 0.0
            price_change_percent = 0.0

            # Try to get real-time price if market engine is available
            if self.market_engine:
                try:
                    quote = await self.market_engine.get_quote(symbol)
                    if quote:
                        current_market_price = quote.price
                        price_change = quote.change
                        price_change_percent = quote.change_percent
                except Exception as e:
                    self.logger.warning(f"Could not get real-time price for {symbol}: {e}")

            # Calculate entry, target, and stop prices
            entry_price = current_market_price
            
            # Calculate target and stop based on signal direction
            if pattern_result['signal_direction'] == 'bullish_momentum':
                target_price = entry_price * (1 + self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 - self.default_risk_percent / 100)
            else:
                target_price = entry_price * (1 - self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 + self.default_risk_percent / 100)
            
            # Calculate risk/reward ratio
            risk_reward_ratio = abs(target_price - entry_price) / abs(entry_price - stop_loss)
            
            # Create Lee Method signal
            signal = LeeMethodSignal(
                symbol=symbol,
                signal_type=pattern_result['signal_direction'],
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=pattern_result['confidence'],
                timeframe='daily',
                timestamp=datetime.now(),
                # Lee Method specific data (required fields)
                histogram_sequence=pattern_result['histogram_sequence'],
                momentum_bars=df_with_indicators['histogram'].tail(5).tolist(),
                momentum_confirmation=pattern_result['momentum_confirmation'],
                # TTM Squeeze specific fields
                histogram_current=df_with_indicators['histogram'].iloc[-1],
                histogram_previous=df_with_indicators['histogram'].iloc[-2] if len(df_with_indicators) > 1 else 0.0,
                # Additional signal fields
                signal_direction=pattern_result['signal_direction'],
                strength=pattern_result.get('signal_strength', 'MODERATE'),
                current_price=current_market_price,
                ema5_uptrend=df_with_indicators['ema5'].iloc[-1] > df_with_indicators['ema5'].iloc[-2],
                ema8_uptrend=df_with_indicators['ema8'].iloc[-1] > df_with_indicators['ema8'].iloc[-2],
                ema21_uptrend=df_with_indicators.get('ema21', pd.Series([0, 0])).iloc[-1] > df_with_indicators.get('ema21', pd.Series([0, 0])).iloc[-2] if len(df_with_indicators) > 1 else False,
                squeeze_active=df_with_indicators.get('squeeze_active', pd.Series([False])).iloc[-1] if 'squeeze_active' in df_with_indicators.columns else False,
                # Multi-timeframe analysis (required fields)
                weekly_trend=pattern_result['trend_analysis']['weekly_trend'],
                daily_trend=pattern_result['trend_analysis']['daily_trend'],
                trend_alignment=pattern_result['trend_analysis']['trend_alignment']
            )

            return signal
            
        except Exception as e:
            self.logger.error(f"Error scanning symbol {symbol}: {e}")
            return None

    async def scan_multiple_symbols(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan multiple symbols for Lee Method patterns with enhanced batch processing"""
        signals = []

        try:
            # Enhanced batch processing with intelligent rate limiting
            batch_size = 10  # Optimized for rate limiting
            total_batches = (len(symbols) + batch_size - 1) // batch_size

            self.logger.info(f"Scanning {len(symbols)} symbols in {total_batches} batches of {batch_size}")

            for batch_num, i in enumerate(range(0, len(symbols), batch_size)):
                batch = symbols[i:i + batch_size]
                self.logger.debug(f"Processing batch {batch_num + 1}/{total_batches}: {batch}")

                # Pre-fetch quotes for the entire batch using enhanced market data manager
                try:
                    batch_quotes = await enhanced_market_data.get_multiple_quotes(batch, timeout=20.0)
                    self.logger.debug(f"Pre-fetched quotes for {len([q for q in batch_quotes.values() if q])} symbols")
                except Exception as e:
                    self.logger.warning(f"Batch quote pre-fetch failed: {e}")
                    batch_quotes = {}

                # Create tasks for concurrent processing with timeout
                tasks = []
                for symbol in batch:
                    task = asyncio.create_task(self.scan_symbol(symbol))
                    tasks.append((symbol, task))

                # Wait for all tasks with timeout
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                        timeout=30.0  # 30 second timeout per batch
                    )
                except asyncio.TimeoutError:
                    self.logger.warning(f"Batch {batch_num + 1} timed out")

                # Collect results
                for symbol, task in tasks:
                    try:
                        if task.done():
                            result = task.result()
                            if isinstance(result, LeeMethodSignal):
                                signals.append(result)
                                self.logger.info(f"✅ Signal found for {symbol}: {result.signal_type}")
                            elif isinstance(result, Exception):
                                self.logger.error(f"Error scanning {symbol}: {result}")
                        else:
                            task.cancel()
                            self.logger.warning(f"Cancelled scan for {symbol}")
                    except Exception as e:
                        self.logger.error(f"Error processing result for {symbol}: {e}")

                # Adaptive delay based on success rate
                successful_scans = len([task for _, task in tasks if task.done() and not isinstance(task.result(), Exception)])
                success_rate = successful_scans / len(batch) if batch else 0

                if success_rate < 0.5:  # Less than 50% success
                    delay = 2.0  # Longer delay
                elif success_rate < 0.8:  # Less than 80% success
                    delay = 1.0  # Medium delay
                else:
                    delay = 0.3  # Short delay for good performance

                if batch_num < total_batches - 1:  # Don't delay after last batch
                    self.logger.debug(f"Batch delay: {delay}s (success rate: {success_rate:.1%})")
                    await asyncio.sleep(delay)

            self.logger.info(f"Scan completed: {len(signals)} signals found from {len(symbols)} symbols")
            return signals

        except Exception as e:
            self.logger.error(f"Error scanning multiple symbols: {e}")
            return signals

    async def scan_symbols(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Scan symbols for Lee Method patterns (orchestrator interface)"""
        try:
            # Check market hours first - don't scan when markets are closed
            if not self._is_market_hours():
                self.logger.info("Skipping symbol scan - markets are closed")
                return {
                    'signals': [],
                    'scan_time': datetime.now().isoformat(),
                    'symbols_scanned': 0,
                    'signals_found': 0,
                    'market_status': 'CLOSED'
                }

            if symbols is None:
                # Use high-volume symbols for faster, more stable scanning
                symbols = get_high_volume_symbols()  # Only 30 symbols instead of 350+

            signals = await self.scan_multiple_symbols(symbols)

            return {
                'signals': [signal.to_dict() for signal in signals],
                'scan_time': datetime.now().isoformat(),
                'symbols_scanned': len(symbols),
                'signals_found': len(signals),
                'market_status': 'OPEN'
            }

        except Exception as e:
            self.logger.error(f"Error in scan_symbols: {e}")
            return {'signals': [], 'error': str(e)}

    async def get_active_signals(self) -> List[Dict[str, Any]]:
        """Get active Lee Method signals (orchestrator interface)"""
        try:
            # For the main scanner, we don't maintain persistent signals
            # So we'll do a comprehensive scan of S&P 500 symbols
            symbols = get_sp500_symbols()
            signals = await self.scan_multiple_symbols(symbols)

            return [signal.to_dict() for signal in signals]

        except Exception as e:
            self.logger.error(f"Error getting active signals: {e}")
            return []


# ============================================================================
# REAL-TIME SCANNER
# ============================================================================

class AtlasLeeMethodRealtimeScanner:
    """Real-time Lee Method scanner for A.T.L.A.S. system"""
    
    def __init__(self, fmp_api_key: str = None, market_engine=None):
        self.logger = logger

        # Initialize market engine if not provided
        if not market_engine:
            try:
                from atlas_market_core import AtlasMarketEngine
                market_engine = AtlasMarketEngine()
            except ImportError:
                self.logger.warning("Market engine not available for real-time scanner")
                market_engine = None

        self.lee_scanner = LeeMethodScanner(fmp_api_key, market_engine)
        
        # Scanner configuration - Use high-volume symbols for stable scanning
        self.scan_symbols = get_high_volume_symbols()  # Only 30 symbols for faster, more reliable scanning
        
        # Scanner state
        self.is_running = False
        self.scan_interval = 30  # seconds
        self.last_scan_time = None
        self.scan_count = 0
        self.active_signals = {}
        self.scan_task = None
        
        # Performance tracking
        self.scan_times = []
        self.error_count = 0

    async def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            self.logger.warning("Scanner is already running")
            return
        
        self.is_running = True
        self.logger.info("[SCANNER] Starting Lee Method real-time scanner")
        
        # Start scanning task
        self.scan_task = asyncio.create_task(self._scanning_loop())

    async def stop_scanning(self):
        """Stop the real-time scanning process"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("[SCANNER] Lee Method scanner stopped")

    async def _scanning_loop(self):
        """Main scanning loop"""
        while self.is_running:
            try:
                await self._perform_lee_method_scan()
                await asyncio.sleep(self.scan_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in scanning loop: {e}")
                self.error_count += 1
                await asyncio.sleep(5)  # Brief pause on error

    async def _perform_lee_method_scan(self):
        """Perform Lee Method scan on all symbols"""
        try:
            scan_start = datetime.now()
            self.scan_count += 1
            self.last_scan_time = scan_start
            
            # Clear old signals (older than 1 hour)
            self._cleanup_old_signals()
            
            # Scan symbols in batches to avoid overwhelming APIs
            batch_size = 5
            new_signals = []
            
            for i in range(0, len(self.scan_symbols), batch_size):
                batch = self.scan_symbols[i:i + batch_size]
                batch_results = await self._scan_batch(batch)
                new_signals.extend(batch_results)
                
                # Optimized delay between batches for better performance
                await asyncio.sleep(1.0)  # Reduced from 2.0s for faster scanning
            
            # Update active signals
            for signal in new_signals:
                self.active_signals[signal.symbol] = signal
            
            # Track performance
            scan_duration = (datetime.now() - scan_start).total_seconds()
            self.scan_times.append(scan_duration)
            
            # Keep only recent scan times (last 100)
            if len(self.scan_times) > 100:
                self.scan_times = self.scan_times[-100:]
            
            self.logger.info(f"[SCANNER] Scan #{self.scan_count} completed in {scan_duration:.2f}s - {len(new_signals)} new signals")
            
        except Exception as e:
            self.logger.error(f"Error performing Lee Method scan: {e}")
            self.error_count += 1

    async def _scan_batch(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan a batch of symbols"""
        signals = []
        
        try:
            tasks = [self.lee_scanner.scan_symbol(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, LeeMethodSignal):
                    signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error in batch scan: {result}")
            
        except Exception as e:
            self.logger.error(f"Error scanning batch {symbols}: {e}")
        
        return signals

    def _cleanup_old_signals(self):
        """Remove signals older than 1 hour"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        symbols_to_remove = []
        for symbol, signal in self.active_signals.items():
            if signal.timestamp < cutoff_time:
                symbols_to_remove.append(symbol)
        
        for symbol in symbols_to_remove:
            del self.active_signals[symbol]

    def get_latest_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the latest Lee Method signals"""
        signals = list(self.active_signals.values())
        signals.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [signal.to_dict() for signal in signals[:limit]]

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get scanner status information"""
        avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0

        return {
            'is_running': self.is_running,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'active_signals_count': len(self.active_signals),
            'symbols_monitored': len(self.scan_symbols),
            'error_count': self.error_count,
            'average_scan_time': round(avg_scan_time, 2)
        }

    def get_status(self) -> Dict[str, Any]:
        """Get scanner status information (alias for compatibility)"""
        status = self.get_scanner_status()
        # Add additional fields expected by tests
        status.update({
            'running': status['is_running'],
            'active_results_count': status['active_signals_count'],
            'market_hours': self._is_market_hours()  # Use actual market hours detection
        })
        return status


# ============================================================================
# SCANNER INSTANCE MANAGEMENT
# ============================================================================

_scanner_instance = None

def get_scanner_instance(fmp_api_key: str = None) -> AtlasLeeMethodRealtimeScanner:
    """Get or create scanner instance"""
    global _scanner_instance
    
    if _scanner_instance is None:
        _scanner_instance = AtlasLeeMethodRealtimeScanner(fmp_api_key)
    
    return _scanner_instance


# ============================================================================
# LEE METHOD CRITERIA INFORMATION
# ============================================================================

def get_lee_method_criteria() -> Dict[str, Any]:
    """Get Lee Method criteria information (5-point TTM Squeeze algorithm)"""
    return {
        'name': 'Lee Method',
        'description': 'Modified 3+ bar TTM Squeeze pattern detection for momentum reversal signals',
        'algorithm': 'TTM Squeeze Rebound Pattern (3+ Bar Decline)',
        'criteria': [
            {
                'number': 1,
                'name': 'TTM Squeeze Histogram Decline Pattern',
                'description': 'MINIMUM 3 consecutive declining histogram bars (reduced from 5)',
                'detail': 'Detects hist_1 < hist_2 < hist_3 for minimum 3 consecutive bars, indicating momentum decline'
            },
            {
                'number': 2,
                'name': 'Histogram Rebound Signal',
                'description': 'Less negative bounce where histogram improves but remains negative',
                'detail': 'Current histogram > previous histogram AND both values < 0 (anticipates first yellow bar after 3 red bars)'
            },
            {
                'number': 3,
                'name': 'EMA 5 Uptrend Confirmation',
                'description': '5-period exponential moving average trending upward',
                'detail': 'EMA5 > EMA5[1] confirms short-term upward price momentum'
            },
            {
                'number': 4,
                'name': 'EMA 8 Uptrend Confirmation',
                'description': '8-period exponential moving average trending upward',
                'detail': 'EMA8 > EMA8[1] confirms medium-term upward price momentum'
            },
            {
                'number': 5,
                'name': 'Optional TTM Squeeze State Filter',
                'description': 'Configurable requirement for active TTM Squeeze (BB inside KC)',
                'detail': 'Optional filter requiring Bollinger Bands inside Keltner Channels with configurable lookback'
            }
        ],
        'technical_specs': {
            'macd_settings': 'MACD(12,26,9)',
            'bollinger_bands': 'BB(20,2)',
            'keltner_channels': 'KC(20,1.5)',
            'ema_periods': [5, 8],
            'squeeze_filter': 'Optional (configurable)'
        },
        'advantages': [
            'Anticipates momentum reversal at optimal entry point',
            'Detects first less-negative histogram bar after decline',
            'Dual EMA confirmation reduces false signals',
            'Optional squeeze filter for high-probability setups',
            'More precise timing than traditional TTM Squeeze'
        ],
        'signal_type': 'Bullish momentum reversal anticipation'
    }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "LeeMethodScanner",
    "AtlasLeeMethodRealtimeScanner",
    "LeeMethodSignal",
    "get_scanner_instance",
    "get_lee_method_criteria"
]
