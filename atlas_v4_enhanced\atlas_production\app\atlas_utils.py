"""
A.T.L.A.S Utilities - Consolidated Helper Functions and Tools
Combines logging, error handling, performance optimization, and other utilities
"""

import logging
import sys
import os
import asyncio
import time
import functools
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union
import json
import traceback

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

class AtlasLoggingConfig:
    """Windows-compatible logging configuration for A.T.L.A.S."""
    
    @staticmethod
    def setup_atlas_logging(level: str = 'INFO'):
        """Setup Windows-compatible logging for A.T.L.A.S."""
        try:
            # Configure root logger
            logging.basicConfig(
                level=getattr(logging, level.upper()),
                format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S',
                handlers=[
                    logging.StreamHandler(sys.stdout),
                    logging.FileHandler('atlas_system.log', encoding='utf-8')
                ]
            )
            
            # Set specific logger levels
            logging.getLogger('atlas_ai_engine').setLevel(logging.INFO)
            logging.getLogger('atlas_market_engine').setLevel(logging.INFO)
            logging.getLogger('atlas_trading_engine').setLevel(logging.INFO)
            logging.getLogger('atlas_risk_engine').setLevel(logging.INFO)
            logging.getLogger('atlas_education_engine').setLevel(logging.INFO)
            
            logger = logging.getLogger('atlas_logging')
            logger.info("A.T.L.A.S. logging system initialized - Windows compatible format")
            logger.info(f"Log level: {level}")
            logger.info("Unicode characters will be automatically converted to ASCII")
            
            return logger
            
        except Exception as e:
            print(f"Error setting up logging: {e}")
            return logging.getLogger(__name__)

    @staticmethod
    def get_atlas_logger(name: str) -> logging.Logger:
        """Get a logger with Atlas configuration"""
        return logging.getLogger(name)


def initialize_component_logging(component_name: str) -> logging.Logger:
    """Initialize logging for a specific component"""
    try:
        logger = logging.getLogger(component_name)
        logger.info(f"[INIT] {component_name} component logging initialized")
        return logger
    except Exception as e:
        print(f"Error initializing component logging for {component_name}: {e}")
        return logging.getLogger(component_name)


# ============================================================================
# ERROR HANDLING
# ============================================================================

class AtlasErrorHandler:
    """Enhanced error handler with recovery strategies"""
    
    def __init__(self):
        self.logger = logging.getLogger('atlas_error_handler')
        self.recovery_strategies = {}
        self.error_counts = {}
        self.max_retries = 3
        
        self.logger.info("[SHIELD] Enhanced Error Handler initialized")

    def register_recovery_strategy(self, component: str, strategy: Callable):
        """Register recovery strategy for component"""
        self.recovery_strategies[component] = strategy
        self.logger.info(f"[TOOL] Recovery strategy registered for {component}")

    async def handle_error(self, component: str, error: Exception, context: Dict[str, Any] = None) -> bool:
        """Handle error with recovery strategy"""
        try:
            error_key = f"{component}_{type(error).__name__}"
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
            
            self.logger.error(f"[ERROR] {component}: {error}")
            
            # Try recovery if strategy exists and under retry limit
            if (component in self.recovery_strategies and 
                self.error_counts[error_key] <= self.max_retries):
                
                self.logger.info(f"[RECOVERY] Attempting recovery for {component}")
                recovery_result = await self.recovery_strategies[component](error, context)
                
                if recovery_result:
                    self.logger.info(f"[OK] Recovery successful for {component}")
                    return True
                else:
                    self.logger.warning(f"[FAIL] Recovery failed for {component}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in error handler: {e}")
            return False

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        return {
            "error_counts": self.error_counts.copy(),
            "registered_strategies": list(self.recovery_strategies.keys()),
            "max_retries": self.max_retries
        }


# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================

class AtlasPerformanceOptimizer:
    """Performance monitoring and optimization"""
    
    def __init__(self):
        self.logger = logging.getLogger('atlas_performance_optimizer')
        self.metrics = {}
        self.thresholds = {
            'response_time': 5.0,  # seconds
            'memory_usage': 1000,  # MB
            'cpu_usage': 80.0      # percentage
        }
        
        self.logger.info("[LAUNCH] Performance Optimizer initialized")

    def performance_monitor(self, operation_name: str):
        """Decorator for monitoring performance"""
        def decorator(func):
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = await func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # Record metrics
                    self._record_metric(operation_name, execution_time, True)
                    
                    # Check thresholds
                    if execution_time > self.thresholds['response_time']:
                        self.logger.warning(f"[SLOW] {operation_name} took {execution_time:.2f}s")
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._record_metric(operation_name, execution_time, False)
                    raise
                    
            return wrapper
        return decorator

    def _record_metric(self, operation: str, execution_time: float, success: bool):
        """Record performance metric"""
        if operation not in self.metrics:
            self.metrics[operation] = {
                'total_calls': 0,
                'successful_calls': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'max_time': 0.0,
                'min_time': float('inf')
            }
        
        metric = self.metrics[operation]
        metric['total_calls'] += 1
        metric['total_time'] += execution_time
        metric['avg_time'] = metric['total_time'] / metric['total_calls']
        metric['max_time'] = max(metric['max_time'], execution_time)
        metric['min_time'] = min(metric['min_time'], execution_time)
        
        if success:
            metric['successful_calls'] += 1

    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance report"""
        return {
            "metrics": self.metrics.copy(),
            "thresholds": self.thresholds.copy(),
            "timestamp": datetime.now().isoformat()
        }


# ============================================================================
# STARTUP INITIALIZATION
# ============================================================================

class AtlasStartupInit:
    """Startup initialization utilities"""
    
    @staticmethod
    def initialize_atlas_system():
        """Initialize A.T.L.A.S. system components"""
        try:
            # Setup logging first
            logger = AtlasLoggingConfig.setup_atlas_logging('INFO')
            logger.info("A.T.L.A.S. startup initialization completed - Windows-compatible logging active")
            logger.info("Unicode encoding issues should now be resolved")
            
            return True
            
        except Exception as e:
            print(f"Error in startup initialization: {e}")
            return False


# ============================================================================
# SECURITY MANAGER
# ============================================================================

class AtlasSecurityManager:
    """Basic security management"""
    
    def __init__(self):
        self.logger = logging.getLogger('atlas_security')
        self.api_keys = {}
        self.rate_limits = {}
        
        self.logger.info("[SHIELD] Security Manager initialized")

    def validate_api_key(self, service: str, api_key: str) -> bool:
        """Validate API key for service"""
        try:
            # Basic validation - check if key exists and has minimum length
            if not api_key or len(api_key) < 10:
                return False
            
            # Store validated key
            self.api_keys[service] = api_key
            return True
            
        except Exception as e:
            self.logger.error(f"API key validation error: {e}")
            return False

    def check_rate_limit(self, service: str, limit: int = 100) -> bool:
        """Check rate limit for service"""
        try:
            current_time = datetime.now()
            
            if service not in self.rate_limits:
                self.rate_limits[service] = {
                    'count': 0,
                    'reset_time': current_time + timedelta(hours=1)
                }
            
            rate_limit = self.rate_limits[service]
            
            # Reset if time window expired
            if current_time > rate_limit['reset_time']:
                rate_limit['count'] = 0
                rate_limit['reset_time'] = current_time + timedelta(hours=1)
            
            # Check limit
            if rate_limit['count'] >= limit:
                return False
            
            rate_limit['count'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Rate limit check error: {e}")
            return True  # Allow on error


# ============================================================================
# PROACTIVE ASSISTANT
# ============================================================================

class AtlasProactiveAssistant:
    """Proactive trading assistant"""
    
    def __init__(self):
        self.logger = logging.getLogger('atlas_proactive_assistant')
        self.alerts = []
        self.monitoring_enabled = True
        
        self.logger.info("[BOT] Proactive Trading Assistant initialized - enabled: True")

    async def generate_proactive_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate proactive suggestions based on context"""
        try:
            suggestions = []
            
            # Market closing suggestion
            current_hour = datetime.now().hour
            if 15 <= current_hour <= 16:  # Market closing time
                suggestions.append("Market is closing soon - should I summarize today's activity?")
            
            # Portfolio suggestions
            if context.get('portfolio_value'):
                suggestions.append("Would you like me to analyze your portfolio performance?")
            
            # News-based suggestions
            if context.get('recent_news'):
                suggestions.append("I noticed some important market news - would you like an analysis?")
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Error generating proactive suggestions: {e}")
            return []

    def add_alert(self, alert_type: str, message: str, priority: str = "medium"):
        """Add proactive alert"""
        try:
            alert = {
                'type': alert_type,
                'message': message,
                'priority': priority,
                'timestamp': datetime.now(),
                'id': len(self.alerts)
            }
            
            self.alerts.append(alert)
            self.logger.info(f"[ALERT] {priority.upper()}: {message}")
            
            # Keep only recent alerts
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]
                
        except Exception as e:
            self.logger.error(f"Error adding alert: {e}")

    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        return self.alerts[-limit:] if self.alerts else []


# ============================================================================
# GLOBAL INSTANCES
# ============================================================================

# Create global instances for easy access
error_handler = AtlasErrorHandler()
performance_optimizer = AtlasPerformanceOptimizer()
security_manager = AtlasSecurityManager()
proactive_assistant = AtlasProactiveAssistant()


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely load JSON with fallback"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def format_currency(amount: float) -> str:
    """Format amount as currency"""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format value as percentage"""
    return f"{value:.2f}%"

def truncate_string(text: str, max_length: int = 100) -> str:
    """Truncate string to max length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

async def retry_async(func: Callable, max_retries: int = 3, delay: float = 1.0) -> Any:
    """Retry async function with exponential backoff"""
    for attempt in range(max_retries):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(delay * (2 ** attempt))


# ============================================================================
# UTILS ORCHESTRATOR
# ============================================================================

class AtlasUtilsOrchestrator:
    """Main utilities orchestrator"""

    def __init__(self):
        self.logging_config = AtlasLoggingConfig()
        self.error_handler = AtlasErrorHandler()
        self.performance_optimizer = AtlasPerformanceOptimizer()
        self.startup_init = AtlasStartupInit()
        self.security_manager = AtlasSecurityManager()
        self.proactive_assistant = AtlasProactiveAssistant()
        self.status = EngineStatus.INITIALIZING

        logger.info("[ORCHESTRATOR] Utils Orchestrator initialized")

    async def initialize(self):
        """Initialize all utility components"""
        try:
            # Initialize components that have initialize methods
            if hasattr(self.error_handler, 'initialize'):
                await self.error_handler.initialize()
            if hasattr(self.performance_optimizer, 'initialize'):
                await self.performance_optimizer.initialize()
            if hasattr(self.startup_init, 'initialize'):
                await self.startup_init.initialize()
            if hasattr(self.security_manager, 'initialize'):
                await self.security_manager.initialize()
            if hasattr(self.proactive_assistant, 'initialize'):
                await self.proactive_assistant.initialize()

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Utils Orchestrator fully initialized")

        except Exception as e:
            logger.error(f"Utils Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasLoggingConfig",
    "AtlasErrorHandler",
    "AtlasPerformanceOptimizer",
    "AtlasStartupInit",
    "AtlasSecurityManager",
    "AtlasProactiveAssistant",
    "AtlasUtilsOrchestrator",
    "initialize_component_logging",
    "error_handler",
    "performance_optimizer",
    "security_manager",
    "proactive_assistant",
    "safe_json_loads",
    "format_currency",
    "format_percentage",
    "truncate_string",
    "retry_async"
]
