#!/usr/bin/env python3
"""
A.T.L.A.S. Cleanup Manager
Comprehensive cleanup system for all resources including sessions, connections, and processes
"""

import asyncio
import logging
import signal
import sys
import weakref
import gc
from typing import List, Callable, Any, Optional
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class AtlasCleanupManager:
    """Comprehensive cleanup manager for A.T.L.A.S. system resources"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._cleanup_functions: List[Callable] = []
        self._async_cleanup_functions: List[Callable] = []
        self._cleanup_completed = False
        self._signal_handlers_registered = False
        self._initialized = True
        
        logger.info("Atlas Cleanup Manager initialized")
    
    def register_cleanup(self, cleanup_func: Callable, is_async: bool = False):
        """Register a cleanup function"""
        if is_async:
            self._async_cleanup_functions.append(cleanup_func)
        else:
            self._cleanup_functions.append(cleanup_func)
        
        logger.debug(f"Registered {'async' if is_async else 'sync'} cleanup function: {cleanup_func.__name__}")
    
    def register_signal_handlers(self):
        """Register signal handlers for graceful shutdown"""
        if self._signal_handlers_registered:
            return
        
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating cleanup...")
            asyncio.create_task(self.cleanup_all())
            sys.exit(0)
        
        try:
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            self._signal_handlers_registered = True
            logger.debug("Signal handlers registered")
        except Exception as e:
            logger.warning(f"Failed to register signal handlers: {e}")
    
    async def cleanup_all(self):
        """Perform comprehensive cleanup of all registered resources"""
        if self._cleanup_completed:
            return
        
        logger.info("Starting comprehensive cleanup...")
        
        try:
            # Run async cleanup functions
            for cleanup_func in self._async_cleanup_functions:
                try:
                    logger.debug(f"Running async cleanup: {cleanup_func.__name__}")
                    await cleanup_func()
                except Exception as e:
                    logger.error(f"Error in async cleanup {cleanup_func.__name__}: {e}")
            
            # Run sync cleanup functions
            for cleanup_func in self._cleanup_functions:
                try:
                    logger.debug(f"Running sync cleanup: {cleanup_func.__name__}")
                    cleanup_func()
                except Exception as e:
                    logger.error(f"Error in sync cleanup {cleanup_func.__name__}: {e}")
            
            # Force garbage collection
            gc.collect()
            
            # Wait a bit for any remaining async operations
            await asyncio.sleep(0.1)
            
            self._cleanup_completed = True
            logger.info("Comprehensive cleanup completed successfully")
            
        except Exception as e:
            logger.error(f"Error during comprehensive cleanup: {e}")
    
    async def cleanup_aiohttp_sessions(self):
        """Specific cleanup for aiohttp sessions"""
        try:
            # Import session manager if available
            from atlas_session_manager import session_manager
            await session_manager.close_all_sessions()
            logger.debug("aiohttp sessions cleaned up")
        except ImportError:
            logger.debug("Session manager not available")
        except Exception as e:
            logger.error(f"Error cleaning up aiohttp sessions: {e}")
    
    async def cleanup_event_loops(self):
        """Clean up event loops and pending tasks"""
        try:
            # Cancel all pending tasks
            tasks = [task for task in asyncio.all_tasks() if not task.done()]
            if tasks:
                logger.debug(f"Cancelling {len(tasks)} pending tasks")
                for task in tasks:
                    task.cancel()
                
                # Wait for tasks to be cancelled
                await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.debug("Event loop cleanup completed")
        except Exception as e:
            logger.error(f"Error cleaning up event loops: {e}")
    
    def cleanup_weak_references(self):
        """Clean up weak references"""
        try:
            # Force cleanup of weak references
            import weakref
            weakref.finalize_all()
            logger.debug("Weak references cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up weak references: {e}")
    
    @asynccontextmanager
    async def managed_cleanup(self):
        """Context manager for automatic cleanup"""
        try:
            yield self
        finally:
            await self.cleanup_all()
    
    def __del__(self):
        """Cleanup on deletion"""
        if not self._cleanup_completed:
            logger.warning("Cleanup manager deleted without proper cleanup")

# Global cleanup manager instance
cleanup_manager = AtlasCleanupManager()

# Register default cleanup functions
cleanup_manager.register_cleanup(cleanup_manager.cleanup_aiohttp_sessions, is_async=True)
cleanup_manager.register_cleanup(cleanup_manager.cleanup_event_loops, is_async=True)
cleanup_manager.register_cleanup(cleanup_manager.cleanup_weak_references, is_async=False)

# Convenience functions
def register_cleanup(cleanup_func: Callable, is_async: bool = False):
    """Register a cleanup function with the global cleanup manager"""
    cleanup_manager.register_cleanup(cleanup_func, is_async)

async def cleanup_all():
    """Perform comprehensive cleanup"""
    await cleanup_manager.cleanup_all()

def register_signal_handlers():
    """Register signal handlers for graceful shutdown"""
    cleanup_manager.register_signal_handlers()

@asynccontextmanager
async def managed_cleanup():
    """Context manager for automatic cleanup"""
    async with cleanup_manager.managed_cleanup():
        yield

# Auto-register signal handlers
try:
    register_signal_handlers()
except Exception as e:
    logger.warning(f"Failed to auto-register signal handlers: {e}")

logger.info("Atlas Cleanup Manager module loaded")
