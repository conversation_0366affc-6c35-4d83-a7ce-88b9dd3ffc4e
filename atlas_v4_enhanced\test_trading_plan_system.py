"""
Comprehensive Trading Plan System Test Suite
Tests the complete trading plan generation and integration system
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

async def test_trading_plan_system():
    """Comprehensive test of the trading plan system"""
    try:
        print('🧪 TESTING COMPREHENSIVE TRADING PLAN SYSTEM')
        print('=' * 60)
        
        # Test 1: Import and Initialize Components
        print('\n📦 1. TESTING IMPORTS AND INITIALIZATION')
        print('-' * 50)
        
        try:
            from atlas_trading_plan_engine import AtlasTradingPlanEngine
            from models import ComprehensiveTradingPlan, TradingPlanTarget, TradingOpportunity
            from atlas_orchestrator import AtlasOrchestrator
            from atlas_alert_manager import AtlasAlertManager
            print('✅ All imports successful')
        except ImportError as e:
            print(f'❌ Import failed: {e}')
            return False
        
        # Test 2: Initialize Trading Plan Engine
        print('\n🚀 2. TESTING TRADING PLAN ENGINE INITIALIZATION')
        print('-' * 50)
        
        try:
            trading_plan_engine = AtlasTradingPlanEngine()
            await trading_plan_engine.initialize()
            print(f'✅ Trading plan engine initialized: {trading_plan_engine.status}')
        except Exception as e:
            print(f'❌ Trading plan engine initialization failed: {e}')
            return False
        
        # Test 3: Generate Trading Plan
        print('\n📊 3. TESTING TRADING PLAN GENERATION')
        print('-' * 50)
        
        try:
            # Test parameters
            target_profit = 5000.0
            timeframe_days = 30
            starting_capital = 50000.0
            risk_tolerance = "moderate"
            
            print(f'Generating plan: ${target_profit:,.0f} profit in {timeframe_days} days')
            print(f'Starting capital: ${starting_capital:,.0f}')
            print(f'Risk tolerance: {risk_tolerance}')
            
            trading_plan = await trading_plan_engine.generate_comprehensive_trading_plan(
                target_profit=target_profit,
                timeframe_days=timeframe_days,
                starting_capital=starting_capital,
                risk_tolerance=risk_tolerance
            )
            
            print(f'✅ Trading plan generated: {trading_plan.plan_id}')
            print(f'   Plan name: {trading_plan.plan_name}')
            print(f'   Opportunities: {len(trading_plan.opportunities)}')
            print(f'   Expected return: ${trading_plan.total_expected_return:,.2f}')
            print(f'   Total risk: ${trading_plan.total_risk_amount:,.2f}')
            print(f'   Confidence: {trading_plan.plan_confidence:.1f}%')
            
        except Exception as e:
            print(f'❌ Trading plan generation failed: {e}')
            return False
        
        # Test 4: Validate Plan Structure
        print('\n🔍 4. TESTING PLAN STRUCTURE VALIDATION')
        print('-' * 50)
        
        try:
            # Validate plan has required components
            assert trading_plan.plan_id is not None, "Plan ID missing"
            assert trading_plan.target is not None, "Target missing"
            assert len(trading_plan.opportunities) > 0, "No opportunities generated"
            assert trading_plan.portfolio_integration is not None, "Portfolio integration missing"
            assert len(trading_plan.scenarios) > 0, "No scenarios generated"
            assert trading_plan.monitoring is not None, "Monitoring framework missing"
            
            print('✅ Plan structure validation passed')
            
            # Validate individual opportunities
            for i, opp in enumerate(trading_plan.opportunities):
                assert opp.symbol is not None, f"Opportunity {i} missing symbol"
                assert opp.current_price > 0, f"Opportunity {i} invalid current price"
                assert opp.entry_price > 0, f"Opportunity {i} invalid entry price"
                assert opp.exit_target_price > 0, f"Opportunity {i} invalid target price"
                assert opp.stop_loss_price > 0, f"Opportunity {i} invalid stop loss"
                assert opp.position_size_shares > 0, f"Opportunity {i} invalid position size"
                assert 0 <= opp.confidence_score <= 100, f"Opportunity {i} invalid confidence"
                
            print(f'✅ All {len(trading_plan.opportunities)} opportunities validated')
            
        except AssertionError as e:
            print(f'❌ Plan structure validation failed: {e}')
            return False
        except Exception as e:
            print(f'❌ Plan validation error: {e}')
            return False
        
        # Test 5: Test Orchestrator Integration
        print('\n🎭 5. TESTING ORCHESTRATOR INTEGRATION')
        print('-' * 50)
        
        try:
            orchestrator = AtlasOrchestrator()
            await orchestrator.initialize()
            
            # Test trading plan intent detection
            test_messages = [
                "I want to make $5000 in 30 days",
                "Generate a trading plan to earn $10000 within 2 months",
                "Create an actionable trading strategy for $2500 profit",
                "Help me make money with a specific dollar target"
            ]
            
            for message in test_messages:
                intent_result = await orchestrator.detect_trading_plan_intent(message)
                print(f'   Message: "{message}"')
                print(f'   Is trading plan request: {intent_result["is_trading_plan_request"]}')
                print(f'   Confidence: {intent_result["confidence"]:.1f}')
                print(f'   Detected amounts: {intent_result["detected_amounts"]}')
                print()
            
            print('✅ Orchestrator integration test passed')
            
        except Exception as e:
            print(f'❌ Orchestrator integration test failed: {e}')
            return False
        
        # Test 6: Test Alert System Integration
        print('\n🚨 6. TESTING ALERT SYSTEM INTEGRATION')
        print('-' * 50)
        
        try:
            alert_manager = AtlasAlertManager()
            await alert_manager.initialize()
            
            # Test trading plan alerts
            await alert_manager.send_trading_plan_entry_alert(
                plan_id=trading_plan.plan_id,
                symbol="AAPL",
                entry_price=150.00,
                target_price=165.00,
                stop_loss=145.00
            )
            print('✅ Entry alert sent successfully')
            
            await alert_manager.send_trading_plan_risk_alert(
                plan_id=trading_plan.plan_id,
                risk_message="Portfolio risk exceeds 15% threshold"
            )
            print('✅ Risk alert sent successfully')
            
            await alert_manager.send_trading_plan_update_alert(
                plan_id=trading_plan.plan_id,
                update_message="Plan performance updated - 2 positions profitable"
            )
            print('✅ Update alert sent successfully')
            
        except Exception as e:
            print(f'❌ Alert system integration test failed: {e}')
            return False
        
        # Test 7: Test API Endpoints (Simulation)
        print('\n🌐 7. TESTING API ENDPOINT STRUCTURE')
        print('-' * 50)
        
        try:
            # Test plan serialization for API
            plan_dict = trading_plan.dict()
            assert isinstance(plan_dict, dict), "Plan serialization failed"
            assert 'plan_id' in plan_dict, "Plan ID missing from serialization"
            assert 'opportunities' in plan_dict, "Opportunities missing from serialization"
            
            # Test JSON serialization
            plan_json = json.dumps(plan_dict, default=str)
            assert len(plan_json) > 0, "JSON serialization failed"
            
            print('✅ API endpoint structure test passed')
            print(f'   Serialized plan size: {len(plan_json)} characters')
            
        except Exception as e:
            print(f'❌ API endpoint test failed: {e}')
            return False
        
        # Test 8: Performance and Resource Usage
        print('\n⚡ 8. TESTING PERFORMANCE AND RESOURCE USAGE')
        print('-' * 50)
        
        try:
            import time
            
            # Test multiple plan generation performance
            start_time = time.time()
            
            for i in range(3):
                test_plan = await trading_plan_engine.generate_comprehensive_trading_plan(
                    target_profit=1000.0 * (i + 1),
                    timeframe_days=7 * (i + 1),
                    starting_capital=10000.0,
                    risk_tolerance="conservative"
                )
                print(f'   Plan {i+1}: {test_plan.plan_id} - {len(test_plan.opportunities)} opportunities')
            
            end_time = time.time()
            total_time = end_time - start_time
            
            print(f'✅ Performance test passed')
            print(f'   Generated 3 plans in {total_time:.2f} seconds')
            print(f'   Average time per plan: {total_time/3:.2f} seconds')
            
        except Exception as e:
            print(f'❌ Performance test failed: {e}')
            return False
        
        # Test Summary
        print('\n📋 TEST SUMMARY')
        print('=' * 60)
        print('✅ All trading plan system tests passed successfully!')
        print()
        print('🎯 SYSTEM CAPABILITIES VERIFIED:')
        print('   • Trading plan engine initialization')
        print('   • Comprehensive plan generation')
        print('   • Plan structure validation')
        print('   • Orchestrator integration')
        print('   • Alert system integration')
        print('   • API endpoint compatibility')
        print('   • Performance benchmarks')
        print()
        print('🚀 TRADING PLAN SYSTEM IS READY FOR PRODUCTION!')
        
        return True
        
    except Exception as e:
        print(f'❌ CRITICAL TEST FAILURE: {e}')
        logger.error(f"Trading plan system test failed: {e}")
        return False

async def main():
    """Main test execution"""
    try:
        success = await test_trading_plan_system()
        
        if success:
            print('\n🎉 ALL TESTS PASSED - SYSTEM READY!')
            return 0
        else:
            print('\n💥 TESTS FAILED - SYSTEM NOT READY')
            return 1
            
    except Exception as e:
        print(f'\n💥 TEST EXECUTION FAILED: {e}')
        return 1

if __name__ == "__main__":
    # Run the test suite
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
