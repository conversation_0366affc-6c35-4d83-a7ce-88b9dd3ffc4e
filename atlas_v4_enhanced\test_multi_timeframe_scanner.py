#!/usr/bin/env python3
"""
A.T.L.A.S. Multi-Timeframe Lee Method Scanner Test
Test the enhanced scanner with daily and weekly Lee Method patterns + TTM Squeeze
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import A.T.L.A.S. components
try:
    from atlas_lee_method import LeeMethodScanner
    from sp500_symbols import get_high_volume_symbols
except ImportError as e:
    logger.error(f"Failed to import A.T.L.A.S. components: {e}")
    sys.exit(1)


class MultiTimeframeScannerTest:
    """Test suite for multi-timeframe Lee Method scanning"""
    
    def __init__(self):
        self.lee_scanner = LeeMethodScanner()
        self.test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']  # High-volume test symbols
        
    async def initialize(self):
        """Initialize the scanner"""
        try:
            success = await self.lee_scanner.initialize()
            if success:
                logger.info("✅ Lee Method Scanner initialized successfully")
                return True
            else:
                logger.error("❌ Failed to initialize Lee Method Scanner")
                return False
        except Exception as e:
            logger.error(f"❌ Scanner initialization error: {e}")
            return False
    
    async def test_single_symbol_multi_timeframe(self, symbol: str):
        """Test multi-timeframe scanning for a single symbol"""
        logger.info(f"\n🔍 Testing Multi-Timeframe Scan: {symbol}")
        logger.info("=" * 60)
        
        try:
            # Test the new multi-timeframe scanning method
            results = await self.lee_scanner.scan_symbol_multi_timeframe(symbol, include_ttm_squeeze=True)
            
            if 'error' in results:
                logger.error(f"❌ Error scanning {symbol}: {results['error']}")
                return False
            
            # Display results
            logger.info(f"📊 Results for {symbol}:")
            logger.info(f"   Total Signals Found: {results['summary']['total_signals']}")
            logger.info(f"   Best Confidence: {results['summary']['best_confidence']:.3f}")
            logger.info(f"   Has Daily Signal: {results['summary']['has_daily_signal']}")
            logger.info(f"   Has Weekly Signal: {results['summary']['has_weekly_signal']}")
            
            # Show individual timeframe results
            for timeframe, signal_data in results['signals'].items():
                logger.info(f"\n   📈 {timeframe.upper()} TIMEFRAME:")
                logger.info(f"      Signal Type: {signal_data['signal_type']}")
                logger.info(f"      Confidence: {signal_data['confidence']:.3f}")
                logger.info(f"      Entry Price: ${signal_data['entry_price']:.2f}")
                logger.info(f"      Target Price: ${signal_data['target_price']:.2f}")
                logger.info(f"      Stop Loss: ${signal_data['stop_loss']:.2f}")
                logger.info(f"      Strength: {signal_data['strength']}")
                logger.info(f"      TTM Squeeze: {'Active' if signal_data['ttm_squeeze_active'] else 'Inactive'}")
            
            # Show best signal
            if results['best_signal']:
                best = results['best_signal']
                logger.info(f"\n   🎯 BEST SIGNAL ({best['timeframe'].upper()}):")
                logger.info(f"      Type: {best['signal_type']}")
                logger.info(f"      Confidence: {best['confidence']:.3f}")
                logger.info(f"      Entry: ${best['entry_price']:.2f}")
                logger.info(f"      Target: ${best['target_price']:.2f}")
                logger.info(f"      Risk/Reward: {((best['target_price'] - best['entry_price']) / (best['entry_price'] - best['stop_loss'])):.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error testing {symbol}: {e}")
            return False
    
    async def test_traditional_vs_multi_timeframe(self, symbol: str):
        """Compare traditional single timeframe vs multi-timeframe scanning"""
        logger.info(f"\n🔄 Comparing Scanning Methods: {symbol}")
        logger.info("=" * 60)
        
        try:
            # Traditional single timeframe scan (daily only)
            traditional_signal = await self.lee_scanner.scan_symbol(symbol, timeframes=['1day'])
            
            # Multi-timeframe scan
            multi_tf_results = await self.lee_scanner.scan_symbol_multi_timeframe(symbol)
            
            logger.info("📊 COMPARISON RESULTS:")
            
            # Traditional results
            if traditional_signal:
                logger.info(f"   📈 TRADITIONAL (Daily Only):")
                logger.info(f"      Signal Found: YES")
                logger.info(f"      Confidence: {traditional_signal.confidence:.3f}")
                logger.info(f"      Timeframe: {traditional_signal.timeframe}")
            else:
                logger.info(f"   📈 TRADITIONAL (Daily Only): No signal found")
            
            # Multi-timeframe results
            logger.info(f"   📊 MULTI-TIMEFRAME:")
            logger.info(f"      Total Signals: {multi_tf_results['summary']['total_signals']}")
            logger.info(f"      Best Confidence: {multi_tf_results['summary']['best_confidence']:.3f}")
            logger.info(f"      Daily Signal: {'YES' if multi_tf_results['summary']['has_daily_signal'] else 'NO'}")
            logger.info(f"      Weekly Signal: {'YES' if multi_tf_results['summary']['has_weekly_signal'] else 'NO'}")
            
            # Analysis
            if multi_tf_results['summary']['total_signals'] > 0:
                if traditional_signal and multi_tf_results['summary']['has_daily_signal']:
                    logger.info("   ✅ Both methods found daily signals - consistency confirmed")
                elif not traditional_signal and multi_tf_results['summary']['has_weekly_signal']:
                    logger.info("   🎯 Multi-timeframe found weekly signal missed by daily-only scan")
                elif multi_tf_results['summary']['total_signals'] > 1:
                    logger.info("   📈 Multi-timeframe provides broader market perspective")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in comparison test: {e}")
            return False
    
    async def test_ttm_squeeze_integration(self, symbol: str):
        """Test TTM Squeeze integration with Lee Method patterns"""
        logger.info(f"\n🎯 Testing TTM Squeeze Integration: {symbol}")
        logger.info("=" * 60)
        
        try:
            # Get historical data for manual TTM Squeeze analysis
            df_daily = await self.lee_scanner.fetch_historical_data(symbol, timeframe='1day', limit=50)
            df_weekly = await self.lee_scanner.fetch_historical_data(symbol, timeframe='1week', limit=20)
            
            if not df_daily.empty:
                # Calculate TTM Squeeze indicators
                df_with_squeeze = self.lee_scanner.calculate_ttm_squeeze(df_daily)
                squeeze_status = self.lee_scanner._check_ttm_squeeze_status(df_with_squeeze)
                
                logger.info("📊 TTM SQUEEZE ANALYSIS (Daily):")
                logger.info(f"   Squeeze Active: {squeeze_status['active']}")
                logger.info(f"   Momentum Direction: {squeeze_status['momentum_direction']}")
                logger.info(f"   Squeeze Release: {squeeze_status['squeeze_release']}")
                logger.info(f"   Quality: {squeeze_status['quality']}")
                logger.info(f"   Current Histogram: {squeeze_status['histogram_current']:.4f}")
            
            if not df_weekly.empty:
                # Calculate weekly TTM Squeeze
                df_weekly_squeeze = self.lee_scanner.calculate_ttm_squeeze(df_weekly)
                weekly_squeeze_status = self.lee_scanner._check_ttm_squeeze_status(df_weekly_squeeze)
                
                logger.info("\n📊 TTM SQUEEZE ANALYSIS (Weekly):")
                logger.info(f"   Squeeze Active: {weekly_squeeze_status['active']}")
                logger.info(f"   Momentum Direction: {weekly_squeeze_status['momentum_direction']}")
                logger.info(f"   Squeeze Release: {weekly_squeeze_status['squeeze_release']}")
                logger.info(f"   Quality: {weekly_squeeze_status['quality']}")
            
            # Test with TTM Squeeze filtering
            results_with_squeeze = await self.lee_scanner.scan_symbol_multi_timeframe(symbol, include_ttm_squeeze=True)
            
            logger.info("\n🎯 SIGNALS WITH TTM SQUEEZE FILTER:")
            if results_with_squeeze['summary']['total_signals'] > 0:
                for tf, signal in results_with_squeeze['signals'].items():
                    squeeze_boost = "🚀" if signal['ttm_squeeze_active'] else "📊"
                    logger.info(f"   {squeeze_boost} {tf}: Confidence {signal['confidence']:.3f} ({'TTM Active' if signal['ttm_squeeze_active'] else 'TTM Inactive'})")
            else:
                logger.info("   No signals found with current TTM Squeeze criteria")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in TTM Squeeze test: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        logger.info("🚀 Starting A.T.L.A.S. Multi-Timeframe Lee Method Scanner Test")
        logger.info("=" * 80)
        
        # Initialize
        if not await self.initialize():
            return False
        
        success_count = 0
        total_tests = 0
        
        # Test each symbol
        for symbol in self.test_symbols:
            total_tests += 3  # 3 tests per symbol
            
            # Test 1: Multi-timeframe scanning
            if await self.test_single_symbol_multi_timeframe(symbol):
                success_count += 1
            
            # Test 2: Traditional vs Multi-timeframe comparison
            if await self.test_traditional_vs_multi_timeframe(symbol):
                success_count += 1
            
            # Test 3: TTM Squeeze integration
            if await self.test_ttm_squeeze_integration(symbol):
                success_count += 1
            
            # Brief pause between symbols
            await asyncio.sleep(1)
        
        # Summary
        logger.info("\n" + "=" * 80)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Successful Tests: {success_count}")
        logger.info(f"Success Rate: {(success_count/total_tests)*100:.1f}%")
        
        if success_count == total_tests:
            logger.info("🎉 ALL TESTS PASSED - Multi-timeframe Lee Method Scanner is working correctly!")
        elif success_count > total_tests * 0.8:
            logger.info("✅ Most tests passed - Scanner is functional with minor issues")
        else:
            logger.info("⚠️ Multiple test failures - Scanner needs attention")
        
        return success_count == total_tests


async def main():
    """Main test execution"""
    test_suite = MultiTimeframeScannerTest()
    success = await test_suite.run_comprehensive_test()
    
    if success:
        logger.info("\n🎯 Multi-timeframe Lee Method Scanner is ready for production!")
        logger.info("Features confirmed:")
        logger.info("  ✅ Daily and Weekly timeframe analysis")
        logger.info("  ✅ TTM Squeeze integration")
        logger.info("  ✅ Enhanced confidence scoring")
        logger.info("  ✅ Multi-timeframe signal comparison")
    else:
        logger.info("\n❌ Scanner needs additional configuration or debugging")
    
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
