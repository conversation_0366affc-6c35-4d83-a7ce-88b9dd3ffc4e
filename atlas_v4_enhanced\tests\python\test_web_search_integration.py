"""
Comprehensive Web Search Integration Test
Tests the system-wide web search functionality across all A.T.L.A.S. components
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSearchIntegrationTest:
    """Comprehensive test suite for web search integration"""
    
    def __init__(self):
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "component_results": {},
            "performance_metrics": {},
            "overall_success": False
        }
        
    async def run_all_tests(self):
        """Run comprehensive web search integration tests"""
        logger.info("🚀 Starting A.T.L.A.S. Web Search Integration Tests")
        
        # Test components
        test_components = [
            ("Web Search Service", self.test_web_search_service),
            ("AI Engine Integration", self.test_ai_engine_integration),
            ("Market Analysis Integration", self.test_market_analysis_integration),
            ("Risk Management Integration", self.test_risk_management_integration),
            ("Trading Engine Integration", self.test_trading_engine_integration),
            ("Education System Integration", self.test_education_system_integration),
            ("Lee Method Scanner Integration", self.test_lee_method_integration),
            ("Portfolio Analysis Integration", self.test_portfolio_analysis_integration),
            ("News Insights Integration", self.test_news_insights_integration),
            ("Gap Detection Logic", self.test_gap_detection),
            ("Performance Benchmarks", self.test_performance_benchmarks)
        ]
        
        for component_name, test_func in test_components:
            try:
                logger.info(f"🧪 Testing {component_name}...")
                result = await test_func()
                self.test_results["component_results"][component_name] = result
                
                if result.get("success", False):
                    self.test_results["tests_passed"] += 1
                    logger.info(f"✅ {component_name}: PASSED")
                else:
                    self.test_results["tests_failed"] += 1
                    logger.error(f"❌ {component_name}: FAILED - {result.get('error', 'Unknown error')}")
                
                self.test_results["tests_run"] += 1
                
            except Exception as e:
                logger.error(f"💥 {component_name}: EXCEPTION - {e}")
                self.test_results["component_results"][component_name] = {
                    "success": False,
                    "error": str(e),
                    "exception": True
                }
                self.test_results["tests_failed"] += 1
                self.test_results["tests_run"] += 1
        
        # Calculate overall success
        success_rate = self.test_results["tests_passed"] / self.test_results["tests_run"] if self.test_results["tests_run"] > 0 else 0
        self.test_results["overall_success"] = success_rate >= 0.8  # 80% success rate required
        self.test_results["success_rate"] = success_rate
        
        # Generate final report
        await self.generate_test_report()
        
        return self.test_results

    async def test_web_search_service(self) -> Dict[str, Any]:
        """Test the core web search service"""
        try:
            from atlas_web_search_service import web_search_service, SearchContext, SearchQuery
            
            # Test service availability
            if not web_search_service.is_available():
                return {"success": False, "error": "Web search service not available"}
            
            # Test initialization
            await web_search_service.initialize()
            
            # Test basic search
            search_query = SearchQuery(
                query="AAPL stock market news",
                context=SearchContext.GENERAL,
                symbols=["AAPL"],
                max_results=3
            )
            
            results = await web_search_service.search(search_query)
            
            if not results:
                return {"success": False, "error": "No search results returned"}
            
            # Validate result structure
            for result in results:
                required_fields = ["title", "snippet", "url", "source", "relevance_score"]
                for field in required_fields:
                    if not hasattr(result, field):
                        return {"success": False, "error": f"Missing field: {field}"}
            
            return {
                "success": True,
                "results_count": len(results),
                "avg_relevance": sum(r.relevance_score for r in results) / len(results),
                "sources": list(set(r.source for r in results))
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_ai_engine_integration(self) -> Dict[str, Any]:
        """Test AI engine web search integration"""
        try:
            from atlas_ai_core import AtlasAIEngine
            
            ai_engine = AtlasAIEngine()
            await ai_engine.initialize()
            
            # Test web search enhancement
            test_message = "What's the latest news about AAPL earnings?"
            test_symbols = ["AAPL"]
            
            if hasattr(ai_engine.atlas_engine, '_enhance_response_with_web_search'):
                enhancement = await ai_engine.atlas_engine._enhance_response_with_web_search(
                    test_message, test_symbols
                )
                
                if enhancement.get("web_search_used"):
                    return {
                        "success": True,
                        "web_search_used": True,
                        "sources_found": enhancement.get("search_count", 0)
                    }
                else:
                    return {
                        "success": True,
                        "web_search_used": False,
                        "reason": "Web search not triggered or not available"
                    }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_market_analysis_integration(self) -> Dict[str, Any]:
        """Test market analysis engine web search integration"""
        try:
            from atlas_market_core import AtlasMarketEngine
            
            market_engine = AtlasMarketEngine()
            await market_engine.initialize()
            
            # Test web search enhancement
            if hasattr(market_engine, 'enhance_market_analysis_with_web_search'):
                enhancement = await market_engine.enhance_market_analysis_with_web_search(
                    ["AAPL", "MSFT"]
                )
                
                return {
                    "success": True,
                    "web_search_used": enhancement.get("web_search_used", False),
                    "symbols_analyzed": enhancement.get("symbols_analyzed", 0),
                    "total_sources": enhancement.get("total_sources_found", 0)
                }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_risk_management_integration(self) -> Dict[str, Any]:
        """Test risk management engine web search integration"""
        try:
            from atlas_risk_core import AtlasRiskEngine
            
            risk_engine = AtlasRiskEngine()
            await risk_engine.initialize()
            
            # Test web search enhancement
            if hasattr(risk_engine, 'enhance_risk_assessment_with_web_search'):
                enhancement = await risk_engine.enhance_risk_assessment_with_web_search(
                    ["AAPL", "MSFT"]
                )
                
                return {
                    "success": True,
                    "web_search_used": enhancement.get("web_search_used", False),
                    "symbols_analyzed": enhancement.get("symbols_analyzed", 0),
                    "risk_intelligence": bool(enhancement.get("risk_intelligence"))
                }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_trading_engine_integration(self) -> Dict[str, Any]:
        """Test trading engine web search integration"""
        try:
            from atlas_trading_core import AtlasTradingEngine
            
            trading_engine = AtlasTradingEngine()
            await trading_engine.initialize()
            
            # Test web search enhancement
            if hasattr(trading_engine, 'enhance_trading_decision_with_web_search'):
                enhancement = await trading_engine.enhance_trading_decision_with_web_search("AAPL")
                
                return {
                    "success": True,
                    "web_search_used": enhancement.get("web_search_used", False),
                    "trading_intelligence": bool(enhancement.get("trading_intelligence")),
                    "recommendation": enhancement.get("trading_recommendation", "None")
                }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_education_system_integration(self) -> Dict[str, Any]:
        """Test education system web search integration"""
        try:
            from atlas_education import AtlasEducationEngine
            
            education_engine = AtlasEducationEngine()
            await education_engine.initialize()
            
            # Test enhanced search
            results = await education_engine.search_educational_content("options trading strategies")
            
            # Check if web results are included
            web_results = [r for r in results if r.get("source") == "web"]
            
            return {
                "success": True,
                "total_results": len(results),
                "web_enhanced_results": len(web_results),
                "web_enhancement_available": len(web_results) > 0
            }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_lee_method_integration(self) -> Dict[str, Any]:
        """Test Lee Method scanner web search integration"""
        try:
            from atlas_lee_method import LeeMethodScanner
            
            scanner = LeeMethodScanner()
            
            # Test web search enhancement
            if hasattr(scanner, 'enhance_signal_with_market_context'):
                test_signal = {"confidence": 0.7, "symbol": "AAPL"}
                enhancement = await scanner.enhance_signal_with_market_context("AAPL", test_signal)
                
                return {
                    "success": True,
                    "web_enhanced": enhancement.get("web_enhanced", False),
                    "market_context": bool(enhancement.get("market_context")),
                    "enhanced_confidence": enhancement.get("enhanced_confidence", 0.0)
                }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_portfolio_analysis_integration(self) -> Dict[str, Any]:
        """Test portfolio analysis web search integration"""
        try:
            from atlas_orchestrator import AtlasOrchestrator
            
            orchestrator = AtlasOrchestrator()
            await orchestrator.initialize()
            
            # Test enhanced portfolio summary
            if hasattr(orchestrator, '_enhance_portfolio_with_web_search'):
                enhancement = await orchestrator._enhance_portfolio_with_web_search(["AAPL", "MSFT"])
                
                return {
                    "success": True,
                    "web_enhanced": enhancement.get("web_enhanced", False),
                    "symbols_analyzed": enhancement.get("symbols_analyzed", 0),
                    "intelligence_available": bool(enhancement.get("intelligence"))
                }
            else:
                return {"success": False, "error": "Web search enhancement method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_news_insights_integration(self) -> Dict[str, Any]:
        """Test news insights engine web search integration"""
        try:
            from atlas_news_insights_engine import AtlasNewsInsightsEngine
            
            news_engine = AtlasNewsInsightsEngine()
            await news_engine.initialize()
            
            # Test gap detection
            if hasattr(news_engine, 'detect_information_gaps'):
                gap_analysis = news_engine.detect_information_gaps([], ["AAPL"])
                
                return {
                    "success": True,
                    "gap_detection_available": True,
                    "should_trigger_search": gap_analysis.get("should_trigger_web_search", False),
                    "gaps_detected": gap_analysis.get("gap_count", 0)
                }
            else:
                return {"success": False, "error": "Gap detection method not found"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_gap_detection(self) -> Dict[str, Any]:
        """Test gap detection logic"""
        try:
            from atlas_news_insights_engine import AtlasNewsInsightsEngine, NewsArticle, NewsCategory
            from datetime import datetime
            
            news_engine = AtlasNewsInsightsEngine()
            
            # Create test articles with low confidence
            test_articles = [
                NewsArticle(
                    id="test1",
                    title="Test Article 1",
                    content="Test content",
                    source="test",
                    url="http://test.com/1",
                    published_at=datetime.now(),
                    symbols=["AAPL"],
                    category=NewsCategory.MARKET_NEWS,
                    sentiment_score=0.5,
                    confidence=0.3,  # Low confidence
                    market_impact_score=0.5,
                    source_reliability=0.8,
                    metadata={}
                )
            ]
            
            gap_analysis = news_engine.detect_information_gaps(test_articles, ["AAPL", "MSFT"])
            
            return {
                "success": True,
                "gaps_detected": gap_analysis.get("gap_count", 0) > 0,
                "should_trigger_search": gap_analysis.get("should_trigger_web_search", False),
                "gap_types": gap_analysis.get("gaps_detected", [])
            }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_performance_benchmarks(self) -> Dict[str, Any]:
        """Test performance benchmarks"""
        try:
            from atlas_web_search_service import web_search_service, SearchQuery, SearchContext
            import time
            
            if not web_search_service.is_available():
                return {"success": False, "error": "Web search service not available"}
            
            # Performance test
            start_time = time.time()
            
            search_query = SearchQuery(
                query="AAPL market analysis",
                context=SearchContext.MARKET_ANALYSIS,
                symbols=["AAPL"],
                max_results=5
            )
            
            results = await web_search_service.search(search_query)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Calculate confidence metrics
            if results:
                avg_relevance = sum(r.relevance_score for r in results) / len(results)
                confidence_target_met = avg_relevance >= 8.0  # Target >80% confidence (8.0/10.0)
            else:
                avg_relevance = 0.0
                confidence_target_met = False
            
            return {
                "success": True,
                "response_time_seconds": response_time,
                "results_count": len(results),
                "average_relevance_score": avg_relevance,
                "confidence_target_met": confidence_target_met,
                "performance_acceptable": response_time < 10.0 and len(results) > 0
            }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def generate_test_report(self):
        """Generate comprehensive test report"""
        report_filename = f"web_search_integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        logger.info(f"📊 Test report saved to: {report_filename}")
        
        # Print summary
        logger.info("=" * 60)
        logger.info("🎯 A.T.L.A.S. WEB SEARCH INTEGRATION TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Tests Run: {self.test_results['tests_run']}")
        logger.info(f"Tests Passed: {self.test_results['tests_passed']}")
        logger.info(f"Tests Failed: {self.test_results['tests_failed']}")
        logger.info(f"Success Rate: {self.test_results.get('success_rate', 0):.1%}")
        logger.info(f"Overall Success: {'✅ PASS' if self.test_results['overall_success'] else '❌ FAIL'}")
        logger.info("=" * 60)

async def main():
    """Run the comprehensive web search integration test"""
    test_suite = WebSearchIntegrationTest()
    results = await test_suite.run_all_tests()
    
    if results["overall_success"]:
        logger.info("🎉 All web search integration tests completed successfully!")
        return 0
    else:
        logger.error("💥 Some web search integration tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
