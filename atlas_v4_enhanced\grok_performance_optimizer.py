"""
A.T.L.A.S. Grok Performance Optimization Module
Advanced caching, performance monitoring, and optimization strategies
for production deployment of enhanced Grok integration.
"""

import asyncio
import time
import logging
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor

# Core imports
from atlas_grok_integration import (
    GrokRequest, GrokResponse, GrokAnalysisResult,
    AtlasGrokIntegrationEngine, GrokAPIClient
)

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    request_count: int = 0
    total_response_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    error_count: int = 0
    rate_limit_hits: int = 0
    avg_response_time: float = 0.0
    cache_hit_rate: float = 0.0
    success_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    created_at: datetime
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)
    ttl_seconds: int = 300  # 5 minutes default
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl_seconds)
    
    def access(self):
        """Mark cache entry as accessed"""
        self.access_count += 1
        self.last_accessed = datetime.now()

class IntelligentCache:
    """Intelligent caching system with TTL, LRU, and smart invalidation"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order = deque()
        self.lock = threading.RLock()
        
        # Performance tracking
        self.hits = 0
        self.misses = 0
        
    def _generate_key(self, request: GrokRequest) -> str:
        """Generate cache key from request"""
        key_data = {
            'task_type': request.task_type.value,
            'capability': request.capability.value,
            'prompt_hash': hashlib.md5(request.prompt.encode()).hexdigest(),
            'temperature': request.temperature,
            'context_hash': hashlib.md5(str(request.context).encode()).hexdigest() if request.context else None,
            'search_params_hash': hashlib.md5(str(request.search_parameters).encode()).hexdigest() if request.search_parameters else None
        }
        return hashlib.sha256(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
    
    def get(self, request: GrokRequest) -> Optional[GrokResponse]:
        """Get cached response"""
        key = self._generate_key(request)
        
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                # Check if expired
                if entry.is_expired():
                    del self.cache[key]
                    if key in self.access_order:
                        self.access_order.remove(key)
                    self.misses += 1
                    return None
                
                # Update access info
                entry.access()
                
                # Move to end of access order (most recently used)
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
                
                self.hits += 1
                logger.debug(f"Cache hit for key: {key[:16]}...")
                return entry.data
            
            self.misses += 1
            return None
    
    def put(self, request: GrokRequest, response: GrokResponse, ttl: Optional[int] = None):
        """Store response in cache"""
        key = self._generate_key(request)
        ttl = ttl or self.default_ttl
        
        with self.lock:
            # Remove oldest entries if at capacity
            while len(self.cache) >= self.max_size and self.access_order:
                oldest_key = self.access_order.popleft()
                if oldest_key in self.cache:
                    del self.cache[oldest_key]
            
            # Store new entry
            entry = CacheEntry(
                data=response,
                created_at=datetime.now(),
                ttl_seconds=ttl
            )
            
            self.cache[key] = entry
            self.access_order.append(key)
            
            logger.debug(f"Cached response for key: {key[:16]}...")
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching pattern"""
        with self.lock:
            keys_to_remove = []
            for key in self.cache.keys():
                if pattern in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.cache[key]
                if key in self.access_order:
                    self.access_order.remove(key)
            
            logger.info(f"Invalidated {len(keys_to_remove)} cache entries matching pattern: {pattern}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0.0
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'utilization': len(self.cache) / self.max_size
        }

class RequestBatcher:
    """Batch similar requests for efficient processing"""
    
    def __init__(self, batch_size: int = 5, batch_timeout: float = 2.0):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests: Dict[str, List[Tuple[GrokRequest, asyncio.Future]]] = defaultdict(list)
        self.batch_timers: Dict[str, asyncio.Task] = {}
        self.lock = asyncio.Lock()
    
    def _get_batch_key(self, request: GrokRequest) -> str:
        """Generate batch key for similar requests"""
        return f"{request.task_type.value}_{request.capability.value}_{request.temperature}"
    
    async def add_request(self, request: GrokRequest, client: GrokAPIClient) -> GrokResponse:
        """Add request to batch or process immediately"""
        batch_key = self._get_batch_key(request)
        future = asyncio.Future()
        
        async with self.lock:
            self.pending_requests[batch_key].append((request, future))
            
            # If batch is full, process immediately
            if len(self.pending_requests[batch_key]) >= self.batch_size:
                await self._process_batch(batch_key, client)
            else:
                # Set timer for batch timeout
                if batch_key not in self.batch_timers:
                    self.batch_timers[batch_key] = asyncio.create_task(
                        self._batch_timeout(batch_key, client)
                    )
        
        return await future
    
    async def _batch_timeout(self, batch_key: str, client: GrokAPIClient):
        """Process batch after timeout"""
        await asyncio.sleep(self.batch_timeout)
        async with self.lock:
            if batch_key in self.pending_requests and self.pending_requests[batch_key]:
                await self._process_batch(batch_key, client)
    
    async def _process_batch(self, batch_key: str, client: GrokAPIClient):
        """Process a batch of requests"""
        if batch_key not in self.pending_requests:
            return
        
        batch = self.pending_requests[batch_key]
        del self.pending_requests[batch_key]
        
        # Cancel timer if exists
        if batch_key in self.batch_timers:
            self.batch_timers[batch_key].cancel()
            del self.batch_timers[batch_key]
        
        # Process requests concurrently
        tasks = []
        for request, future in batch:
            task = asyncio.create_task(self._process_single_request(request, client, future))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_single_request(self, request: GrokRequest, client: GrokAPIClient, future: asyncio.Future):
        """Process single request and set future result"""
        try:
            response = await client.make_request(request)
            future.set_result(response)
        except Exception as e:
            future.set_exception(e)

class PerformanceMonitor:
    """Real-time performance monitoring and alerting"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.response_times = deque(maxlen=window_size)
        self.error_rates = deque(maxlen=window_size)
        self.cache_hit_rates = deque(maxlen=window_size)
        self.metrics = PerformanceMetrics()
        self.lock = threading.Lock()
    
    def record_request(self, response_time: float, success: bool, cache_hit: bool):
        """Record request metrics"""
        with self.lock:
            self.response_times.append(response_time)
            self.error_rates.append(0 if success else 1)
            self.cache_hit_rates.append(1 if cache_hit else 0)
            
            # Update metrics
            self.metrics.request_count += 1
            self.metrics.total_response_time += response_time
            
            if cache_hit:
                self.metrics.cache_hits += 1
            else:
                self.metrics.cache_misses += 1
            
            if not success:
                self.metrics.error_count += 1
            
            # Calculate averages
            self.metrics.avg_response_time = sum(self.response_times) / len(self.response_times)
            self.metrics.cache_hit_rate = sum(self.cache_hit_rates) / len(self.cache_hit_rates)
            self.metrics.success_rate = 1.0 - (sum(self.error_rates) / len(self.error_rates))
            self.metrics.last_updated = datetime.now()
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        with self.lock:
            return self.metrics
    
    def check_performance_alerts(self) -> List[str]:
        """Check for performance issues and return alerts"""
        alerts = []
        
        with self.lock:
            # High response time alert
            if self.metrics.avg_response_time > 5.0:
                alerts.append(f"High response time: {self.metrics.avg_response_time:.2f}s")
            
            # Low cache hit rate alert
            if self.metrics.cache_hit_rate < 0.3:
                alerts.append(f"Low cache hit rate: {self.metrics.cache_hit_rate:.2%}")
            
            # High error rate alert
            if self.metrics.success_rate < 0.9:
                alerts.append(f"High error rate: {(1-self.metrics.success_rate):.2%}")
        
        return alerts

class OptimizedGrokClient(GrokAPIClient):
    """Performance-optimized Grok API client"""
    
    def __init__(self):
        super().__init__()
        self.cache = IntelligentCache(max_size=2000, default_ttl=300)
        self.batcher = RequestBatcher(batch_size=3, batch_timeout=1.5)
        self.monitor = PerformanceMonitor(window_size=200)
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    async def make_request(self, request: GrokRequest) -> GrokResponse:
        """Optimized request with caching and batching"""
        start_time = time.time()
        cache_hit = False
        
        try:
            # Check cache first
            cached_response = self.cache.get(request)
            if cached_response:
                cache_hit = True
                response_time = time.time() - start_time
                self.monitor.record_request(response_time, True, cache_hit)
                logger.debug("Returning cached response")
                return cached_response
            
            # Use batching for similar requests
            if self._should_batch(request):
                response = await self.batcher.add_request(request, self)
            else:
                response = await super().make_request(request)
            
            # Cache successful responses
            if response.success and self._should_cache(request, response):
                ttl = self._calculate_cache_ttl(request, response)
                self.cache.put(request, response, ttl)
            
            # Record metrics
            response_time = time.time() - start_time
            self.monitor.record_request(response_time, response.success, cache_hit)
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            self.monitor.record_request(response_time, False, cache_hit)
            raise e
    
    def _should_batch(self, request: GrokRequest) -> bool:
        """Determine if request should be batched"""
        # Don't batch real-time search requests or image analysis
        if (request.capability.value in ['real_time_search', 'vision'] or
            request.search_parameters is not None or
            request.image_data is not None):
            return False
        return True
    
    def _should_cache(self, request: GrokRequest, response: GrokResponse) -> bool:
        """Determine if response should be cached"""
        # Don't cache real-time data or failed responses
        if (not response.success or
            request.capability.value == 'real_time_search' or
            request.search_parameters is not None):
            return False
        return True
    
    def _calculate_cache_ttl(self, request: GrokRequest, response: GrokResponse) -> int:
        """Calculate appropriate cache TTL based on request type"""
        # Longer TTL for stable analysis types
        if request.task_type.value in ['logical_reasoning', 'pattern_detection']:
            return 600  # 10 minutes
        elif request.capability.value == 'vision':
            return 1800  # 30 minutes for image analysis
        else:
            return 300  # 5 minutes default
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        cache_stats = self.cache.get_stats()
        metrics = self.monitor.get_current_metrics()
        alerts = self.monitor.check_performance_alerts()
        
        return {
            'cache': cache_stats,
            'performance': {
                'avg_response_time': metrics.avg_response_time,
                'success_rate': metrics.success_rate,
                'total_requests': metrics.request_count,
                'error_count': metrics.error_count
            },
            'alerts': alerts,
            'last_updated': metrics.last_updated.isoformat()
        }
