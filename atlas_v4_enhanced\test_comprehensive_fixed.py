#!/usr/bin/env python3
"""
Comprehensive test for A.T.L.A.S. Multi-Agent System with proper cleanup
"""

import asyncio
import logging
import sys
import signal
from atlas_multi_agent_orchestrator import (
    AtlasMultiAgentOrchestrator, OrchestrationRequest, 
    IntentType, OrchestrationMode, TaskPriority
)
from atlas_session_manager import cleanup_sessions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveTest:
    """Comprehensive test suite with proper cleanup"""
    
    def __init__(self):
        self.orchestrator = None
        self.cleanup_done = False
    
    async def setup(self):
        """Setup test environment"""
        print("🚀 Setting up A.T.L.A.S. Multi-Agent System...")
        
        self.orchestrator = AtlasMultiAgentOrchestrator()
        success = await self.orchestrator.initialize()
        
        if not success:
            raise RuntimeError("Failed to initialize orchestrator")
        
        print("✅ Setup completed successfully")
        return True
    
    async def test_system_status(self):
        """Test system status and agent registration"""
        print("\n📊 Testing System Status...")
        
        status = self.orchestrator.get_orchestrator_status()
        print(f"   System Status: {status['status']}")
        print(f"   Total Agents: {status['total_agents']}")
        print(f"   Active Agents: {status['active_agents']}")
        
        # Verify all 6 agents are registered
        expected_agents = 6
        if status['total_agents'] != expected_agents:
            print(f"❌ Expected {expected_agents} agents, got {status['total_agents']}")
            return False
        
        print("✅ System status test passed")
        return True
    
    async def test_orchestration_request(self):
        """Test orchestration request processing"""
        print("\n🔍 Testing Orchestration Request...")
        
        request = OrchestrationRequest(
            request_id="test_comprehensive_001",
            intent=IntentType.DATA_ANALYSIS,
            symbol="AAPL",
            input_data={
                "test_mode": True,
                "task_type": "validation"  # Use supported task type
            },
            orchestration_mode=OrchestrationMode.PARALLEL,
            priority=TaskPriority.MEDIUM,
            timeout_seconds=30
        )
        
        result = await self.orchestrator.process_request(request)
        
        if result and result.success:
            print(f"✅ Request processed successfully!")
            print(f"   Confidence Score: {result.confidence_score:.3f}")
            print(f"   Processing Time: {result.processing_time:.2f}s")
            print(f"   Agents Involved: {len(result.agent_results)}")
            return True
        else:
            print("❌ Request processing failed")
            if result:
                error_msg = getattr(result, 'error_message', getattr(result, 'error', 'Unknown error'))
                print(f"   Error: {error_msg}")
            return False
    
    async def test_individual_agents(self):
        """Test individual agent functionality"""
        print("\n🤖 Testing Individual Agents...")
        
        agent_tests = []
        for role, agent in self.orchestrator.agents.items():
            print(f"\n   🔍 Testing {role.value} agent...")
            
            # Get agent status
            status = agent.get_status()
            print(f"      Status: {status['status']}")
            print(f"      Tasks Completed: {status['metrics']['tasks_completed']}")
            
            if status['status'] == 'active':
                print(f"      ✅ {role.value} agent is functional")
                agent_tests.append(True)
            else:
                print(f"      ❌ {role.value} agent is not active")
                agent_tests.append(False)
        
        success_rate = sum(agent_tests) / len(agent_tests) * 100
        print(f"\n   Agent Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 100:
            print("✅ All individual agent tests passed")
            return True
        else:
            print("❌ Some individual agent tests failed")
            return False
    
    async def test_error_handling(self):
        """Test error handling capabilities"""
        print("\n⚠️ Testing Error Handling...")
        
        # Test with invalid symbol
        request = OrchestrationRequest(
            request_id="test_error_001",
            intent=IntentType.DATA_ANALYSIS,
            symbol="INVALID_SYMBOL_12345",
            input_data={"test_mode": True},
            orchestration_mode=OrchestrationMode.SEQUENTIAL,
            priority=TaskPriority.LOW,
            timeout_seconds=15
        )
        
        result = await self.orchestrator.process_request(request)
        
        # Error handling should still return a result
        if result:
            print("✅ Error handling test passed - system handled invalid input gracefully")
            return True
        else:
            print("❌ Error handling test failed - system crashed on invalid input")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.cleanup_done:
            return
        
        print("\n🧹 Cleaning up resources...")
        
        try:
            # Close all aiohttp sessions
            await cleanup_sessions()
            print("   ✅ Sessions cleaned up")
            
            # Shutdown orchestrator if it exists
            if self.orchestrator:
                # Add any orchestrator cleanup here if needed
                pass
            
            self.cleanup_done = True
            print("✅ Cleanup completed successfully")
            
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
    
    async def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("🚀 A.T.L.A.S. Multi-Agent System Comprehensive Tests")
        print("=" * 60)
        
        test_results = []
        
        try:
            # Setup
            setup_result = await self.setup()
            test_results.append(("Setup", setup_result))
            
            if not setup_result:
                print("❌ Setup failed, aborting tests")
                return False
            
            # Run tests
            tests = [
                ("System Status", self.test_system_status),
                ("Orchestration Request", self.test_orchestration_request),
                ("Individual Agents", self.test_individual_agents),
                ("Error Handling", self.test_error_handling),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = await test_func()
                    test_results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} test failed with exception: {e}")
                    test_results.append((test_name, False))
            
        finally:
            # Always cleanup
            await self.cleanup()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        
        success_rate = passed / total * 100 if total > 0 else 0
        print(f"\nOverall Success Rate: {success_rate:.1f}% ({passed}/{total})")
        
        if success_rate >= 80:
            print("🎉 COMPREHENSIVE TESTS PASSED!")
            return True
        else:
            print("❌ COMPREHENSIVE TESTS FAILED")
            return False

async def main():
    """Main test runner"""
    test_suite = ComprehensiveTest()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        print(f"\n⚠️ Received signal {signum}, cleaning up...")
        asyncio.create_task(test_suite.cleanup())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        success = await test_suite.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        await test_suite.cleanup()
        return 1
    except Exception as e:
        print(f"\n❌ Test suite failed with exception: {e}")
        await test_suite.cleanup()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
