#!/usr/bin/env python3
"""
A.T.L.A.S. Performance Test Runner
Simple script to execute performance validation tests
"""

import asyncio
import logging
import sys
import argparse
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import performance validation
from performance_validation import PerformanceValidator, run_performance_validation
from atlas_multi_agent_orchestrator import AtlasMultiAgentOrchestrator

async def run_quick_test():
    """Run a quick performance test"""
    logger.info("🚀 Running Quick Performance Test...")
    
    try:
        # Initialize orchestrator
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create validator
        validator = PerformanceValidator(orchestrator)
        
        # Run basic performance test
        logger.info("📊 Running basic performance tests...")
        perf_results = await validator._run_performance_tests()
        
        # Print results
        print("\n" + "="*60)
        print("🎯 QUICK PERFORMANCE TEST RESULTS")
        print("="*60)
        
        for test_name, result in perf_results.items():
            if "metrics" in result:
                metrics = result["metrics"]
                print(f"\n{test_name.upper()}:")
                print(f"  Success Rate: {metrics.success_rate:.1%}")
                print(f"  Avg Response Time: {metrics.average_response_time:.2f}s")
                print(f"  P95 Response Time: {metrics.p95_response_time:.2f}s")
                print(f"  Requests/Second: {metrics.requests_per_second:.1f}")
                
                # Check targets
                response_ok = metrics.p95_response_time <= 10.0
                success_ok = metrics.success_rate >= 0.95
                
                print(f"  Response Time Target: {'✅ PASSED' if response_ok else '❌ FAILED'}")
                print(f"  Success Rate Target: {'✅ PASSED' if success_ok else '❌ FAILED'}")
        
        print("="*60)
        
        return perf_results
        
    except Exception as e:
        logger.error(f"Quick test failed: {e}")
        return {"error": str(e)}

async def run_load_test():
    """Run load testing only"""
    logger.info("🔥 Running Load Test...")
    
    try:
        # Initialize orchestrator
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create validator
        validator = PerformanceValidator(orchestrator)
        
        # Run load tests
        logger.info("📈 Running load tests...")
        load_results = await validator._run_load_tests()
        
        # Print results
        print("\n" + "="*60)
        print("🔥 LOAD TEST RESULTS")
        print("="*60)
        
        for test_name, result in load_results.items():
            if "metrics" in result:
                config = result["config"]
                metrics = result["metrics"]
                
                print(f"\n{test_name.upper()}:")
                print(f"  Concurrent Users: {config['concurrent_users']}")
                print(f"  Requests per User: {config['requests_per_user']}")
                print(f"  Total Requests: {metrics.total_requests}")
                print(f"  Success Rate: {metrics.success_rate:.1%}")
                print(f"  Avg Response Time: {metrics.average_response_time:.2f}s")
                print(f"  P95 Response Time: {metrics.p95_response_time:.2f}s")
                print(f"  Requests/Second: {metrics.requests_per_second:.1f}")
                
                # Check targets
                response_ok = metrics.p95_response_time <= 10.0
                success_ok = metrics.success_rate >= 0.95
                throughput_ok = metrics.requests_per_second >= 10.0
                
                print(f"  Response Time: {'✅ PASSED' if response_ok else '❌ FAILED'}")
                print(f"  Success Rate: {'✅ PASSED' if success_ok else '❌ FAILED'}")
                print(f"  Throughput: {'✅ PASSED' if throughput_ok else '❌ FAILED'}")
        
        print("="*60)
        
        return load_results
        
    except Exception as e:
        logger.error(f"Load test failed: {e}")
        return {"error": str(e)}

async def run_backtest():
    """Run backtesting only"""
    logger.info("📈 Running Backtest...")
    
    try:
        # Initialize orchestrator
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create validator
        validator = PerformanceValidator(orchestrator)
        
        # Run backtests
        logger.info("🎯 Running backtests...")
        backtest_results = await validator._run_backtests()
        
        # Print results
        print("\n" + "="*60)
        print("📈 BACKTEST RESULTS")
        print("="*60)
        
        for symbol, result in backtest_results.items():
            if symbol != "overall" and "accuracy" in result:
                print(f"\n{symbol}:")
                print(f"  Accuracy: {result['accuracy']:.1%}")
                print(f"  Precision: {result['precision']:.1%}")
                print(f"  Recall: {result['recall']:.1%}")
                print(f"  F1 Score: {result['f1_score']:.3f}")
                print(f"  Sharpe Ratio: {result['sharpe_ratio']:.2f}")
                print(f"  Win Rate: {result['win_rate']:.1%}")
                print(f"  Accuracy Target: {'✅ PASSED' if result['passed_accuracy'] else '❌ FAILED'}")
        
        if "overall" in backtest_results:
            overall = backtest_results["overall"]
            print(f"\nOVERALL PERFORMANCE:")
            print(f"  Average Accuracy: {overall['accuracy']:.1%}")
            print(f"  Average Precision: {overall['precision']:.1%}")
            print(f"  Average F1 Score: {overall['f1_score']:.3f}")
            print(f"  Accuracy Target: {'✅ PASSED' if overall['passed_accuracy'] else '❌ FAILED'}")
        
        print("="*60)
        
        return backtest_results
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        return {"error": str(e)}

async def run_optimization():
    """Run optimization tests only"""
    logger.info("🤝 Running Optimization Tests...")
    
    try:
        # Initialize orchestrator
        orchestrator = AtlasMultiAgentOrchestrator()
        await orchestrator.initialize()
        
        # Create validator
        validator = PerformanceValidator(orchestrator)
        
        # Run optimization
        logger.info("⚡ Running agent collaboration optimization...")
        opt_results = await validator._optimize_agent_collaboration()
        
        # Print results
        print("\n" + "="*60)
        print("🤝 OPTIMIZATION RESULTS")
        print("="*60)
        
        print("\nORCHESTRATION MODES:")
        for mode, metrics in opt_results["orchestration_modes"].items():
            print(f"  {mode.upper()}:")
            print(f"    Avg Response Time: {metrics['average_response_time']:.2f}s")
            print(f"    Success Rate: {metrics['success_rate']:.1%}")
            print(f"    Requests/Second: {metrics['requests_per_second']:.1f}")
        
        print("\nCONFIDENCE THRESHOLDS:")
        for threshold, metrics in opt_results["confidence_thresholds"].items():
            print(f"  {threshold}:")
            print(f"    Success Rate: {metrics['success_rate']:.1%}")
            print(f"    Avg Response Time: {metrics['average_response_time']:.2f}s")
        
        print("\nRECOMMENDATIONS:")
        for i, rec in enumerate(opt_results["recommendations"], 1):
            print(f"  {i}. {rec}")
        
        print("="*60)
        
        return opt_results
        
    except Exception as e:
        logger.error(f"Optimization test failed: {e}")
        return {"error": str(e)}

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="A.T.L.A.S. Performance Test Runner")
    parser.add_argument(
        "test_type",
        choices=["quick", "load", "backtest", "optimization", "full"],
        help="Type of test to run"
    )
    parser.add_argument(
        "--output",
        "-o",
        help="Output file for results (JSON format)"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run the appropriate test
    if args.test_type == "quick":
        results = asyncio.run(run_quick_test())
    elif args.test_type == "load":
        results = asyncio.run(run_load_test())
    elif args.test_type == "backtest":
        results = asyncio.run(run_backtest())
    elif args.test_type == "optimization":
        results = asyncio.run(run_optimization())
    elif args.test_type == "full":
        results = asyncio.run(run_performance_validation())
    else:
        print(f"Unknown test type: {args.test_type}")
        sys.exit(1)
    
    # Save results if output file specified
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📄 Results saved to: {args.output}")
    
    # Exit with appropriate code
    if "error" in results:
        print(f"\n❌ Test failed: {results['error']}")
        sys.exit(1)
    else:
        print(f"\n✅ Test completed successfully!")
        sys.exit(0)

if __name__ == "__main__":
    main()
