import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

// Import components for performance testing
import AtlasChatInterface from '../../src/components/chat/AtlasChatInterface';
import AtlasChartAnalysisPanel from '../../src/components/charts/AtlasChartAnalysisPanel';
import AtlasStatusDashboard from '../../src/components/status/AtlasStatusDashboard';
import AtlasProgressIndicator from '../../src/components/progress/AtlasProgressIndicator';

// Mock theme
const mockTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: { main: '#00ff88' },
  },
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={mockTheme}>
    {children}
  </ThemeProvider>
);

// Performance measurement utilities
const measureRenderTime = async (component: React.ReactElement): Promise<number> => {
  const startTime = performance.now();
  render(<TestWrapper>{component}</TestWrapper>);
  const endTime = performance.now();
  return endTime - startTime;
};

const measureInteractionTime = async (
  component: React.ReactElement,
  interaction: () => void
): Promise<number> => {
  render(<TestWrapper>{component}</TestWrapper>);
  
  const startTime = performance.now();
  interaction();
  await waitFor(() => {}, { timeout: 100 });
  const endTime = performance.now();
  
  return endTime - startTime;
};

describe('A.T.L.A.S. Performance Tests', () => {
  // Test 1: Component Render Performance
  describe('Render Performance', () => {
    test('chat interface renders within performance threshold', async () => {
      const renderTime = await measureRenderTime(<AtlasChatInterface />);
      
      // Should render within 100ms for good UX
      expect(renderTime).toBeLessThan(100);
      console.log(`Chat Interface render time: ${renderTime.toFixed(2)}ms`);
    });

    test('chart analysis panel renders within performance threshold', async () => {
      const renderTime = await measureRenderTime(
        <AtlasChartAnalysisPanel symbol="AAPL" />
      );
      
      // Chart components may take longer due to canvas rendering
      expect(renderTime).toBeLessThan(200);
      console.log(`Chart Analysis Panel render time: ${renderTime.toFixed(2)}ms`);
    });

    test('status dashboard renders within performance threshold', async () => {
      const renderTime = await measureRenderTime(
        <AtlasStatusDashboard
          isVisible={true}
          sessionId="test-session"
          userId="test-user"
        />
      );
      
      expect(renderTime).toBeLessThan(150);
      console.log(`Status Dashboard render time: ${renderTime.toFixed(2)}ms`);
    });

    test('progress indicator renders within performance threshold', async () => {
      const mockStages = [
        {
          id: 'test1',
          name: 'Test Stage 1',
          description: 'Testing stage 1',
          status: 'completed' as const,
          progress: 100,
        },
        {
          id: 'test2',
          name: 'Test Stage 2',
          description: 'Testing stage 2',
          status: 'active' as const,
          progress: 50,
        },
      ];

      const renderTime = await measureRenderTime(
        <AtlasProgressIndicator
          stages={mockStages}
          overallProgress={75}
          title="Performance Test"
        />
      );
      
      expect(renderTime).toBeLessThan(50);
      console.log(`Progress Indicator render time: ${renderTime.toFixed(2)}ms`);
    });
  });

  // Test 2: Interaction Performance
  describe('Interaction Performance', () => {
    test('quick action buttons respond within threshold', async () => {
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      const quickActionButton = screen.getByText(/Analyze AAPL/i);
      
      const interactionTime = await measureInteractionTime(
        <AtlasChatInterface />,
        () => fireEvent.click(quickActionButton)
      );
      
      // Button interactions should be near-instantaneous
      expect(interactionTime).toBeLessThan(50);
      console.log(`Quick action interaction time: ${interactionTime.toFixed(2)}ms`);
    });

    test('tab switching performs within threshold', async () => {
      render(
        <TestWrapper>
          <AtlasChartAnalysisPanel symbol="AAPL" />
        </TestWrapper>
      );

      const technicalAnalysisTab = screen.getByText('Technical Analysis');
      
      const interactionTime = await measureInteractionTime(
        <AtlasChartAnalysisPanel symbol="AAPL" />,
        () => fireEvent.click(technicalAnalysisTab)
      );
      
      expect(interactionTime).toBeLessThan(100);
      console.log(`Tab switching time: ${interactionTime.toFixed(2)}ms`);
    });
  });

  // Test 3: Memory Usage Validation
  describe('Memory Usage', () => {
    test('components clean up properly on unmount', () => {
      const { unmount } = render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );

      // Measure memory before unmount
      const beforeUnmount = (performance as any).memory?.usedJSHeapSize || 0;
      
      unmount();
      
      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      // Memory should not continuously grow
      const afterUnmount = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Allow for some variance in memory measurement
      if (beforeUnmount > 0 && afterUnmount > 0) {
        const memoryGrowth = afterUnmount - beforeUnmount;
        expect(memoryGrowth).toBeLessThan(1000000); // Less than 1MB growth
        console.log(`Memory growth after unmount: ${memoryGrowth} bytes`);
      }
    });
  });

  // Test 4: Real-time Performance Requirements
  describe('Real-time Performance', () => {
    test('progress updates render smoothly', async () => {
      const mockStages = [
        {
          id: 'realtime-test',
          name: 'Real-time Test',
          description: 'Testing real-time updates',
          status: 'active' as const,
          progress: 0,
        },
      ];

      const { rerender } = render(
        <TestWrapper>
          <AtlasProgressIndicator
            stages={mockStages}
            overallProgress={0}
            title="Real-time Test"
          />
        </TestWrapper>
      );

      // Simulate rapid progress updates
      const updateTimes: number[] = [];
      
      for (let progress = 10; progress <= 100; progress += 10) {
        const startTime = performance.now();
        
        const updatedStages = [{
          ...mockStages[0],
          progress,
        }];
        
        rerender(
          <TestWrapper>
            <AtlasProgressIndicator
              stages={updatedStages}
              overallProgress={progress}
              title="Real-time Test"
            />
          </TestWrapper>
        );
        
        const endTime = performance.now();
        updateTimes.push(endTime - startTime);
      }

      // All updates should be fast for smooth animation
      const avgUpdateTime = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length;
      expect(avgUpdateTime).toBeLessThan(16); // 60fps = 16.67ms per frame
      
      console.log(`Average progress update time: ${avgUpdateTime.toFixed(2)}ms`);
    });

    test('terminal output handles high-frequency updates', async () => {
      const generateMessages = (count: number) => 
        Array.from({ length: count }, (_, i) => ({
          id: `msg-${i}`,
          timestamp: new Date(),
          level: 'info' as const,
          source: 'TEST',
          message: `Test message ${i}`,
        }));

      const { rerender } = render(
        <TestWrapper>
          <AtlasTerminalOutput
            messages={generateMessages(10)}
            title="Performance Test Terminal"
          />
        </TestWrapper>
      );

      // Test with increasing message counts
      const messageCounts = [50, 100, 200, 500];
      const renderTimes: number[] = [];

      for (const count of messageCounts) {
        const startTime = performance.now();
        
        rerender(
          <TestWrapper>
            <AtlasTerminalOutput
              messages={generateMessages(count)}
              title="Performance Test Terminal"
            />
          </TestWrapper>
        );
        
        const endTime = performance.now();
        renderTimes.push(endTime - startTime);
      }

      // Should handle large message volumes efficiently
      const maxRenderTime = Math.max(...renderTimes);
      expect(maxRenderTime).toBeLessThan(100);
      
      console.log(`Max terminal render time with 500 messages: ${maxRenderTime.toFixed(2)}ms`);
    });
  });

  // Test 5: Trading Performance Standards
  describe('Trading Performance Standards', () => {
    test('maintains 1-2 second alert requirement simulation', async () => {
      // Simulate the Lee Method scanner alert requirement
      const alertStartTime = performance.now();
      
      // Simulate signal detection and UI update
      render(
        <TestWrapper>
          <AtlasChatInterface />
        </TestWrapper>
      );
      
      // Simulate quick action for Lee Method signals
      const leeMethodButton = screen.getByText(/Lee Method signals/i);
      fireEvent.click(leeMethodButton);
      
      const alertEndTime = performance.now();
      const alertTime = alertEndTime - alertStartTime;
      
      // Should meet the 1-2 second alert requirement
      expect(alertTime).toBeLessThan(2000);
      console.log(`Simulated alert response time: ${alertTime.toFixed(2)}ms`);
    });

    test('chart analysis loads within acceptable timeframe', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <AtlasChartAnalysisPanel symbol="AAPL" />
        </TestWrapper>
      );
      
      // Wait for chart to be ready
      await waitFor(() => {
        expect(screen.getByText('Chart & Analysis')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Chart analysis should load quickly for trading decisions
      expect(loadTime).toBeLessThan(1000);
      console.log(`Chart analysis load time: ${loadTime.toFixed(2)}ms`);
    });
  });

  // Test 6: System Resource Usage
  describe('System Resource Usage', () => {
    test('multiple components render efficiently together', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <div>
            <AtlasChatInterface />
            <AtlasChartAnalysisPanel symbol="AAPL" />
            <AtlasStatusDashboard
              isVisible={true}
              sessionId="test-session"
              userId="test-user"
            />
          </div>
        </TestWrapper>
      );
      
      const endTime = performance.now();
      const totalRenderTime = endTime - startTime;
      
      // Multiple components should render efficiently
      expect(totalRenderTime).toBeLessThan(500);
      console.log(`Multiple components render time: ${totalRenderTime.toFixed(2)}ms`);
    });
  });
});
